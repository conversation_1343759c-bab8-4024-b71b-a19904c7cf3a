#!/bin/bash

echo "=== JWT Logout Fix Test ==="
echo ""

# Clean up previous cookies
rm -f cookies.txt

echo "1. Testing JWT login..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:5100/api/v1/auth/jwt_login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}' \
  -c cookies.txt)

echo "Login response: $LOGIN_RESPONSE"
ACCESS_TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
echo "Access token: ${ACCESS_TOKEN:0:20}..."

echo ""
echo "2. Checking cookies after login..."
echo "Cookies stored:"
cat cookies.txt | grep -E "(jwt_session_id|refresh_token)" || echo "No session cookies found"

echo ""
echo "3. Performing logout..."
LOGOUT_RESPONSE=$(curl -s -X POST http://localhost:5100/api/v1/auth/jwt_logout \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -c cookies.txt)

echo "Logout response: $LOGOUT_RESPONSE"

echo ""
echo "4. Checking cookies after logout..."
echo "Cookies remaining:"
cat cookies.txt | grep -E "(jwt_session_id|refresh_token)" || echo "No session cookies found (GOOD!)"

echo ""
echo "5. Testing authenticated endpoint without token (should fail)..."
API_TEST=$(curl -s -X GET http://localhost:5100/api/v1/user \
  -H "Content-Type: application/json" \
  -b cookies.txt)

echo "API response: $API_TEST"

if [[ $API_TEST == *"authentication token"* ]] || [[ $API_TEST == *"unauthorized"* ]]; then
  echo ""
  echo "✅ SUCCESS: Logout properly cleared session - API returns unauthorized"
else
  echo ""
  echo "❌ FAILURE: Session still active after logout - API returned user data"
fi

echo ""
echo "6. Testing Rails endpoint (should redirect to login)..."
RAILS_TEST=$(curl -s -L -o /dev/null -w "%{url_effective}" \
  -b cookies.txt \
  http://localhost:5100/cs)

echo "Final URL after redirect: $RAILS_TEST"

if [[ $RAILS_TEST == *"/login"* ]]; then
  echo "✅ SUCCESS: Properly redirected to login page"
else
  echo "❌ FAILURE: Did not redirect to login - may have auto-authenticated"
fi