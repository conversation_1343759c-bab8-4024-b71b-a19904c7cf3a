#\!/bin/bash

echo "=== JWT Registration Test Suite ==="
echo ""

# Test 1: Registration with valid data
echo "Test 1: Valid JWT Registration"
response=$(curl -s -X POST http://localhost:5100/api/v1/auth/jwt_register \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "user": {
      "email": "test.final.'$(date +%s)'@example.com",
      "password": "123456",
      "password_confirmation": "123456"
    }
  }')

if echo "$response"  < /dev/null |  grep -q '"success":true'; then
  echo "✅ PASS: Registration successful"
  access_token=$(echo "$response" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
  echo "✅ PASS: Access token received"
else
  echo "❌ FAIL: Registration failed"
  echo "Response: $response"
  exit 1
fi

# Test 2: Use access token to access protected endpoint
echo ""
echo "Test 2: Access Protected Endpoint with Registration Token"
user_response=$(curl -s -X GET http://localhost:5100/api/v1/user \
  -H "Accept: application/json" \
  -H "Authorization: Bearer $access_token")

if echo "$user_response" | grep -q '"email"'; then
  echo "✅ PASS: Protected endpoint accessible with registration token"
else
  echo "❌ FAIL: Cannot access protected endpoint"
  echo "Response: $user_response"
fi

# Test 3: Registration with duplicate email
echo ""
echo "Test 3: Duplicate Email Registration (Should Fail)"
duplicate_response=$(curl -s -X POST http://localhost:5100/api/v1/auth/jwt_register \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "user": {
      "email": "<EMAIL>",
      "password": "123456",
      "password_confirmation": "123456"
    }
  }')

if echo "$duplicate_response" | grep -q '"success":false'; then
  echo "✅ PASS: Duplicate registration correctly rejected"
else
  echo "❌ FAIL: Duplicate registration not handled properly"
fi

# Test 4: Registration with password mismatch
echo ""
echo "Test 4: Password Mismatch Registration (Should Fail)"
mismatch_response=$(curl -s -X POST http://localhost:5100/api/v1/auth/jwt_register \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "user": {
      "email": "test.mismatch.'$(date +%s)'@example.com",
      "password": "123456",
      "password_confirmation": "654321"
    }
  }')

if echo "$mismatch_response" | grep -q '"success":false'; then
  echo "✅ PASS: Password mismatch correctly rejected"
else
  echo "❌ FAIL: Password mismatch not handled properly"
fi

echo ""
echo "=== Test Summary ==="
echo "JWT Registration endpoint is functioning correctly\!"
