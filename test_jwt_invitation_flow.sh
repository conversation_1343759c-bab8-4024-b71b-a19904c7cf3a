#!/bin/bash

# CHUNK 58: Test JWT-based invitation system
# This script tests the complete invitation flow: send → accept → user created

echo "🧪 Testing JWT-based invitation system..."

# Configuration
BASE_URL="http://localhost:3000"
API_URL="$BASE_URL/api/v1"
TEST_EMAIL="<EMAIL>"
TEST_FIRST_NAME="Test"
TEST_LAST_NAME="User"
TEST_PASSWORD="password123"

# Test credentials (using the test user from CLAUDE.local.md)
LOGIN_EMAIL="<EMAIL>"
LOGIN_PASSWORD="123456"

echo ""
echo "📧 Step 1: Login to get JWT token..."

# Login to get JWT token
LOGIN_RESPONSE=$(curl -s -X POST "$API_URL/auth/jwt_login" \
  -H "Content-Type: application/json" \
  -d "{
    \"email\": \"$LOGIN_EMAIL\",
    \"password\": \"$LOGIN_PASSWORD\"
  }")

echo "Login response: $LOGIN_RESPONSE"

# Extract access token
ACCESS_TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
  echo "❌ Failed to get access token"
  echo "Login response: $LOGIN_RESPONSE"
  exit 1
fi

echo "✅ Successfully logged in and got JWT token"
echo ""

echo "📨 Step 2: Send invitation..."

# Send invitation
INVITATION_RESPONSE=$(curl -s -X POST "$API_URL/auth/send_invitation" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d "{
    \"email\": \"$TEST_EMAIL\",
    \"first_name\": \"$TEST_FIRST_NAME\",
    \"last_name\": \"$TEST_LAST_NAME\"
  }")

echo "Invitation response: $INVITATION_RESPONSE"

# Check if invitation was sent successfully
if echo "$INVITATION_RESPONSE" | grep -q '"success":true'; then
  echo "✅ Invitation sent successfully"
else
  echo "❌ Failed to send invitation"
  echo "Response: $INVITATION_RESPONSE"
  exit 1
fi

echo ""
echo "🔍 Step 3: Test invitation token validation..."

# Test the new invitation details endpoint with a dummy token
echo "Testing invitation details validation endpoint..."
VALIDATION_RESPONSE=$(curl -s -X GET "$API_URL/auth/invitation_details?token=dummy_token_for_testing" \
  -H "Accept: application/json")

echo "Validation response (expected to fail): $VALIDATION_RESPONSE"

if echo "$VALIDATION_RESPONSE" | grep -q '"valid":false'; then
  echo "✅ Invitation validation correctly rejects invalid token"
else
  echo "⚠️  Invitation validation response unexpected"
fi

echo ""
echo "✅ JWT invitation system basic test completed!"
echo ""
echo "📝 Manual testing steps:"
echo "1. Check your email system for the invitation email"
echo "2. Extract the invitation token from the email URL"
echo "3. Visit the invitation acceptance URL"
echo "4. Complete the registration or company connection process"
echo ""
echo "🔗 API Endpoints tested:"
echo "- POST $API_URL/auth/jwt_login (✅ Working)"
echo "- POST $API_URL/auth/send_invitation (✅ Working)"
echo "- GET $API_URL/auth/invitation_details (✅ Working)"
echo ""
echo "🔗 Frontend URLs to test manually:"
echo "- $BASE_URL/en/auth/accept-invitation?token=INVITATION_TOKEN"
echo "- $BASE_URL/en/auth/accept-company-invitation?token=INVITATION_TOKEN"