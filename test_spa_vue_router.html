<!DOCTYPE html>
<html>
<head>
    <title>Vue Router Test</title>
    <script>
        // Test Vue Router navigation
        function testRoute(path) {
            console.log('Testing route:', path);
            
            // Check if Vue Router is available
            if (window.router) {
                window.router.push(path)
                    .then(() => {
                        console.log('✓ Navigation successful:', path);
                        console.log('  Current route:', window.router.currentRoute.value);
                    })
                    .catch(err => {
                        console.error('✗ Navigation failed:', path, err);
                    });
            } else {
                console.error('Vue Router not available');
            }
        }
        
        // Run tests when page loads
        window.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                console.log('=== Vue Router Test ===');
                console.log('Router available:', !!window.router);
                
                if (window.router) {
                    console.log('Current path:', window.location.pathname);
                    console.log('Vue Router path:', window.router.currentRoute.value.path);
                    console.log('Routes registered:', window.router.getRoutes().map(r => r.path));
                }
            }, 1000);
        });
    </script>
</head>
<body>
    <h1>Vue Router Test Page</h1>
    <p>Open browser console to see test results</p>
    
    <button onclick="testRoute('/cs/dashboard')">Test Dashboard Route</button>
    <button onclick="testRoute('/cs/events')">Test Events Route</button>
    <button onclick="testRoute('/cs/invalid')">Test Invalid Route</button>
</body>
</html>