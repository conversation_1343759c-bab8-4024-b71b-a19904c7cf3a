<!DOCTYPE html>
<html>
<head>
    <title>Console Test</title>
</head>
<body>
    <h1>Console Logging Test</h1>
    <button onclick="testConsole()">Test Console</button>
    
    <script>
        console.log('Page loaded - this should appear in terminal');
        console.error('Test error message');
        console.warn('Test warning message');
        
        function testConsole() {
            console.log('Button clicked!');
            console.error('Test error from button');
        }
        
        // Test on load
        setTimeout(() => {
            console.log('Delayed log message');
        }, 1000);
    </script>
</body>
</html>