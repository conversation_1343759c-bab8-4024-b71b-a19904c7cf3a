// ABOUTME: Vite plugin to forward browser console logs via HMR WebSocket connection
// ABOUTME: Listens for 'console-forward' messages from client and displays them in terminal with color coding

export function consoleLoggerPlugin() {
  return {
    name: 'console-forward-plugin',
    configureServer(server) {
      // Listen for console messages via existing HMR WebSocket
      server.ws.on('console-forward', (data, client) => {
        const time = new Date().toLocaleTimeString();
        
        // Color coding for different log levels
        const levelColors = {
          log: '\x1b[37m',     // White
          info: '\x1b[36m',    // Cyan
          warn: '\x1b[33m',    // Yellow
          error: '\x1b[31m',   // Red
          debug: '\x1b[35m'    // Magenta
        };
        
        const reset = '\x1b[0m';
        const dim = '\x1b[2m';
        const bold = '\x1b[1m';
        const blue = '\x1b[34m';
        
        const color = levelColors[data.level] || levelColors.log;
        
        // Format the output to be readable in the terminal
        console.log(
          `${dim}${time}${reset} ${bold}${blue}[Browser]${reset} ${color}${data.level.toUpperCase()}${reset}`,
          ...data.args
        );
      });
    },
  };
}