#!/bin/bash
# ABOUTME: Test script for new user invitation acceptance flow
# ABOUTME: Verifies personal workspace creation and company context initialization

set -e

echo "=== Testing New User Invitation Acceptance Flow ==="
echo "This test verifies that new users get their personal workspace properly set up"
echo ""

# Configuration
API_BASE="http://************:5100/api/v1"
TEST_EMAIL="test_$(date +%s)@example.com"
TEST_PASSWORD="Test123456!"
INVITATION_TOKEN=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper function for API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local token=$4
    
    if [ -n "$token" ]; then
        curl -s -X "$method" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            -H "Authorization: Bearer $token" \
            ${data:+-d "$data"} \
            "$API_BASE$endpoint"
    else
        curl -s -X "$method" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            ${data:+-d "$data"} \
            "$API_BASE$endpoint"
    fi
}

echo "Step 1: Creating an invitation for a new user..."
# Note: This requires an existing owner user to create the invitation
# You'll need to manually create an invitation or use an existing one

echo -e "${YELLOW}Please create an invitation manually or provide an existing invitation token${NC}"
echo "You can create one by:"
echo "1. <NAME_EMAIL>"
echo "2. Go to Companies > Invite User"
echo "3. Enter email: $TEST_EMAIL"
echo ""
read -p "Enter the invitation token: " INVITATION_TOKEN

if [ -z "$INVITATION_TOKEN" ]; then
    echo -e "${RED}Error: No invitation token provided${NC}"
    exit 1
fi

echo ""
echo "Step 2: Validating invitation token..."
VALIDATION_RESPONSE=$(api_call GET "/invitations/$INVITATION_TOKEN" "")
echo "Response: $VALIDATION_RESPONSE" | jq '.' 2>/dev/null || echo "$VALIDATION_RESPONSE"

# Check if invitation is valid
if echo "$VALIDATION_RESPONSE" | grep -q '"valid":true'; then
    echo -e "${GREEN}✓ Invitation is valid${NC}"
else
    echo -e "${RED}✗ Invalid invitation${NC}"
    exit 1
fi

echo ""
echo "Step 3: Accepting invitation and setting password..."
ACCEPT_RESPONSE=$(api_call POST "/invitations/$INVITATION_TOKEN/accept" "{
    \"password\": \"$TEST_PASSWORD\",
    \"password_confirmation\": \"$TEST_PASSWORD\"
}")
echo "Response: $ACCEPT_RESPONSE" | jq '.' 2>/dev/null || echo "$ACCEPT_RESPONSE"

# Extract JWT token
JWT_TOKEN=$(echo "$ACCEPT_RESPONSE" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
COMPANY_ID=$(echo "$ACCEPT_RESPONSE" | grep -o '"company_id":[0-9]*' | cut -d':' -f2)

if [ -z "$JWT_TOKEN" ]; then
    echo -e "${RED}✗ Failed to get JWT token${NC}"
    exit 1
fi

echo -e "${GREEN}✓ User registered and logged in${NC}"
echo "JWT Token: ${JWT_TOKEN:0:20}..."
echo "Primary Company ID: $COMPANY_ID"

echo ""
echo "Step 4: Verifying user has a personal workspace..."
USER_RESPONSE=$(api_call GET "/user" "" "$JWT_TOKEN")
echo "User data: $USER_RESPONSE" | jq '.' 2>/dev/null || echo "$USER_RESPONSE"

# Check if user response contains company_id
if echo "$USER_RESPONSE" | grep -q '"company_id":'; then
    echo -e "${GREEN}✓ User has company context${NC}"
else
    echo -e "${RED}✗ User missing company context - this is the bug!${NC}"
fi

echo ""
echo "Step 5: Fetching user's companies..."
COMPANIES_RESPONSE=$(api_call GET "/companies" "" "$JWT_TOKEN")
echo "Companies: $COMPANIES_RESPONSE" | jq '.' 2>/dev/null || echo "$COMPANIES_RESPONSE"

# Check if user has at least one company (personal workspace)
if echo "$COMPANIES_RESPONSE" | grep -q '"company_user_roles":\['; then
    COMPANY_COUNT=$(echo "$COMPANIES_RESPONSE" | grep -o '"company":{' | wc -l)
    echo -e "${GREEN}✓ User has access to $COMPANY_COUNT company(s)${NC}"
    
    # Check for primary company
    if echo "$COMPANIES_RESPONSE" | grep -q '"is_primary":true'; then
        echo -e "${GREEN}✓ User has a primary company set${NC}"
    else
        echo -e "${RED}✗ No primary company found${NC}"
    fi
else
    echo -e "${RED}✗ User has no companies${NC}"
fi

echo ""
echo "Step 6: Checking company connections (pending invitations)..."
# This should work without X-Company-ID header now
CONNECTIONS_RESPONSE=$(api_call GET "/company_connections/fetch" "" "$JWT_TOKEN")
echo "Connections: $CONNECTIONS_RESPONSE" | jq '.' 2>/dev/null || echo "$CONNECTIONS_RESPONSE"

# The invitation should show as pending connection
if echo "$CONNECTIONS_RESPONSE" | grep -q '\['; then
    CONNECTION_COUNT=$(echo "$CONNECTIONS_RESPONSE" | grep -o '"id":' | wc -l)
    if [ "$CONNECTION_COUNT" -gt 0 ]; then
        echo -e "${GREEN}✓ User has $CONNECTION_COUNT pending connection(s)${NC}"
    else
        echo -e "${YELLOW}⚠ No pending connections (invitation might be already accepted)${NC}"
    fi
else
    echo -e "${RED}✗ Error fetching connections - likely requires X-Company-ID${NC}"
fi

echo ""
echo "=== Test Summary ==="
echo "Email: $TEST_EMAIL"
echo "Personal Workspace ID: $COMPANY_ID"

if [ -n "$COMPANY_ID" ] && [ "$COMPANY_ID" != "null" ]; then
    echo -e "${GREEN}✓ Test PASSED: New user has personal workspace and can access the app${NC}"
else
    echo -e "${RED}✗ Test FAILED: New user missing company context${NC}"
    echo "This indicates the bug is still present:"
    echo "- User was created but personal workspace not properly loaded"
    echo "- Frontend will show 'Please provide X-Company-ID header' error"
fi