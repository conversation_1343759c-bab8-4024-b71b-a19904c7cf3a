#!/bin/bash
# ABOUTME: Test script for TYM-115 login-logout loop fix
# ABOUTME: Verifies that company context is properly set after JWT login

set -e

echo "====================================================="
echo "TYM-115 Login-Logout Loop Fix Test"
echo "====================================================="

BASE_URL="http://************:5100"
API_BASE="$BASE_URL/api/v1"

# Test credentials
EMAIL="<EMAIL>"
PASSWORD="123456"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "\n${YELLOW}Step 1: Test JWT Login${NC}"
echo "Testing login with credentials..."

# Login and capture response
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/jwt_login" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -c /tmp/tym115_cookies.txt \
  -d "{\"email\": \"$EMAIL\", \"password\": \"$PASSWORD\"}")

echo "Login Response: $LOGIN_RESPONSE"

# Extract token and company_id
ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
COMPANY_ID=$(echo "$LOGIN_RESPONSE" | grep -o '"company_id":[^,}]*' | cut -d':' -f2)

if [ -z "$ACCESS_TOKEN" ]; then
  echo -e "${RED}✗ Login failed - no access token received${NC}"
  exit 1
fi

echo -e "${GREEN}✓ Login successful${NC}"
echo "  Access Token: ${ACCESS_TOKEN:0:20}..."
echo "  Company ID: $COMPANY_ID"

if [ -z "$COMPANY_ID" ] || [ "$COMPANY_ID" = "null" ]; then
  echo -e "${YELLOW}⚠ Warning: No company_id returned in login response${NC}"
else
  echo -e "${GREEN}✓ Company ID present in login response${NC}"
fi

echo -e "\n${YELLOW}Step 2: Test API calls with X-Company-ID header${NC}"

# Test without X-Company-ID header (should fail)
echo "Testing API call WITHOUT X-Company-ID header..."
RESPONSE_NO_HEADER=$(curl -s -w "\n%{http_code}" -X GET "$API_BASE/employees" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Accept: application/json")

HTTP_CODE_NO_HEADER=$(echo "$RESPONSE_NO_HEADER" | tail -n 1)
BODY_NO_HEADER=$(echo "$RESPONSE_NO_HEADER" | head -n -1)

echo "Response without header: HTTP $HTTP_CODE_NO_HEADER"
if [ "$HTTP_CODE_NO_HEADER" = "403" ] || [ "$HTTP_CODE_NO_HEADER" = "400" ]; then
  echo -e "${GREEN}✓ API correctly requires X-Company-ID header${NC}"
else
  echo -e "${RED}✗ Unexpected response code without X-Company-ID: $HTTP_CODE_NO_HEADER${NC}"
fi

# Test with X-Company-ID header (should succeed)
if [ ! -z "$COMPANY_ID" ] && [ "$COMPANY_ID" != "null" ]; then
  echo "Testing API call WITH X-Company-ID header..."
  RESPONSE_WITH_HEADER=$(curl -s -w "\n%{http_code}" -X GET "$API_BASE/employees" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "X-Company-ID: $COMPANY_ID" \
    -H "Accept: application/json")
  
  HTTP_CODE_WITH_HEADER=$(echo "$RESPONSE_WITH_HEADER" | tail -n 1)
  
  echo "Response with header: HTTP $HTTP_CODE_WITH_HEADER"
  if [ "$HTTP_CODE_WITH_HEADER" = "200" ]; then
    echo -e "${GREEN}✓ API call successful with X-Company-ID header${NC}"
  else
    echo -e "${RED}✗ API call failed even with X-Company-ID: $HTTP_CODE_WITH_HEADER${NC}"
  fi
fi

echo -e "\n${YELLOW}Step 3: Test companies endpoint${NC}"
COMPANIES_RESPONSE=$(curl -s -X GET "$API_BASE/companies" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Accept: application/json")

echo "Companies Response (truncated): ${COMPANIES_RESPONSE:0:200}..."

# Check for is_primary flag
if echo "$COMPANIES_RESPONSE" | grep -q '"is_primary":true'; then
  echo -e "${GREEN}✓ Primary company flag present in companies response${NC}"
else
  echo -e "${YELLOW}⚠ No primary company flag found in response${NC}"
fi

# Check for primary_company_id
if echo "$COMPANIES_RESPONSE" | grep -q '"primary_company_id":'; then
  PRIMARY_ID=$(echo "$COMPANIES_RESPONSE" | grep -o '"primary_company_id":[^,}]*' | cut -d':' -f2)
  echo -e "${GREEN}✓ Primary company ID included: $PRIMARY_ID${NC}"
else
  echo -e "${YELLOW}⚠ No primary_company_id in response${NC}"
fi

echo -e "\n${YELLOW}Step 4: Test token refresh${NC}"
REFRESH_RESPONSE=$(curl -s -X POST "$API_BASE/auth/refresh_token" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Accept: application/json" \
  -b /tmp/tym115_cookies.txt \
  -c /tmp/tym115_cookies.txt)

echo "Refresh Response (truncated): ${REFRESH_RESPONSE:0:200}..."

NEW_TOKEN=$(echo "$REFRESH_RESPONSE" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
REFRESH_COMPANY_ID=$(echo "$REFRESH_RESPONSE" | grep -o '"company_id":[^,}]*' | cut -d':' -f2)

if [ ! -z "$NEW_TOKEN" ]; then
  echo -e "${GREEN}✓ Token refresh successful${NC}"
  if [ ! -z "$REFRESH_COMPANY_ID" ] && [ "$REFRESH_COMPANY_ID" != "null" ]; then
    echo -e "${GREEN}✓ Company ID present in refresh response: $REFRESH_COMPANY_ID${NC}"
  else
    echo -e "${YELLOW}⚠ No company_id in refresh response${NC}"
  fi
else
  echo -e "${RED}✗ Token refresh failed${NC}"
fi

echo -e "\n${YELLOW}Step 5: Test session restore${NC}"
RESTORE_RESPONSE=$(curl -s -X POST "$API_BASE/auth/restore_session" \
  -H "Accept: application/json" \
  -b /tmp/tym115_cookies.txt)

echo "Restore Response (truncated): ${RESTORE_RESPONSE:0:200}..."

RESTORE_TOKEN=$(echo "$RESTORE_RESPONSE" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
RESTORE_COMPANY_ID=$(echo "$RESTORE_RESPONSE" | grep -o '"company_id":[^,}]*' | cut -d':' -f2)

if [ ! -z "$RESTORE_TOKEN" ]; then
  echo -e "${GREEN}✓ Session restore successful${NC}"
  if [ ! -z "$RESTORE_COMPANY_ID" ] && [ "$RESTORE_COMPANY_ID" != "null" ]; then
    echo -e "${GREEN}✓ Company ID present in restore response: $RESTORE_COMPANY_ID${NC}"
  else
    echo -e "${YELLOW}⚠ No company_id in restore response${NC}"
  fi
else
  echo -e "${YELLOW}⚠ Session restore failed (expected if Redis was cleared)${NC}"
fi

echo -e "\n${YELLOW}Step 6: Test logout${NC}"
LOGOUT_RESPONSE=$(curl -s -X POST "$API_BASE/auth/jwt_logout" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Accept: application/json" \
  -b /tmp/tym115_cookies.txt)

echo "Logout Response: $LOGOUT_RESPONSE"

if echo "$LOGOUT_RESPONSE" | grep -q '"success":true'; then
  echo -e "${GREEN}✓ Logout successful${NC}"
else
  echo -e "${RED}✗ Logout failed${NC}"
fi

# Cleanup
rm -f /tmp/tym115_cookies.txt

echo -e "\n====================================================="
echo -e "${GREEN}TYM-115 Test Complete${NC}"
echo "====================================================="
echo ""
echo "Summary:"
echo "- Login returns company_id: $([ ! -z "$COMPANY_ID" ] && [ "$COMPANY_ID" != "null" ] && echo "✓" || echo "✗")"
echo "- API requires X-Company-ID: $([ "$HTTP_CODE_NO_HEADER" = "403" ] || [ "$HTTP_CODE_NO_HEADER" = "400" ] && echo "✓" || echo "✗")"
echo "- API works with X-Company-ID: $([ "$HTTP_CODE_WITH_HEADER" = "200" ] && echo "✓" || echo "✗")"
echo "- Companies list includes primary: $(echo "$COMPANIES_RESPONSE" | grep -q '"is_primary":true' && echo "✓" || echo "✗")"
echo ""
echo "If all checks pass, the login-logout loop should be fixed!"