// ABOUTME: E2E test for TYM-106 company invitation acceptance UX improvements
// ABOUTME: Tests direct redirect to companies page with success message and highlighting

import { test, expect } from './fixtures/auth';

test.describe('TYM-106: Company Invitation Acceptance UX', () => {
  test('user is redirected to companies page after accepting invitation with success message and highlighting', async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // Navigate to company connections page
    await page.goto('/company_connections');
    
    // Wait for the component to load
    await expect(page.locator('[data-vue-component="company-connections"]')).toBeVisible();
    
    // Check if there are pending invitations
    const invitations = await page.locator('.connections-item').count();
    
    if (invitations === 0) {
      console.log('No pending invitations to test with');
      return;
    }
    
    // Get the company name from the first invitation for verification
    const companyName = await page.locator('.connections-item').first().locator('p.font-medium').textContent();
    
    // Accept the first invitation
    await page.locator('.connections-item').first().locator('button:has-text("Připojit se")').click();
    
    // Confirm the invitation acceptance
    page.on('dialog', dialog => dialog.accept());
    
    // Verify redirect to companies page
    await expect(page).toHaveURL(/\/cs\/companies/);
    
    // Verify success message is displayed
    await expect(page.locator('.success-message')).toBeVisible();
    
    // Verify success message contains the company name
    if (companyName) {
      await expect(page.locator('.success-message')).toContainText(companyName.trim());
    }
    
    // Verify success message has proper styling
    await expect(page.locator('.success-message')).toHaveClass(/bg-green-50/);
    await expect(page.locator('.success-message')).toHaveClass(/border-green-200/);
    
    // Check if the newly connected company is highlighted
    const highlightedCompany = page.locator('.company-highlighted');
    if (await highlightedCompany.count() > 0) {
      // Verify highlighting styles
      await expect(highlightedCompany.first()).toHaveCSS('border', /green/);
      await expect(highlightedCompany.first()).toHaveCSS('background-color', /rgb\(240, 253, 244\)/);
    }
    
    // Verify URL parameters are cleaned up (no success parameters should remain)
    const url = page.url();
    expect(url).not.toContain('success=connected');
    expect(url).not.toContain('highlight=');
    expect(url).not.toContain('company=');
  });

  test('success message auto-dismisses after 6 seconds', async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // Navigate directly to companies page with success parameters to test auto-dismiss
    await page.goto('/cs/companies?success=connected&company=Test+Company&highlight=123');
    
    // Wait for page to load first
    await expect(page.locator('h2')).toContainText('Pracovní prostory');
    
    // Wait for component to load and success message to appear
    await expect(page.locator('.success-message')).toBeVisible();
    
    // Wait for auto-dismiss (6 seconds)
    await page.waitForTimeout(6500);
    
    // Verify success message is no longer visible
    await expect(page.locator('.success-message')).not.toBeVisible();
  });

  test('companies page works normally without success parameters', async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // Navigate to companies page normally
    await page.goto('/cs/companies');
    
    // Verify page loads correctly
    await expect(page.locator('h2')).toContainText('Pracovní prostory');
    
    // Verify no success message is displayed
    await expect(page.locator('.success-message')).not.toBeVisible();
    
    // Verify no companies are highlighted
    await expect(page.locator('.company-highlighted')).not.toBeVisible();
    
    // Verify normal company grid is displayed
    await expect(page.locator('.grid')).toBeVisible();
  });

  test('success message is mobile responsive', async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Navigate with success parameters
    await page.goto('/cs/companies?success=connected&company=Test+Company&highlight=123');
    
    // Wait for page to load first
    await expect(page.locator('h2')).toContainText('Pracovní prostory');
    
    // Verify success message is visible on mobile
    await expect(page.locator('.success-message')).toBeVisible();
    
    // Verify success message is properly styled for mobile
    const successMessage = page.locator('.success-message');
    await expect(successMessage).toHaveCSS('padding', /16px/);
    
    // Verify the message content is readable on mobile
    await expect(successMessage.locator('h3')).toBeVisible();
    await expect(successMessage.locator('p')).toBeVisible();
  });
});