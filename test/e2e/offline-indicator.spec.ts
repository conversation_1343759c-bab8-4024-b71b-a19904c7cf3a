// ABOUTME: E2E tests for offline indicator functionality using Playwright
// ABOUTME: Tests real network status tracking and offline indicator display in the SPA

import { test, expect } from '@playwright/test';

test.describe('Offline Indicator', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the actual SPA - testing the real application
    await page.goto('/');
    
    // Wait for the SPA to load properly
    await page.waitForSelector('#app', { timeout: 10000 });
  });

  test('offline indicator appears when network goes offline', async ({ page }) => {
    // Test the real application - not mock HTML
    
    // Initially should be online (indicator should not be visible)
    const offlineIndicator = page.locator('[data-testid="offline-indicator"]');
    await expect(offlineIndicator).not.toBeVisible();
    
    // Simulate going offline
    await page.context().setOffline(true);
    
    // Trigger offline event by evaluating in the browser context
    await page.evaluate(() => {
      window.dispatchEvent(new Event('offline'));
    });
    
    // Wait for the offline indicator to appear
    await expect(offlineIndicator).toBeVisible({ timeout: 5000 });
    
    // Verify the offline message is displayed
    await expect(offlineIndicator.locator('.offline-message')).toContainText('Jste offline');
    
    // Verify retry button is present
    const retryButton = offlineIndicator.locator('[data-testid="retry-button"]');
    await expect(retryButton).toBeVisible();
    await expect(retryButton).toContainText('Zkusit znovu');
  });

  test('offline indicator disappears when network comes back online', async ({ page }) => {
    // Start offline
    await page.context().setOffline(true);
    await page.evaluate(() => {
      window.dispatchEvent(new Event('offline'));
    });
    
    const offlineIndicator = page.locator('[data-testid="offline-indicator"]');
    await expect(offlineIndicator).toBeVisible();
    
    // Go back online
    await page.context().setOffline(false);
    await page.evaluate(() => {
      window.dispatchEvent(new Event('online'));
    });
    
    // Offline indicator should disappear
    await expect(offlineIndicator).not.toBeVisible({ timeout: 5000 });
  });

  test('retry button works when clicked', async ({ page }) => {
    // Go offline
    await page.context().setOffline(true);
    await page.evaluate(() => {
      window.dispatchEvent(new Event('offline'));
    });
    
    const offlineIndicator = page.locator('[data-testid="offline-indicator"]');
    await expect(offlineIndicator).toBeVisible();
    
    const retryButton = offlineIndicator.locator('[data-testid="retry-button"]');
    
    // Click retry button
    await retryButton.click();
    
    // Button should show "Zkouším..." state
    await expect(retryButton).toContainText('Zkouším...');
    await expect(retryButton).toBeDisabled();
    
    // Go back online to simulate successful retry
    await page.context().setOffline(false);
    
    // Wait for retry to complete and indicator to disappear
    await expect(offlineIndicator).not.toBeVisible({ timeout: 10000 });
  });

  test('close button hides the indicator manually', async ({ page }) => {
    // Go offline
    await page.context().setOffline(true);
    await page.evaluate(() => {
      window.dispatchEvent(new Event('offline'));
    });
    
    const offlineIndicator = page.locator('[data-testid="offline-indicator"]');
    await expect(offlineIndicator).toBeVisible();
    
    // Click close button
    const closeButton = offlineIndicator.locator('[data-testid="close-button"]');
    await expect(closeButton).toBeVisible();
    await closeButton.click();
    
    // Indicator should be hidden
    await expect(offlineIndicator).not.toBeVisible();
  });

  test('offline indicator works across different pages in SPA', async ({ page }) => {
    // Navigate to a different SPA route (if authenticated)
    try {
      // Try to navigate to dashboard or another SPA route
      await page.goto('/dashboard');
      await page.waitForSelector('#app', { timeout: 5000 });
    } catch {
      // If not authenticated, stay on current page
      console.log('Not authenticated, testing on current page');
    }
    
    // Go offline
    await page.context().setOffline(true);
    await page.evaluate(() => {
      window.dispatchEvent(new Event('offline'));
    });
    
    // Offline indicator should still appear
    const offlineIndicator = page.locator('[data-testid="offline-indicator"]');
    await expect(offlineIndicator).toBeVisible();
    
    // Verify it's properly positioned at the top
    const boundingBox = await offlineIndicator.boundingBox();
    expect(boundingBox?.y).toBeLessThan(100); // Should be near the top
  });

  test('network state is properly tracked in Vuex store', async ({ page }) => {
    // Check initial online state in store
    let networkState = await page.evaluate(() => {
      return window.$store?.getters['networkStore/isOnline'];
    });
    expect(networkState).toBe(true);
    
    // Go offline
    await page.context().setOffline(true);
    await page.evaluate(() => {
      window.dispatchEvent(new Event('offline'));
    });
    
    // Check offline state in store
    networkState = await page.evaluate(() => {
      return window.$store?.getters['networkStore/isOnline'];
    });
    expect(networkState).toBe(false);
    
    // Check offline indicator state
    const showIndicator = await page.evaluate(() => {
      return window.$store?.getters['networkStore/showOfflineIndicator'];
    });
    expect(showIndicator).toBe(true);
  });
});