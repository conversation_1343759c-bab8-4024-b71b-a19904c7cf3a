require 'test_helper'

class SpaRoutesTest < ActionDispatch::IntegrationTest
  test "dashboard route responds successfully" do
    get '/cs/dashboard'
    assert_response :success
    assert_select '#app', 1, "SPA mount point should exist"
  end

  test "events route responds successfully" do
    get '/cs/events'
    assert_response :success
    assert_select '#app', 1, "SPA mount point should exist"
  end

  test "non-existent route returns 404" do
    get '/cs/nonexistent'
    assert_response :not_found
  end

  test "API routes are not affected" do
    get '/api/v1/subscription_status'
    # Should return 401 since we're not authenticated
    assert_response :unauthorized
  end
end