// ABOUTME: Unit tests for network detection utility
// ABOUTME: Tests network event handling and Vuex store integration

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { initNetworkDetection, cleanupNetworkDetection, retryConnection } from '../../../app/frontend/utils/networkDetection';

// Mock Vuex store
const mockStore = {
  dispatch: vi.fn()
};

// Mock window methods
const mockEventListeners: { [key: string]: EventListener[] } = {};

beforeEach(() => {
  // Reset mocks
  vi.clearAllMocks();
  
  // Mock navigator.onLine
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: true
  });
  
  // Mock window event listeners
  global.window = Object.create(window);
  window.addEventListener = vi.fn((event: string, listener: EventListener) => {
    if (!mockEventListeners[event]) {
      mockEventListeners[event] = [];
    }
    mockEventListeners[event].push(listener);
  });
  
  window.removeEventListener = vi.fn((event: string, listener: EventListener) => {
    if (mockEventListeners[event]) {
      const index = mockEventListeners[event].indexOf(listener);
      if (index > -1) {
        mockEventListeners[event].splice(index, 1);
      }
    }
  });
  
  // Mock Image constructor for retry functionality
  global.Image = vi.fn(() => ({
    onload: null,
    onerror: null,
    src: ''
  })) as any;
});

afterEach(() => {
  cleanupNetworkDetection();
  Object.keys(mockEventListeners).forEach(key => {
    mockEventListeners[key] = [];
  });
});

describe('Network Detection', () => {
  describe('initNetworkDetection', () => {
    it('should initialize with current online status', () => {
      initNetworkDetection(mockStore as any);
      
      expect(mockStore.dispatch).toHaveBeenCalledWith('networkStore/setOnlineStatus', true);
    });
    
    it('should set up event listeners for online/offline events', () => {
      initNetworkDetection(mockStore as any);
      
      expect(window.addEventListener).toHaveBeenCalledWith('online', expect.any(Function));
      expect(window.addEventListener).toHaveBeenCalledWith('offline', expect.any(Function));
    });
    
    it('should not initialize twice', () => {
      initNetworkDetection(mockStore as any);
      initNetworkDetection(mockStore as any);
      
      // Should only dispatch once
      expect(mockStore.dispatch).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('online/offline event handlers', () => {
    beforeEach(() => {
      initNetworkDetection(mockStore as any);
      vi.clearAllMocks(); // Clear the initial setOnlineStatus call
    });
    
    it('should handle online event', () => {
      // Simulate online event
      const onlineHandler = mockEventListeners['online'][0];
      onlineHandler(new Event('online'));
      
      expect(mockStore.dispatch).toHaveBeenCalledWith('networkStore/setOnlineStatus', true);
    });
    
    it('should handle offline event', () => {
      // Simulate offline event
      const offlineHandler = mockEventListeners['offline'][0];
      offlineHandler(new Event('offline'));
      
      expect(mockStore.dispatch).toHaveBeenCalledWith('networkStore/setOnlineStatus', false);
    });
  });
  
  describe('cleanupNetworkDetection', () => {
    it('should remove event listeners', () => {
      initNetworkDetection(mockStore as any);
      cleanupNetworkDetection();
      
      expect(window.removeEventListener).toHaveBeenCalledWith('online', expect.any(Function));
      expect(window.removeEventListener).toHaveBeenCalledWith('offline', expect.any(Function));
    });
    
    it('should handle cleanup when not initialized', () => {
      expect(() => cleanupNetworkDetection()).not.toThrow();
    });
  });
  
  describe('retryConnection', () => {
    beforeEach(() => {
      // Initialize network detection so the store is available for retryConnection
      initNetworkDetection(mockStore as any);
      vi.clearAllMocks(); // Clear the initial setOnlineStatus call
    });
    
    it('should resolve true when image loads successfully', async () => {
      const mockImg = {
        onload: null,
        onerror: null,
        src: ''
      };
      
      global.Image = vi.fn(() => mockImg) as any;
      
      const retryPromise = retryConnection();
      
      // Simulate successful image load
      if (mockImg.onload) {
        mockImg.onload({} as Event);
      }
      
      const result = await retryPromise;
      expect(result).toBe(true);
      expect(mockStore.dispatch).toHaveBeenCalledWith('networkStore/setOnlineStatus', true);
    });
    
    it('should resolve false when image fails to load', async () => {
      const mockImg = {
        onload: null,
        onerror: null,
        src: ''
      };
      
      global.Image = vi.fn(() => mockImg) as any;
      
      const retryPromise = retryConnection();
      
      // Simulate image load error
      if (mockImg.onerror) {
        mockImg.onerror({} as Event);
      }
      
      const result = await retryPromise;
      expect(result).toBe(false);
    });
    
    it('should timeout after 5 seconds', async () => {
      vi.useFakeTimers();
      
      const mockImg = {
        onload: null,
        onerror: null,
        src: ''
      };
      
      global.Image = vi.fn(() => mockImg) as any;
      
      const retryPromise = retryConnection();
      
      // Fast-forward time to trigger timeout
      vi.advanceTimersByTime(5000);
      
      const result = await retryPromise;
      expect(result).toBe(false);
      
      vi.useRealTimers();
    });
  });
});