/**
 * Test Suite for JWT Refresh Token Response Interceptor (Chunk 24)
 * 
 * Tests the automatic JWT refresh functionality added to axiosSetup.js.
 * Ensures proper handling of token expiration, concurrent requests, and error scenarios.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios from 'axios';
import JwtStorageService from '../../../app/frontend/services/jwtStorage';

// Mock JwtStorageService
vi.mock('../../../app/frontend/services/jwtStorage', () => ({
  default: {
    getToken: vi.fn(),
    setToken: vi.fn(),
    removeToken: vi.fn(),
    hasToken: vi.fn()
  }
}));

// Mock AuthService for refresh token functionality
const mockAuthService = {
  refreshToken: vi.fn(),
  clearLocalAuthState: vi.fn()
};

vi.mock('../../../app/frontend/services/authService', () => ({
  default: mockAuthService
}));

// Mock flash message service
vi.mock('../../../app/frontend/utils/flashMessage', () => ({
  sendFlashMessage: vi.fn()
}));

// Mock window.router for navigation
const mockRouter = {
  push: vi.fn()
};

Object.defineProperty(global, 'window', {
  value: {
    router: mockRouter
  },
  writable: true
});

// Import axiosSetup after mocking dependencies to ensure interceptors are set up
import '../../../app/frontend/utils/axiosSetup';

describe('JWT Refresh Token Response Interceptor', () => {
  let originalAdapter;
  let mockAdapter;
  let capturedConfigs;
  let mockResponses;
  let consoleLogSpy;
  let consoleErrorSpy;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup mock response queue
    capturedConfigs = [];
    mockResponses = [];
    
    // Mock console methods
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    // Mock the axios adapter to capture request configs and control responses
    originalAdapter = axios.defaults.adapter;
    mockAdapter = vi.fn().mockImplementation((config) => {
      capturedConfigs.push(config);
      
      // Return next queued response or default success
      const response = mockResponses.shift() || {
        data: 'default success',
        status: 200,
        statusText: 'OK',
        headers: {},
        config
      };
      
      if (response.error) {
        return Promise.reject(response.error);
      }
      
      return Promise.resolve(response);
    });
    
    axios.defaults.adapter = mockAdapter;
  });

  afterEach(() => {
    // Restore original adapter
    axios.defaults.adapter = originalAdapter;
    consoleLogSpy.mockRestore();
    consoleErrorSpy.mockRestore();
    
    // Clear any residual mock responses
    mockResponses = [];
  });

  describe('Successful Token Refresh Scenarios', () => {
    it('should refresh token and retry request when 401 occurs with valid JWT', async () => {
      // Arrange
      const mockToken = 'expired-jwt-token';
      const newToken = 'refreshed-jwt-token';
      
      JwtStorageService.getToken.mockReturnValue(mockToken);
      mockAuthService.refreshToken.mockResolvedValue(true);
      
      // First request fails with 401, retry succeeds
      mockResponses.push({
        error: {
          response: { status: 401 },
          config: { url: '/api/v1/data' }
        }
      });
      mockResponses.push({
        data: { success: true, data: 'retrieved data' },
        status: 200,
        statusText: 'OK',
        headers: {}
      });

      // Act
      const response = await axios.get('/api/v1/data');

      // Assert
      expect(mockAuthService.refreshToken).toHaveBeenCalledOnce();
      expect(capturedConfigs).toHaveLength(2); // Original request + retry
      expect(response.data.success).toBe(true);
      expect(consoleLogSpy).toHaveBeenCalledWith('JWT token expired, attempting refresh...');
      expect(consoleLogSpy).toHaveBeenCalledWith('Token refresh successful, retrying original request');
    });

    it('should handle multiple concurrent requests during token refresh', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue('expired-token');
      mockAuthService.refreshToken.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(true), 100))
      );

      // All requests fail with 401 initially
      for (let i = 0; i < 5; i++) {
        mockResponses.push({
          error: {
            response: { status: 401 },
            config: { url: `/api/v1/data${i}` }
          }
        });
      }
      
      // All retries succeed
      for (let i = 0; i < 5; i++) {
        mockResponses.push({
          data: { success: true, data: `data${i}` },
          status: 200,
          statusText: 'OK',
          headers: {}
        });
      }

      // Act - Make multiple concurrent requests
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(axios.get(`/api/v1/data${i}`));
      }
      
      const responses = await Promise.all(promises);

      // Assert
      expect(mockAuthService.refreshToken).toHaveBeenCalledOnce(); // Only called once despite multiple requests
      expect(capturedConfigs).toHaveLength(10); // 5 original + 5 retries
      expect(responses).toHaveLength(5);
      responses.forEach((response, index) => {
        expect(response.data.data).toBe(`data${index}`);
      });
    });

    it('should not retry requests with _skipRefreshInterceptor flag', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue('some-token');
      
      mockResponses.push({
        error: {
          response: { status: 401 },
          config: { 
            url: '/api/v1/auth/refresh_token',
            _skipRefreshInterceptor: true 
          }
        }
      });

      // Act & Assert
      await expect(axios.post('/api/v1/auth/refresh_token', {}, {
        _skipRefreshInterceptor: true
      })).rejects.toThrow();

      expect(mockAuthService.refreshToken).not.toHaveBeenCalled();
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
    });
  });

  describe('Token Refresh Failure Scenarios', () => {
    it('should logout user when refresh token fails', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue('expired-token');
      mockAuthService.refreshToken.mockRejectedValue(new Error('Refresh failed'));
      
      mockResponses.push({
        error: {
          response: { status: 401 },
          config: { url: '/api/v1/data' }
        }
      });

      // Act & Assert
      await expect(axios.get('/api/v1/data')).rejects.toThrow('Refresh failed');

      expect(mockAuthService.refreshToken).toHaveBeenCalledOnce();
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
      expect(consoleErrorSpy).toHaveBeenCalledWith('Token refresh failed:', 'Refresh failed');
    });

    it('should handle refresh token network errors gracefully', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue('expired-token');
      const networkError = new Error('Network Error');
      networkError.response = { status: 503 };
      mockAuthService.refreshToken.mockRejectedValue(networkError);
      
      mockResponses.push({
        error: {
          response: { status: 401 },
          config: { url: '/api/v1/data' }
        }
      });

      // Act & Assert
      await expect(axios.get('/api/v1/data')).rejects.toThrow('Network Error');

      expect(mockAuthService.refreshToken).toHaveBeenCalledOnce();
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
    });

    it('should not attempt refresh when no JWT token is available', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue(null);
      
      mockResponses.push({
        error: {
          response: { status: 401 },
          config: { url: '/api/v1/data' }
        }
      });

      // Act & Assert
      await expect(axios.get('/api/v1/data')).rejects.toThrow();

      expect(mockAuthService.refreshToken).not.toHaveBeenCalled();
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
    });

    it('should handle JwtStorageService errors gracefully', async () => {
      // Arrange
      JwtStorageService.getToken.mockImplementation(() => {
        throw new Error('Storage error');
      });
      
      mockResponses.push({
        error: {
          response: { status: 401 },
          config: { url: '/api/v1/data' }
        }
      });

      // Act & Assert
      await expect(axios.get('/api/v1/data')).rejects.toThrow();

      expect(mockAuthService.refreshToken).not.toHaveBeenCalled();
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
    });
  });

  describe('Request Retry Prevention', () => {
    it('should not retry a request more than once', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue('expired-token');
      mockAuthService.refreshToken.mockResolvedValue(true);
      
      // Both original and retry fail with 401
      mockResponses.push({
        error: {
          response: { status: 401 },
          config: { url: '/api/v1/data' }
        }
      });
      mockResponses.push({
        error: {
          response: { status: 401 },
          config: { url: '/api/v1/data', _retry: true }
        }
      });

      // Act & Assert
      await expect(axios.get('/api/v1/data')).rejects.toThrow();

      expect(mockAuthService.refreshToken).toHaveBeenCalledOnce();
      expect(capturedConfigs).toHaveLength(2); // Original + one retry only
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
    });

    it('should skip refresh for auth-related endpoints', async () => {
      // Arrange
      const authEndpoints = [
        '/api/v1/auth/jwt_login',
        '/api/v1/auth/jwt_logout',
        '/api/v1/auth/refresh_token'
      ];

      JwtStorageService.getToken.mockReturnValue('some-token');

      for (const endpoint of authEndpoints) {
        mockResponses.push({
          error: {
            response: { status: 401 },
            config: { url: endpoint }
          }
        });

        // Act & Assert
        await expect(axios.post(endpoint)).rejects.toThrow();
      }

      expect(mockAuthService.refreshToken).not.toHaveBeenCalled();
      expect(mockRouter.push).toHaveBeenCalledTimes(authEndpoints.length);
    });
  });

  describe('Non-401 Error Handling', () => {
    it('should not attempt refresh for non-401 errors', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue('valid-token');
      
      const errorCodes = [400, 403, 404, 500];
      
      for (const code of errorCodes) {
        mockResponses.push({
          error: {
            response: { 
              status: code,
              data: { message: `Error ${code}` }
            },
            config: { url: '/api/v1/data' }
          }
        });

        // Act & Assert
        await expect(axios.get('/api/v1/data')).rejects.toThrow();
      }

      expect(mockAuthService.refreshToken).not.toHaveBeenCalled();
    });
  });

  describe('Router Integration', () => {
    it('should handle missing router gracefully', async () => {
      // Arrange
      const originalRouter = window.router;
      delete window.router;
      
      Object.defineProperty(window, 'location', {
        value: { href: '' },
        writable: true
      });

      JwtStorageService.getToken.mockReturnValue(null);
      
      mockResponses.push({
        error: {
          response: { status: 401 },
          config: { url: '/api/v1/data' }
        }
      });

      // Act & Assert
      await expect(axios.get('/api/v1/data')).rejects.toThrow();

      expect(window.location.href).toBe('/users/sign_in');

      // Cleanup
      window.router = originalRouter;
    });

    it('should use consistent messages through helper function', async () => {
      // Arrange
      const { sendFlashMessage } = await import('../../../app/frontend/utils/flashMessage');
      JwtStorageService.getToken.mockReturnValue(null);
      
      mockResponses.push({
        error: {
          response: { status: 401 },
          config: { url: '/api/v1/data' }
        }
      });

      // Act & Assert
      await expect(axios.get('/api/v1/data')).rejects.toThrow();

      // Verify helper function is used (consistent message and routing)
      expect(sendFlashMessage).toHaveBeenCalledWith(
        'Vaše relace vypršela. Přihlaste se prosím znovu.', 
        'warning'
      );
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
    });
  });

  describe('Queue Management', () => {
    it('should process failed queue when refresh fails', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue('expired-token');
      const refreshError = new Error('Refresh failed');
      mockAuthService.refreshToken.mockRejectedValue(refreshError);

      // Multiple requests fail with 401
      for (let i = 0; i < 3; i++) {
        mockResponses.push({
          error: {
            response: { status: 401 },
            config: { url: `/api/v1/data${i}` }
          }
        });
      }

      // Act - Make multiple concurrent requests
      const promises = [
        axios.get('/api/v1/data0'),
        axios.get('/api/v1/data1'),
        axios.get('/api/v1/data2')
      ];

      // Assert - All requests should fail with the refresh error
      await expect(Promise.all(promises)).rejects.toThrow();

      expect(mockAuthService.refreshToken).toHaveBeenCalledOnce();
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
    });
  });
});