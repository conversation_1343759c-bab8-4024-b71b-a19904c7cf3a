/**
 * Test Suite for Axios JWT Authorization Header Interceptor (Chunk 23)
 * 
 * Tests the automatic JWT header injection functionality added to axiosSetup.js.
 * Ensures proper dual authentication support during the migration period.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios from 'axios';
import JwtStorageService from '../../../app/frontend/services/jwtStorage';

// Mock JwtStorageService
vi.mock('../../../app/frontend/services/jwtStorage', () => ({
  default: {
    getToken: vi.fn(),
    setToken: vi.fn(),
    removeToken: vi.fn(),
    hasToken: vi.fn()
  }
}));

// Mock flash message service
vi.mock('../../../app/frontend/utils/flashMessage', () => ({
  sendFlashMessage: vi.fn()
}));

// Mock document for CSRF token testing
Object.defineProperty(global, 'document', {
  value: {
    querySelector: vi.fn()
  }
});

// Import axiosSetup after mocking dependencies to ensure interceptors are set up
import '../../../app/frontend/utils/axiosSetup';

describe('Axios JWT Authorization Header Interceptor', () => {
  let originalAdapter;
  let mockAdapter;
  let consoleWarnSpy;
  let capturedConfigs;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Capture request configs instead of making real requests
    capturedConfigs = [];
    
    // Mock the axios adapter to capture request configs
    originalAdapter = axios.defaults.adapter;
    mockAdapter = vi.fn().mockImplementation((config) => {
      capturedConfigs.push(config);
      return Promise.resolve({
        data: 'test response',
        status: 200,
        statusText: 'OK',
        headers: {},
        config
      });
    });
    
    axios.defaults.adapter = mockAdapter;
    
    // Spy on console.warn for error logging tests
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore original adapter
    axios.defaults.adapter = originalAdapter;
    consoleWarnSpy.mockRestore();
  });

  describe('JWT Token Header Injection', () => {
    it('should add Authorization header when JWT token is available', async () => {
      // Arrange
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      JwtStorageService.getToken.mockReturnValue(mockToken);

      // Act
      await axios.get('/api/v1/test');

      // Assert
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].headers.Authorization).toBe(`Bearer ${mockToken}`);
    });

    it('should not add Authorization header when no JWT token is available', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue(null);

      // Act
      await axios.get('/api/v1/test');

      // Assert
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].headers.Authorization).toBeUndefined();
    });

    it('should not add Authorization header when JWT token is empty string', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue('');

      // Act
      await axios.get('/api/v1/test');

      // Assert
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].headers.Authorization).toBeUndefined();
    });
  });

  describe('CSRF Token Compatibility', () => {
    it('should preserve CSRF token for session-based requests', async () => {
      // Arrange
      const mockCsrfToken = 'test-csrf-token';
      axios.defaults.headers.common['X-CSRF-Token'] = mockCsrfToken;
      JwtStorageService.getToken.mockReturnValue(null);

      // Act
      await axios.post('/api/v1/test', { data: 'test' });

      // Assert
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].headers['X-CSRF-Token']).toBe(mockCsrfToken);
    });

    it('should include both JWT and CSRF tokens when both are available', async () => {
      // Arrange
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      const mockCsrfToken = 'test-csrf-token';
      
      JwtStorageService.getToken.mockReturnValue(mockToken);
      axios.defaults.headers.common['X-CSRF-Token'] = mockCsrfToken;

      // Act
      await axios.post('/api/v1/test', { data: 'test' });

      // Assert
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].headers.Authorization).toBe(`Bearer ${mockToken}`);
      expect(capturedConfigs[0].headers['X-CSRF-Token']).toBe(mockCsrfToken);
    });
  });

  describe('Error Handling', () => {
    it('should handle JWT storage access failures gracefully', async () => {
      // Arrange
      const storageError = new Error('Storage quota exceeded');
      JwtStorageService.getToken.mockImplementation(() => {
        throw storageError;
      });

      // Act
      await axios.get('/api/v1/test');

      // Assert
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Failed to access JWT token for request:',
        storageError
      );
      
      // Request should still proceed without Authorization header
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].headers.Authorization).toBeUndefined();
    });

    it('should not fail the request when JWT service throws an error', async () => {
      // Arrange
      JwtStorageService.getToken.mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      // Act & Assert
      await expect(axios.get('/api/v1/test')).resolves.toBeDefined();
      expect(capturedConfigs).toHaveLength(1);
    });
  });

  describe('Dual Authentication Scenarios', () => {
    it('should work with JWT authentication', async () => {
      // Arrange
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      JwtStorageService.getToken.mockReturnValue(mockToken);

      // Act
      await axios.get('/api/v1/user');

      // Assert
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].headers.Authorization).toBe(`Bearer ${mockToken}`);
    });

    it('should work with session authentication (no JWT)', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue(null);

      // Act
      await axios.get('/api/v1/user');

      // Assert
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].headers.Authorization).toBeUndefined();
      // Should still have withCredentials for session cookies
      expect(capturedConfigs[0].withCredentials).toBe(true);
    });
  });

  describe('Request Configuration Preservation', () => {
    it('should preserve existing request headers', async () => {
      // Arrange
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      JwtStorageService.getToken.mockReturnValue(mockToken);

      const customHeaders = {
        'Content-Type': 'application/json',
        'X-Custom-Header': 'custom-value'
      };

      // Act
      await axios.get('/api/v1/test', { headers: customHeaders });

      // Assert
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].headers.Authorization).toBe(`Bearer ${mockToken}`);
      expect(capturedConfigs[0].headers['Content-Type']).toBe('application/json');
      expect(capturedConfigs[0].headers['X-Custom-Header']).toBe('custom-value');
    });

    it('should preserve withCredentials setting', async () => {
      // Arrange
      JwtStorageService.getToken.mockReturnValue(null);

      // Act
      await axios.get('/api/v1/test');

      // Assert
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].withCredentials).toBe(true);
    });

    it('should override request-specific Authorization header with JWT token', async () => {
      // Arrange - This tests the behavior where interceptor overrides manual Authorization
      const mockToken = 'jwt-token-from-storage';
      const explicitAuth = 'Bearer explicit-token';
      
      JwtStorageService.getToken.mockReturnValue(mockToken);

      // Act
      await axios.get('/api/v1/test', {
        headers: {
          Authorization: explicitAuth
        }
      });

      // Assert - Interceptor should override the request-specific header with JWT
      // This ensures consistent JWT usage throughout the application
      expect(capturedConfigs).toHaveLength(1);
      expect(capturedConfigs[0].headers.Authorization).toBe(`Bearer ${mockToken}`);
    });
  });

  describe('Integration Test Scenarios', () => {
    it('should work correctly for multiple sequential requests', async () => {
      // Arrange
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
      JwtStorageService.getToken.mockReturnValue(mockToken);

      // Act
      await axios.get('/api/v1/user');
      await axios.post('/api/v1/data', { test: 'data' });
      
      // Change token for third request
      const newToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.new.token';
      JwtStorageService.getToken.mockReturnValue(newToken);
      await axios.put('/api/v1/update', { test: 'update' });

      // Assert
      expect(capturedConfigs).toHaveLength(3);
      expect(capturedConfigs[0].headers.Authorization).toBe(`Bearer ${mockToken}`);
      expect(capturedConfigs[1].headers.Authorization).toBe(`Bearer ${mockToken}`);
      expect(capturedConfigs[2].headers.Authorization).toBe(`Bearer ${newToken}`);
    });
  });
});