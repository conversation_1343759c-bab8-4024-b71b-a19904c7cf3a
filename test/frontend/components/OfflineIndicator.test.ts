// ABOUTME: Unit tests for OfflineIndicator Vue component
// ABOUTME: Tests component rendering, user interactions, and Vuex integration

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createStore } from 'vuex';
import OfflineIndicator from '../../../app/frontend/components/OfflineIndicator.vue';
import { retryConnection } from '../../../app/frontend/utils/networkDetection';

// Mock the network detection utility
vi.mock('../../../app/frontend/utils/networkDetection', () => ({
  retryConnection: vi.fn()
}));

const mockRetryConnection = vi.mocked(retryConnection);

// Create a mock store
function createMockStore(showOfflineIndicator = true) {
  return createStore({
    modules: {
      networkStore: {
        namespaced: true,
        state: {
          isOnline: !showOfflineIndicator,
          showOfflineIndicator
        },
        getters: {
          showOfflineIndicator: (state) => state.showOfflineIndicator
        },
        actions: {
          hideOfflineIndicator: vi.fn()
        }
      }
    }
  });
}

// Mock i18n translation function
const mockT = (key: string, fallback?: string) => {
  const translations: { [key: string]: string } = {
    'network.offline_message': 'Jste offline',
    'network.retry': 'Zkusit znovu',
    'network.retrying': 'Zkouším...',
    'network.hide': 'Skrýt'
  };
  return translations[key] || fallback || key;
};

describe('OfflineIndicator', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders when showOfflineIndicator is true', () => {
    const store = createMockStore(true);
    const wrapper = mount(OfflineIndicator, {
      global: {
        plugins: [store],
        mocks: {
          $t: mockT
        }
      }
    });

    expect(wrapper.find('[data-testid="offline-indicator"]').exists()).toBe(true);
    expect(wrapper.text()).toContain('Jste offline');
  });

  it('does not render when showOfflineIndicator is false', () => {
    const store = createMockStore(false);
    const wrapper = mount(OfflineIndicator, {
      global: {
        plugins: [store],
        mocks: {
          $t: mockT
        }
      }
    });

    expect(wrapper.find('[data-testid="offline-indicator"]').exists()).toBe(false);
  });

  it('displays correct Czech text content', () => {
    const store = createMockStore(true);
    const wrapper = mount(OfflineIndicator, {
      global: {
        plugins: [store],
        mocks: {
          $t: mockT
        }
      }
    });

    expect(wrapper.find('.offline-message').text()).toBe('Jste offline');
    expect(wrapper.find('[data-testid="retry-button"]').text()).toBe('Zkusit znovu');
  });

  it('calls hideOfflineIndicator when close button is clicked', async () => {
    const store = createMockStore(true);
    const wrapper = mount(OfflineIndicator, {
      global: {
        plugins: [store],
        mocks: {
          $t: mockT
        }
      }
    });

    const closeButton = wrapper.find('[data-testid="close-button"]');
    expect(closeButton.exists()).toBe(true);

    await closeButton.trigger('click');

    expect(store.dispatch).toHaveBeenCalledWith('networkStore/hideOfflineIndicator');
  });

  it('handles retry button click and shows loading state', async () => {
    mockRetryConnection.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve(true), 100)));
    
    const store = createMockStore(true);
    const wrapper = mount(OfflineIndicator, {
      global: {
        plugins: [store],
        mocks: {
          $t: mockT
        }
      }
    });

    const retryButton = wrapper.find('[data-testid="retry-button"]');
    
    // Initially should show "Zkusit znovu"
    expect(retryButton.text()).toBe('Zkusit znovu');
    expect(retryButton.attributes('disabled')).toBeUndefined();

    // Click retry button
    await retryButton.trigger('click');
    
    // Should show loading state
    await wrapper.vm.$nextTick();
    expect(wrapper.vm.isRetrying).toBe(true);
    expect(retryButton.text()).toBe('Zkouším...');
    expect(retryButton.attributes('disabled')).toBeDefined();

    // Wait for retry to complete
    await new Promise(resolve => setTimeout(resolve, 150));
    await wrapper.vm.$nextTick();
    
    // Should return to normal state
    expect(wrapper.vm.isRetrying).toBe(false);
    expect(mockRetryConnection).toHaveBeenCalled();
  });

  it('handles retry failure gracefully', async () => {
    mockRetryConnection.mockRejectedValue(new Error('Network error'));
    
    const store = createMockStore(true);
    const wrapper = mount(OfflineIndicator, {
      global: {
        plugins: [store],
        mocks: {
          $t: mockT
        }
      }
    });

    const retryButton = wrapper.find('[data-testid="retry-button"]');
    
    await retryButton.trigger('click');
    
    // Wait for retry to complete
    await new Promise(resolve => setTimeout(resolve, 50));
    await wrapper.vm.$nextTick();
    
    // Should return to normal state even after error
    expect(wrapper.vm.isRetrying).toBe(false);
    expect(mockRetryConnection).toHaveBeenCalled();
  });

  it('has correct CSS classes and structure', () => {
    const store = createMockStore(true);
    const wrapper = mount(OfflineIndicator, {
      global: {
        plugins: [store],
        mocks: {
          $t: mockT
        }
      }
    });

    expect(wrapper.find('.offline-indicator').exists()).toBe(true);
    expect(wrapper.find('.offline-content').exists()).toBe(true);
    expect(wrapper.find('.offline-icon').exists()).toBe(true);
    expect(wrapper.find('.offline-message').exists()).toBe(true);
    expect(wrapper.find('.retry-button').exists()).toBe(true);
    expect(wrapper.find('.close-button').exists()).toBe(true);
  });

  it('has proper accessibility attributes', () => {
    const store = createMockStore(true);
    const wrapper = mount(OfflineIndicator, {
      global: {
        plugins: [store],
        mocks: {
          $t: mockT
        }
      }
    });

    expect(wrapper.find('[data-vue-component="offline-indicator"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="offline-indicator"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="retry-button"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="close-button"]').exists()).toBe(true);
    
    // Check title attribute on close button
    const closeButton = wrapper.find('[data-testid="close-button"]');
    expect(closeButton.attributes('title')).toBe('Skrýt');
  });
});