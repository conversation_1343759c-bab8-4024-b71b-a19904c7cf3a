import { describe, it, expect, beforeEach, vi } from 'vitest'
import { fireEvent, waitFor } from '@testing-library/vue'
import { renderWithProviders } from '../utils/test-utils'
import SystemMessage from '@/components/SystemMessage.vue'

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {}
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      store = {}
    })
  }
})()

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('SystemMessage', () => {
  beforeEach(() => {
    localStorageMock.clear()
    vi.clearAllMocks()
  })

  it('renders system messages when not dismissed', () => {
    const { getByText, getByTestId } = renderWithProviders(SystemMessage)
    
    // Should show the version announcement
    expect(getByText('Nová verze 2.5.0')).toBeInTheDocument()
    expect(getByText('Vylepšené PWA')).toBeInTheDocument()
    
    // Should show the maintenance message
    expect(getByText('Plánovaná údržba')).toBeInTheDocument()
    
    // Should have dismiss buttons
    expect(getByTestId('dismiss-version-2025-08-10')).toBeInTheDocument()
    expect(getByTestId('dismiss-maintenance-2025-01-15')).toBeInTheDocument()
  })

  it('hides dismissed messages from DOM immediately after dismiss click', async () => {
    const { getByText, getByTestId, queryByText } = renderWithProviders(SystemMessage)
    
    // Verify message is initially visible
    expect(getByText('Nová verze 2.5.0')).toBeInTheDocument()
    
    // Click dismiss button
    const dismissButton = getByTestId('dismiss-version-2025-08-10')
    await fireEvent.click(dismissButton)
    
    // Message should be immediately hidden from DOM
    await waitFor(() => {
      expect(queryByText('Nová verze 2.5.0')).not.toBeInTheDocument()
    })
    
    // Verify localStorage was updated
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'dismissed_system_messages',
      JSON.stringify(['version-2025-08-10'])
    )
  })

  it('persists dismissed state across component remounts', () => {
    // Pre-populate localStorage with dismissed message
    // Use the actual localStorage API which will call our mock
    localStorage.setItem('dismissed_system_messages', JSON.stringify(['version-2025-08-10']))

    const { queryByText, getByText } = renderWithProviders(SystemMessage)

    // Dismissed message should not appear
    expect(queryByText('Nová verze 2.5.0')).not.toBeInTheDocument()

    // Non-dismissed message should still appear
    expect(getByText('Plánovaná údržba')).toBeInTheDocument()
  })

  it('hides entire component when all messages are dismissed', async () => {
    const { getByTestId, container } = renderWithProviders(SystemMessage)
    
    // Dismiss both messages
    await fireEvent.click(getByTestId('dismiss-version-2025-08-10'))
    await fireEvent.click(getByTestId('dismiss-maintenance-2025-01-15'))
    
    // Entire component should be hidden
    await waitFor(() => {
      expect(container.querySelector('.bg-blue-50')).not.toBeInTheDocument()
    })
  })

  it('handles localStorage errors gracefully', async () => {
    // Mock localStorage.setItem to throw an error
    localStorageMock.setItem.mockImplementationOnce(() => {
      throw new Error('Storage quota exceeded')
    })
    
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    const { getByTestId, getByText } = renderWithProviders(SystemMessage)
    
    // Click dismiss button
    await fireEvent.click(getByTestId('dismiss-version-2025-08-10'))
    
    // Should log error but not crash
    expect(consoleSpy).toHaveBeenCalledWith('Could not save dismissed message:', expect.any(Error))
    
    // Message should still be visible since localStorage failed
    expect(getByText('Nová verze 2.5.0')).toBeInTheDocument()
    
    consoleSpy.mockRestore()
  })

  it('toggles announcements collapse state', async () => {
    const { getByText, container } = renderWithProviders(SystemMessage)

    // Initially expanded - content should be visible
    expect(getByText('Vylepšené PWA')).toBeInTheDocument()

    // Find and click the collapse button (ChevronUp icon)
    const collapseButton = document.querySelector('button[class*="ml-2"]')
    expect(collapseButton).toBeInTheDocument()

    await fireEvent.click(collapseButton!)

    // Content should be hidden with display: none
    await waitFor(() => {
      const contentDiv = container.querySelector('div[data-v-013f8a0f=""][style*="display: none"]')
      expect(contentDiv).toBeInTheDocument()
    })

    // Verify localStorage was updated
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'system_announcements_collapsed',
      'true'
    )
  })
})
