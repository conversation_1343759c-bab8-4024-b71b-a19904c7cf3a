/**
 * Test Suite for HeaderDropdown Logout Integration (Chunk 26)
 * 
 * Tests the updated HeaderDropdown component logout functionality that now uses
 * AuthService via userStore instead of direct API calls or broken route navigation.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createStore } from 'vuex';

// Mock Vue Router
const mockRouter = {
  push: vi.fn(() => Promise.resolve())
};

// Mock userStore module
const mockUserStore = {
  namespaced: true,
  actions: {
    logout: vi.fn()
  },
  state: {
    email: '<EMAIL>'
  }
};

// Mock Vuex store
const store = createStore({
  modules: {
    userStore: mockUserStore
  }
});

// Mock the component properly by importing after setting up mocks
vi.mock('lucide-vue-next', () => ({
  Menu: { template: '<div>Menu</div>' },
  LogOut: { template: '<div>LogOut</div>' },
  Settings: { template: '<div>Settings</div>' },
  Clock: { template: '<div>Clock</div>' },
  LandPlot: { template: '<div>LandPlot</div>' },
  CalendarCheck: { template: '<div>CalendarCheck</div>' },
  Crown: { template: '<div>Crown</div>' },
  UsersRound: { template: '<div>UsersRound</div>' },
  Calendar: { template: '<div>Calendar</div>' },
  FileText: { template: '<div>FileText</div>' },
  Mailbox: { template: '<div>Mailbox</div>' },
  Warehouse: { template: '<div>Warehouse</div>' },
  CircleUserRound: { template: '<div>CircleUserRound</div>' },
  Lock: { template: '<div>Lock</div>' }
}));

vi.mock('../../../app/frontend/mixins/authorizationMixin', () => ({
  default: {
    computed: {
      userRoles: () => [],
      firstRole: () => 'admin',
      isOwner: () => false,
      isAdmin: () => true,
      isSupervisor: () => false,
      isManager: () => true,
      hasPlus: () => true,
      hasPremium: () => false,
      currentPlan: () => 'plus'
    },
    methods: {
      hasRole: () => false,
      hasAnyRole: () => false,
      can: () => true
    }
  }
}));

vi.mock('../../../app/frontend/components/LocalizedLink.vue', () => ({
  default: {
    template: '<a :href="to"><slot /></a>',
    props: ['to', 'useAnchor']
  }
}));

import HeaderDropdown from '../../../app/frontend/components/HeaderDropdown.vue';

describe('HeaderDropdown Logout Integration (Chunk 26)', () => {
  let wrapper;
  let consoleSpy;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Mock console.error for error handling tests
    consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    // Mock global window.location
    Object.defineProperty(window, 'location', {
      value: {
        href: ''
      },
      writable: true
    });

    // Mount component with mocks
    wrapper = mount(HeaderDropdown, {
      global: {
        plugins: [store],
        mocks: {
          $router: mockRouter,
          $t: (key, fallback) => fallback || key
        }
      },
      props: {
        user: 1,
        company: { name: 'Test Company' },
        subscription: { current_plan: 'plus', available_features: [] }
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
    consoleSpy.mockRestore();
  });

  describe('logout button integration', () => {
    it('should render logout button when dropdown is open', async () => {
      // Open the dropdown first
      await wrapper.setData({ isOpen: true });
      await wrapper.vm.$nextTick();
      
      const logoutButton = wrapper.find('a[href="#"]');
      expect(logoutButton.exists()).toBe(true);
      expect(logoutButton.text()).toContain('Odhlásit se');
    });

    it('should call handleLogout method when logout button is clicked', async () => {
      // Open the dropdown first
      await wrapper.setData({ isOpen: true });
      await wrapper.vm.$nextTick();
      
      const handleLogoutSpy = vi.spyOn(wrapper.vm, 'handleLogout');
      
      const logoutButton = wrapper.find('a[href="#"]');
      await logoutButton.trigger('click');
      
      expect(handleLogoutSpy).toHaveBeenCalled();
    });
  });

  describe('handleLogout method', () => {
    it('should dispatch userStore logout action', async () => {
      const dispatchSpy = vi.spyOn(wrapper.vm.$store, 'dispatch');
      
      await wrapper.vm.handleLogout();
      
      expect(dispatchSpy).toHaveBeenCalledWith('userStore/logout');
    });

    it('should close dropdown after successful logout', async () => {
      const closeDropdownSpy = vi.spyOn(wrapper.vm, 'closeDropdown');
      mockUserStore.actions.logout.mockResolvedValue();
      
      await wrapper.vm.handleLogout();
      
      expect(closeDropdownSpy).toHaveBeenCalled();
    });

    it('should redirect to login page after successful logout', async () => {
      mockUserStore.actions.logout.mockResolvedValue();
      
      await wrapper.vm.handleLogout();
      
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
    });

    it('should handle router push failure with fallback redirect', async () => {
      mockUserStore.actions.logout.mockResolvedValue();
      mockRouter.push.mockRejectedValue(new Error('Router error'));
      
      await wrapper.vm.handleLogout();
      
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
      expect(window.location.href).toBe('/');
    });

    it('should handle logout error and still redirect for security', async () => {
      const logoutError = new Error('Logout failed');
      mockUserStore.actions.logout.mockRejectedValue(logoutError);
      
      await wrapper.vm.handleLogout();
      
      expect(consoleSpy).toHaveBeenCalledWith('Logout failed:', logoutError);
      expect(window.location.href).toBe('/');
    });

    it('should ensure user is redirected even on complete failure', async () => {
      // Simulate complete failure - store dispatch fails and router fails
      mockUserStore.actions.logout.mockRejectedValue(new Error('Store error'));
      mockRouter.push.mockRejectedValue(new Error('Router error'));
      
      await wrapper.vm.handleLogout();
      
      // Security measure: always redirect to prevent staying logged in
      expect(window.location.href).toBe('/');
    });
  });

  describe('integration with existing logout flow', () => {
    it('should not use direct axios calls for logout', async () => {
      // More robust check: verify the component uses AuthService via userStore
      // instead of direct axios calls for logout operations
      const dispatchSpy = vi.spyOn(wrapper.vm.$store, 'dispatch');
      
      // Open dropdown and trigger logout
      await wrapper.setData({ isOpen: true });
      await wrapper.vm.$nextTick();
      
      await wrapper.vm.handleLogout();
      
      // Verify it uses userStore action (which integrates with AuthService)
      expect(dispatchSpy).toHaveBeenCalledWith('userStore/logout');
      
      // Verify the component doesn't have direct axios.delete calls
      // This is a behavioral test rather than string inspection
      expect(wrapper.vm.handleLogout.toString()).not.toContain('axios.delete');
    });

    it('should use AuthService via userStore instead of direct API calls', async () => {
      const dispatchSpy = vi.spyOn(wrapper.vm.$store, 'dispatch');
      
      await wrapper.vm.handleLogout();
      
      // Verify it uses the userStore logout action (which uses AuthService from Chunk 25)
      expect(dispatchSpy).toHaveBeenCalledWith('userStore/logout');
      
      // Verify it doesn't make direct API calls
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('axios')
      );
    });

    it('should be compatible with dual authentication from Chunk 25', async () => {
      // The userStore logout action (from Chunk 25) handles both JWT and session cleanup
      // This test ensures the component properly delegates to that unified logout logic
      
      await wrapper.vm.handleLogout();
      
      expect(wrapper.vm.$store.dispatch).toHaveBeenCalledWith('userStore/logout');
      // The userStore action will handle JWT/session cleanup via AuthService
    });
  });

  describe('user experience', () => {
    it('should provide seamless logout experience', async () => {
      mockUserStore.actions.logout.mockResolvedValue();
      
      // Open the dropdown first
      await wrapper.setData({ isOpen: true });
      await wrapper.vm.$nextTick();
      
      const logoutButton = wrapper.find('a[href="#"]');
      await logoutButton.trigger('click');
      
      // Wait for async operations
      await wrapper.vm.$nextTick();
      
      // Dropdown should close
      expect(wrapper.vm.isOpen).toBe(false);
      
      // Should redirect to login
      expect(mockRouter.push).toHaveBeenCalledWith({ name: 'login' });
    });

    it('should maintain security even on errors', async () => {
      // Even if everything fails, user should be redirected for security
      mockUserStore.actions.logout.mockRejectedValue(new Error('Complete failure'));
      
      await wrapper.vm.handleLogout();
      
      expect(window.location.href).toBe('/');
    });
  });
});