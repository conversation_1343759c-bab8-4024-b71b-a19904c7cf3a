import puppeteer from 'puppeteer';

async function testSidebarVueRouter() {
  console.log('=== Testing Sidebar Vue Router Integration ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('Step 1: Login and navigate to SK dashboard...');
    
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    // Navigate to SK locale
    await page.goto('http://0.0.0.0:5100/sk/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    console.log(`✅ Starting at: ${page.url()}`);
    
    // Wait for Vue app to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\nStep 2: Check Vue Router and sidebar state...');
    
    const routerInfo = await page.evaluate(() => {
      return {
        hasRouter: !!window.router,
        hasVue: !!window.Vue,
        currentRoute: window.router ? {
          path: window.router.currentRoute.value.path,
          params: window.router.currentRoute.value.params,
          name: window.router.currentRoute.value.name
        } : null,
        sidebarLinks: Array.from(document.querySelectorAll('.nav-item')).map(item => ({
          text: item.textContent.trim(),
          tag: item.tagName,
          isRouterLink: item.tagName === 'ROUTER-LINK',
          href: item.href,
          to: item.getAttribute('to')
        }))
      };
    });
    
    console.log('Vue Router state:');
    console.log(`  Has router: ${routerInfo.hasRouter}`);
    console.log(`  Current route: ${routerInfo.currentRoute?.path}`);
    console.log(`  Current locale: ${routerInfo.currentRoute?.params?.locale}`);
    
    console.log('\nSidebar links analysis:');
    routerInfo.sidebarLinks.forEach((link, index) => {
      console.log(`  ${index + 1}. ${link.text}`);
      console.log(`     Tag: ${link.tag}, Router Link: ${link.isRouterLink}`);
      console.log(`     To: ${link.to}, Href: ${link.href}`);
    });
    
    console.log('\nStep 3: Test Vue Router navigation programmatically...');
    
    // Try to navigate using Vue Router directly
    const navigationResult = await page.evaluate(() => {
      if (window.router) {
        try {
          // Try to navigate to events
          window.router.push('/sk/events');
          return { success: true, error: null };
        } catch (error) {
          return { success: false, error: error.message };
        }
      }
      return { success: false, error: 'No router available' };
    });
    
    if (navigationResult.success) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      const newUrl = page.url();
      console.log(`  ✅ Programmatic navigation successful: ${newUrl}`);
    } else {
      console.log(`  ❌ Programmatic navigation failed: ${navigationResult.error}`);
    }
    
    console.log('\nStep 4: Test clicking router-link elements...');
    
    // Go back to dashboard
    await page.goto('http://0.0.0.0:5100/sk/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Find and click a router-link
    const routerLinkClick = await page.evaluate(() => {
      const routerLinks = document.querySelectorAll('router-link');
      if (routerLinks.length > 0) {
        const firstLink = routerLinks[0];
        firstLink.click();
        return { clicked: true, to: firstLink.getAttribute('to') };
      }
      return { clicked: false };
    });
    
    if (routerLinkClick.clicked) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      const finalUrl = page.url();
      console.log(`  ✅ Router link clicked: ${routerLinkClick.to}`);
      console.log(`  Final URL: ${finalUrl}`);
    } else {
      console.log('  ❌ No router-link elements found to click');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testSidebarVueRouter().catch(console.error);