import puppeteer from 'puppeteer';

async function testSPAAuthFlow() {
  console.log('=== Testing SPA Authentication Flow ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Capture console logs
    const logs = [];
    page.on('console', msg => {
      logs.push(`[${msg.type()}] ${msg.text()}`);
    });
    
    // Navigate to dashboard (should redirect to login if auth is enabled)
    console.log('1. Navigating to dashboard...');
    const response = await page.goto('http://0.0.0.0:5100/cs/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    console.log(`   Initial response: ${response.status()}`);
    console.log(`   Current URL: ${page.url()}`);
    
    // Check if redirected to login
    const currentPath = new URL(page.url()).pathname;
    const isRedirectedToLogin = currentPath.includes('/users/sign_in');
    
    console.log(`   Redirected to login: ${isRedirectedToLogin}`);
    
    // Check page content
    const pageContent = await page.content();
    const hasAppElement = pageContent.includes('id="app"');
    const hasLoginForm = pageContent.includes('sign_in') || pageContent.includes('password');
    
    console.log(`   Has Vue app element: ${hasAppElement}`);
    console.log(`   Has login form: ${hasLoginForm}`);
    
    // Print console logs
    if (logs.length > 0) {
      console.log('\n2. Console logs:');
      logs.forEach(log => console.log(`   ${log}`));
    }
    
    // Take screenshot
    await page.screenshot({ path: 'spa-auth-test.png' });
    console.log('\n3. Screenshot saved: spa-auth-test.png');
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
  }
}

testSPAAuthFlow().catch(console.error);