import puppeteer from 'puppeteer';

async function testUserAPI() {
  console.log('=== Testing User API Endpoint ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Login first
    console.log('Step 1: <NAME_EMAIL>...');
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    console.log('✅ Logged in successfully\n');
    
    // Test user API endpoint
    console.log('Step 2: Test /api/v1/user endpoint...');
    
    const apiResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/v1/user', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          credentials: 'same-origin'
        });
        
        const data = await response.json();
        return {
          status: response.status,
          data: data
        };
      } catch (error) {
        return {
          error: error.message
        };
      }
    });
    
    console.log('API Response:');
    console.log(JSON.stringify(apiResponse, null, 2));
    
    // Test subscription endpoint
    console.log('\nStep 3: Test /api/v1/subscription_status endpoint...');
    
    const subscriptionResponse = await page.evaluate(async () => {
      try {
        const response = await fetch('/api/v1/subscription_status', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          credentials: 'same-origin'
        });
        
        const data = await response.json();
        return {
          status: response.status,
          data: data
        };
      } catch (error) {
        return {
          error: error.message
        };
      }
    });
    
    console.log('Subscription Response:');
    console.log(JSON.stringify(subscriptionResponse, null, 2));
    
    // Check which company is currently active
    console.log('\nStep 4: Check current session info...');
    
    const sessionInfo = await page.evaluate(() => {
      return {
        url: window.location.href,
        cookies: document.cookie,
        userAgent: navigator.userAgent
      };
    });
    
    console.log('Session Info:');
    console.log(`URL: ${sessionInfo.url}`);
    console.log(`Cookies: ${sessionInfo.cookies}`);
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testUserAPI().catch(console.error);