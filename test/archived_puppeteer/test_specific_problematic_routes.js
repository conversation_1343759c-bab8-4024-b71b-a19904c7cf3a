import puppeteer from 'puppeteer';

async function testProblematicRoutes() {
  console.log('=== Testing Specific Problematic Routes ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Log redirects to see what's happening
    page.on('response', response => {
      if (response.status() >= 300 && response.status() < 400) {
        console.log(`REDIRECT: ${response.status()} ${response.url()} → ${response.headers()['location']}`);
      }
    });
    
    console.log('Step 1: Login...');
    
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    console.log('✅ Successfully logged in\n');
    
    // Test the specific routes that were failing
    const problematicRoutes = [
      { path: 'contracts', name: 'Contracts' },
      { path: 'companies', name: 'Companies' },
      { path: 'companies/1/edit', name: 'Company Edit' },
      { path: 'company_connections', name: 'Company Connections' },
      { path: 'company_settings/edit', name: 'Company Settings' }
    ];
    
    for (const route of problematicRoutes) {
      console.log(`\nTesting ${route.name}:`);
      
      // Test CS first (baseline)
      console.log(`  CS: /cs/${route.path}`);
      await page.goto(`http://0.0.0.0:5100/cs/${route.path}`, {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      const csUrl = page.url();
      const csSuccess = csUrl.includes(`/cs/${route.path.split('/')[0]}`);
      console.log(`    Result: ${csUrl} ${csSuccess ? '✅' : '❌'}`);
      
      // Test SK (problematic)
      console.log(`  SK: /sk/${route.path}`);
      await page.goto(`http://0.0.0.0:5100/sk/${route.path}`, {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      const skUrl = page.url();
      const skSuccess = skUrl.includes(`/sk/${route.path.split('/')[0]}`);
      console.log(`    Result: ${skUrl} ${skSuccess ? '✅' : '❌'}`);
      
      if (!skSuccess) {
        console.log(`    🔍 SK route failed - likely tenant/authorization issue`);
      }
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testProblematicRoutes().catch(console.error);