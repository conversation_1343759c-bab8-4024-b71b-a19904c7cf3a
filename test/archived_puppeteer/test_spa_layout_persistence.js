import puppeteer from 'puppeteer';

async function testLayoutPersistence() {
  console.log('=== Testing SPA Layout Persistence ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: false, // Show browser to observe transitions
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    slowMo: 1000 // Slow down to observe
  });
  
  try {
    const page = await browser.newPage();
    
    // Track layout element stability
    let layoutData = [];
    
    const checkLayout = async (routeName) => {
      // Check if sidebar and topbar exist and get their content
      const sidebarContent = await page.evaluate(() => {
        const sidebar = document.querySelector('.sidebar');
        return sidebar ? sidebar.textContent.substring(0, 100) : null;
      });
      
      const topbarContent = await page.evaluate(() => {
        const topbar = document.querySelector('.topbar');
        return topbar ? topbar.textContent.substring(0, 100) : null;
      });
      
      const mainContent = await page.evaluate(() => {
        const content = document.querySelector('.content-area');
        return content ? content.innerHTML.substring(0, 200) : null;
      });
      
      return {
        route: routeName,
        hasSidebar: !!sidebarContent,
        hasTopbar: !!topbarContent,
        sidebarStable: sidebarContent,
        topbarStable: topbarContent,
        contentChanged: mainContent
      };
    };
    
    // Test navigation between routes
    const routes = [
      { path: '/cs/dashboard', name: 'Dashboard' },
      { path: '/cs/events', name: 'Events' },
      { path: '/cs/bookings', name: 'Bookings' },
      { path: '/cs/companies', name: 'Companies' }
    ];
    
    console.log('Testing layout persistence across route changes...\n');
    
    for (const route of routes) {
      console.log(`Navigating to ${route.name}...`);
      
      await page.goto(`http://0.0.0.0:5100${route.path}`, {
        waitUntil: 'networkidle2',
        timeout: 30000
      });
      
      // Wait for layout to settle
      await page.waitForTimeout(2000);
      
      const layoutInfo = await checkLayout(route.name);
      layoutData.push(layoutInfo);
      
      console.log(`  Sidebar present: ${layoutInfo.hasSidebar}`);
      console.log(`  Topbar present: ${layoutInfo.hasTopbar}`);
      console.log(`  Content loaded: ${!!layoutInfo.contentChanged}`);
    }
    
    // Analyze layout persistence
    console.log('\n=== Layout Persistence Analysis ===');
    
    const firstSidebar = layoutData[0]?.sidebarStable;
    const firstTopbar = layoutData[0]?.topbarStable;
    
    let sidebarPersistent = true;
    let topbarPersistent = true;
    let contentChanges = true;
    
    for (let i = 1; i < layoutData.length; i++) {
      if (layoutData[i].sidebarStable !== firstSidebar) {
        sidebarPersistent = false;
      }
      if (layoutData[i].topbarStable !== firstTopbar) {
        topbarPersistent = false;
      }
    }
    
    // Check content actually changes
    const uniqueContent = new Set(layoutData.map(d => d.contentChanged));
    contentChanges = uniqueContent.size > 1;
    
    console.log(`✅ Sidebar persistent across routes: ${sidebarPersistent}`);
    console.log(`✅ Topbar persistent across routes: ${topbarPersistent}`);
    console.log(`✅ Content changes between routes: ${contentChanges}`);
    
    if (sidebarPersistent && topbarPersistent && contentChanges) {
      console.log('\n🎉 SPA Layout working correctly! Only content area reloads.');
    } else {
      console.log('\n⚠️  SPA Layout issues detected.');
    }
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
  }
}

testLayoutPersistence().catch(console.error);