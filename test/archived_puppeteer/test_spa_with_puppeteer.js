import puppeteer from 'puppeteer';

async function testSPARoutes() {
  console.log('=== Testing SPA Routes with Puppeteer ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Capture console logs
    const consoleLogs = [];
    page.on('console', msg => {
      consoleLogs.push({
        type: msg.type(),
        text: msg.text()
      });
    });
    
    // Capture errors
    const pageErrors = [];
    page.on('error', err => {
      pageErrors.push(err.message);
    });
    
    page.on('pageerror', err => {
      pageErrors.push(err.message);
    });
    
    // Test Dashboard Route
    console.log('1. Testing /cs/dashboard route...');
    const dashboardResponse = await page.goto('http://0.0.0.0:5100/cs/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    console.log(`   Status: ${dashboardResponse.status()}`);
    
    // Check if Vue app mounted
    const appElement = await page.$('#app');
    console.log(`   Vue app element found: ${appElement !== null}`);
    
    // Check for Vue Router
    const hasRouter = await page.evaluate(() => {
      return window.router !== undefined;
    });
    console.log(`   Vue Router available: ${hasRouter}`);
    
    if (hasRouter) {
      const currentRoute = await page.evaluate(() => {
        return window.router.currentRoute.value.path;
      });
      console.log(`   Current route: ${currentRoute}`);
    }
    
    // Take screenshot
    await page.screenshot({ path: 'dashboard-test.png' });
    console.log('   Screenshot saved: dashboard-test.png');
    
    // Print console logs
    if (consoleLogs.length > 0) {
      console.log('\n   Console logs:');
      consoleLogs.forEach(log => {
        console.log(`     [${log.type}] ${log.text}`);
      });
    }
    
    // Print errors
    if (pageErrors.length > 0) {
      console.log('\n   Page errors:');
      pageErrors.forEach(err => {
        console.log(`     ERROR: ${err}`);
      });
    }
    
    // Clear logs for next test
    consoleLogs.length = 0;
    pageErrors.length = 0;
    
    // Test Events Route
    console.log('\n2. Testing /cs/events route...');
    const eventsResponse = await page.goto('http://0.0.0.0:5100/cs/events', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    console.log(`   Status: ${eventsResponse.status()}`);
    
    await page.screenshot({ path: 'events-test.png' });
    console.log('   Screenshot saved: events-test.png');
    
    // Test non-existent route
    console.log('\n3. Testing /cs/nonexistent route (should 404)...');
    const notFoundResponse = await page.goto('http://0.0.0.0:5100/cs/nonexistent', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    console.log(`   Status: ${notFoundResponse.status()}`);
    
    console.log('\n=== Test Summary ===');
    console.log('Dashboard route:', dashboardResponse.status() === 200 ? '✓ PASS' : '✗ FAIL');
    console.log('Events route:', eventsResponse.status() === 200 ? '✓ PASS' : '✗ FAIL');
    console.log('404 route:', notFoundResponse.status() === 404 ? '✓ PASS' : '✗ FAIL');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the test
testSPARoutes().catch(console.error);