import puppeteer from 'puppeteer';

async function testSidebarNavigationComplete() {
  console.log('=== Testing Complete Sidebar Navigation Flow ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('Step 1: Login and navigate to SK dashboard...');
    
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    // Navigate to SK locale dashboard
    await page.goto('http://0.0.0.0:5100/sk/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    console.log(`✅ Starting at: ${page.url()}`);
    
    // Wait for Vue app to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\nStep 2: Test navigation flow through multiple pages...');
    
    const navigationTests = [
      { name: 'Events', urlPattern: '/sk/events', linkSelector: 'a[href*="/sk/events"]' },
      { name: 'Works', urlPattern: '/sk/works', linkSelector: 'a[href*="/sk/works"]' },
      { name: 'Bookings', urlPattern: '/sk/bookings', linkSelector: 'a[href*="/sk/bookings"]' },
      { name: 'Meetings', urlPattern: '/sk/meetings', linkSelector: 'a[href*="/sk/meetings"]' },
      { name: 'Dashboard', urlPattern: '/sk/', linkSelector: 'a[href*="/sk/"]:not([href*="/sk/"])' }
    ];
    
    let successCount = 0;
    
    for (const test of navigationTests) {
      console.log(`\nTesting ${test.name} navigation:`);
      
      try {
        // Click the navigation link
        await page.click(test.linkSelector);
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const currentUrl = page.url();
        const success = currentUrl.includes(test.urlPattern);
        
        if (success) {
          console.log(`  ✅ ${test.name}: ${currentUrl}`);
          successCount++;
          
          // Verify Vue Router state
          const routerState = await page.evaluate(() => {
            if (window.router && window.router.currentRoute) {
              return {
                path: window.router.currentRoute.value.path,
                locale: window.router.currentRoute.value.params?.locale
              };
            }
            return null;
          });
          
          if (routerState) {
            console.log(`     Router: ${routerState.path} (locale: ${routerState.locale})`);
          }
        } else {
          console.log(`  ❌ ${test.name}: Expected ${test.urlPattern}, got ${currentUrl}`);
        }
      } catch (error) {
        console.log(`  ❌ ${test.name}: Error clicking link - ${error.message}`);
      }
    }
    
    console.log('\nStep 3: Test locale preservation across navigation...');
    
    // Test that starting from EN locale preserves EN
    await page.goto('http://0.0.0.0:5100/en/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    try {
      await page.click('a[href*="/en/events"]');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const enUrl = page.url();
      const enSuccess = enUrl.includes('/en/events');
      
      console.log(`  ${enSuccess ? '✅' : '❌'} EN locale preservation: ${enUrl}`);
      
      if (enSuccess) {
        successCount++;
      }
    } catch (error) {
      console.log(`  ❌ EN locale test failed: ${error.message}`);
    }
    
    console.log(`\n=== Summary ===`);
    console.log(`Successful navigation tests: ${successCount}/${navigationTests.length + 1}`);
    console.log(`Success rate: ${Math.round((successCount / (navigationTests.length + 1)) * 100)}%`);
    
    if (successCount === navigationTests.length + 1) {
      console.log('🎉 All sidebar navigation tests passed!');
    } else {
      console.log('⚠️ Some navigation tests failed - check implementation');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testSidebarNavigationComplete().catch(console.error);