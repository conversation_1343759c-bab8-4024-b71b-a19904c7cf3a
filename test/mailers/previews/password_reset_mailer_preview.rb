# ABOUTME: Mailer preview for <PERSON><PERSON><PERSON><PERSON>t<PERSON>ail<PERSON> to visualize password reset emails
# ABOUTME: Provides preview functionality for the custom JWT-based password reset system

class PasswordResetMailerPreview < ActionMailer::Preview
  
  # Preview this email at http://192.168.1.51:5100/rails/mailers/password_reset_mailer/reset_instructions
  # Template at: /app/views/password_reset_mailer/reset_instructions.html.erb
  def reset_instructions
    # Create a dummy user with only required attributes
    user = User.new(email: "<EMAIL>")
    user.id = 123  # Set ID for logging purposes
    
    # Create a dummy user setting for language preference
    # Use locale from URL parameter or default to 'cs'
    locale = params[:locale] || 'cs'
    user_setting = UserSetting.new(language_code: locale)
    user.user_setting = user_setting
    
    # Generate a dummy reset token
    reset_token = "dummy_jwt_reset_token_123456789"
    
    PasswordResetMailer.reset_instructions(user, reset_token)
  end
end