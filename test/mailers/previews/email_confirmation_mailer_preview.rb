# ABOUTME: Mailer preview for <PERSON>ail<PERSON>on<PERSON>rma<PERSON><PERSON>ail<PERSON> to visualize confirmation emails
# ABOUTME: Provides preview functionality for the custom JWT-based email confirmation system

class EmailConfirmationMailerPreview < ActionMailer::Preview
  
  # Preview this email at http://192.168.1.51:5100/rails/mailers/email_confirmation_mailer/confirmation_instructions
  # Template at: /app/views/email_confirmation_mailer/confirmation_instructions.html.erb
  def confirmation_instructions
    # Create a dummy user with only required attributes
    user = User.new(email: "<EMAIL>")
    user.id = 123  # Set ID for logging purposes
    
    # Create a dummy user setting for language preference
    # Use locale from URL parameter or default to 'cs'
    locale = params[:locale] || 'cs'
    user_setting = UserSetting.new(language_code: locale)
    user.user_setting = user_setting
    
    # Generate a dummy confirmation token
    confirmation_token = "dummy_jwt_confirmation_token_123456789"
    
    EmailConfirmationMailer.confirmation_instructions(user, confirmation_token)
  end
end