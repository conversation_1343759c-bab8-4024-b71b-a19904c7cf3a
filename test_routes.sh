#!/bin/bash

# Simple route testing script
echo "=== Testing SPA Routes ==="
echo "Make sure Rails server is running on port 3000"
echo ""

# Test dashboard route
echo "1. Testing /cs/dashboard..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/cs/dashboard)
if [ "$response" = "200" ]; then
    echo "✓ Dashboard route: $response OK"
else
    echo "✗ Dashboard route: $response (Expected 200)"
fi

# Test events route
echo ""
echo "2. Testing /cs/events..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/cs/events)
if [ "$response" = "200" ]; then
    echo "✓ Events route: $response OK"
else
    echo "✗ Events route: $response (Expected 200)"
fi

# Test non-existent route
echo ""
echo "3. Testing /cs/nonexistent (should 404)..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/cs/nonexistent)
if [ "$response" = "404" ]; then
    echo "✓ Non-existent route: $response OK"
else
    echo "✗ Non-existent route: $response (Expected 404)"
fi

# Test API route
echo ""
echo "4. Testing API /api/v1/subscription_status..."
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/v1/subscription_status)
echo "API route: $response (401 is expected without auth)"

# Test HTML content
echo ""
echo "5. Checking HTML content of /cs/dashboard..."
content=$(curl -s http://localhost:3000/cs/dashboard | grep -c '<div id="app">')
if [ "$content" -gt 0 ]; then
    echo "✓ SPA mount point found"
else
    echo "✗ SPA mount point NOT found"
fi

echo ""
echo "=== Tests Complete ==="