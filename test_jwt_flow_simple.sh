#!/bin/bash

# JWT Authentication Flow Test Script
# Tests the complete JWT flow without requiring browser automation

set -e

BASE_URL="http://localhost:5100"
EMAIL="<EMAIL>"
PASSWORD="123456"
TEST_RESULTS=()

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting JWT Authentication Flow Test${NC}"
echo "=================================="

# Function to add test result
add_result() {
    local test_name="$1"
    local success="$2"
    local details="$3"
    
    if [ "$success" = "true" ]; then
        echo -e "${GREEN}✅ $test_name: $details${NC}"
        TEST_RESULTS+=("PASS: $test_name")
    else
        echo -e "${RED}❌ $test_name: $details${NC}"
        TEST_RESULTS+=("FAIL: $test_name")
    fi
}

# Test 1: JWT Login
echo -e "\n${YELLOW}Test 1: JWT Login${NC}"
JWT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/jwt_login" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -c cookies.txt \
    -d "{\"email\":\"$EMAIL\",\"password\":\"$PASSWORD\"}")

echo "JWT Login Response: $JWT_RESPONSE"

if echo "$JWT_RESPONSE" | grep -q '"success":true' && echo "$JWT_RESPONSE" | grep -q '"access_token"'; then
    ACCESS_TOKEN=$(echo "$JWT_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    add_result "JWT Login" "true" "Success - Token received"
else
    add_result "JWT Login" "false" "Failed - No access token received"
    exit 1
fi

# Test 2: Authenticated API Call
echo -e "\n${YELLOW}Test 2: Authenticated API Call${NC}"
USER_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/user" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Accept: application/json")

echo "User API Response: $USER_RESPONSE"

if echo "$USER_RESPONSE" | grep -q '"email"'; then
    add_result "Authenticated API Call" "true" "Success - User data retrieved"
else
    add_result "Authenticated API Call" "false" "Failed - No user data"
fi

# Test 3: Company List (for company switching test)
echo -e "\n${YELLOW}Test 3: Company List${NC}"
COMPANIES_RESPONSE=$(curl -s -X GET "$BASE_URL/api/v1/companies" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Accept: application/json")

echo "Companies Response: $COMPANIES_RESPONSE"

if echo "$COMPANIES_RESPONSE" | grep -q '"company_user_roles"'; then
    # Extract company IDs for switching test
    COMPANY_COUNT=$(echo "$COMPANIES_RESPONSE" | grep -o '"id":[0-9]*' | wc -l)
    if [ "$COMPANY_COUNT" -gt 1 ]; then
        SWITCH_TO_COMPANY=$(echo "$COMPANIES_RESPONSE" | grep -o '"id":[0-9]*' | head -2 | tail -1 | cut -d':' -f2)
        add_result "Company List" "true" "Success - $COMPANY_COUNT companies found, will test switch to company $SWITCH_TO_COMPANY"
        COMPANY_SWITCH_AVAILABLE=true
    else
        add_result "Company List" "true" "Success - Only 1 company, skipping switch test"
        COMPANY_SWITCH_AVAILABLE=false
    fi
else
    add_result "Company List" "false" "Failed - No companies data"
    COMPANY_SWITCH_AVAILABLE=false
fi

# Test 4: Company Switching (if multiple companies available)
if [ "$COMPANY_SWITCH_AVAILABLE" = "true" ]; then
    echo -e "\n${YELLOW}Test 4: Company Switching${NC}"
    SWITCH_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/companies/switch_company" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "{\"company_id\":$SWITCH_TO_COMPANY}")

    echo "Company Switch Response: $SWITCH_RESPONSE"

    if echo "$SWITCH_RESPONSE" | grep -q '"success":true' && echo "$SWITCH_RESPONSE" | grep -q '"access_token"'; then
        NEW_ACCESS_TOKEN=$(echo "$SWITCH_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        add_result "Company Switching" "true" "Success - New token received"
        ACCESS_TOKEN="$NEW_ACCESS_TOKEN"
    else
        add_result "Company Switching" "false" "Failed - No new access token"
    fi
else
    echo -e "\n${YELLOW}Test 4: Company Switching - SKIPPED (only one company)${NC}"
fi

# Test 5: Token Refresh
echo -e "\n${YELLOW}Test 5: Token Refresh${NC}"
REFRESH_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/refresh_token" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -b cookies.txt \
    -c cookies_new.txt)

echo "Token Refresh Response: $REFRESH_RESPONSE"

if echo "$REFRESH_RESPONSE" | grep -q '"success":true' && echo "$REFRESH_RESPONSE" | grep -q '"access_token"'; then
    REFRESHED_TOKEN=$(echo "$REFRESH_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    if [ "$REFRESHED_TOKEN" != "$ACCESS_TOKEN" ]; then
        add_result "Token Refresh" "true" "Success - New token generated and different from original"
    else
        add_result "Token Refresh" "false" "Token refresh returned same token"
    fi
else
    add_result "Token Refresh" "false" "Failed - No new access token from refresh"
fi

# Test 6: API Response Consistency Check
echo -e "\n${YELLOW}Test 6: API Response Consistency${NC}"
CONSISTENCY_ISSUES=0

# Check login response uses access_token
if ! echo "$JWT_RESPONSE" | grep -q '"access_token"'; then
    echo "❌ Login endpoint should return 'access_token' field"
    CONSISTENCY_ISSUES=$((CONSISTENCY_ISSUES + 1))
fi

# Check company switch uses access_token (if tested)
if [ "$COMPANY_SWITCH_AVAILABLE" = "true" ] && ! echo "$SWITCH_RESPONSE" | grep -q '"access_token"'; then
    echo "❌ Company switch endpoint should return 'access_token' field"
    CONSISTENCY_ISSUES=$((CONSISTENCY_ISSUES + 1))
fi

# Check refresh response uses access_token
if ! echo "$REFRESH_RESPONSE" | grep -q '"access_token"'; then
    echo "❌ Refresh endpoint should return 'access_token' field"
    CONSISTENCY_ISSUES=$((CONSISTENCY_ISSUES + 1))
fi

if [ $CONSISTENCY_ISSUES -eq 0 ]; then
    add_result "API Response Consistency" "true" "All endpoints use 'access_token' field"
else
    add_result "API Response Consistency" "false" "$CONSISTENCY_ISSUES inconsistencies found"
fi

# Test 7: JWT Logout
echo -e "\n${YELLOW}Test 7: JWT Logout${NC}"
LOGOUT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/auth/jwt_logout" \
    -H "Authorization: Bearer $REFRESHED_TOKEN" \
    -H "Accept: application/json" \
    -b cookies_new.txt)

echo "JWT Logout Response: $LOGOUT_RESPONSE"

if echo "$LOGOUT_RESPONSE" | grep -q '"success":true'; then
    add_result "JWT Logout" "true" "Success - Logout completed"
else
    add_result "JWT Logout" "false" "Failed - Logout failed"
fi

# Clean up
rm -f cookies.txt cookies_new.txt

# Summary
echo -e "\n${BLUE}📊 Test Summary${NC}"
echo "=================================="
TOTAL_TESTS=${#TEST_RESULTS[@]}
PASSED_TESTS=$(printf '%s\n' "${TEST_RESULTS[@]}" | grep -c "PASS:" || true)
FAILED_TESTS=$(printf '%s\n' "${TEST_RESULTS[@]}" | grep -c "FAIL:" || true)

echo -e "${GREEN}✅ Passed: $PASSED_TESTS${NC}"
echo -e "${RED}❌ Failed: $FAILED_TESTS${NC}"
echo -e "${BLUE}📋 Total: $TOTAL_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All JWT authentication tests passed!${NC}"
    echo -e "${GREEN}JWT-first authentication is working correctly.${NC}"
    exit 0
else
    echo -e "\n${RED}🚨 Some tests failed. Check the output above for details.${NC}"
    exit 1
fi