# ABOUTME: Migration to add missing visibility columns that exist in database but had no migration
# ABOUTME: These columns control team status visibility and work management access permissions
class AddMissingVisibilityColumnsToCompanySettings < ActiveRecord::Migration[7.0]
  def change
    # Simple Rails approach - these operations are safe for small tables
    add_column :company_settings, :show_managers_in_team_status, :boolean, default: true
    add_column :company_settings, :team_status_visibility, :integer, default: 1
    add_column :company_settings, :work_management_access, :integer, default: 1
  end
end