# Test Improvement and Security Hardening Plan

## 1. Introduction

This document outlines a strategic plan to enhance the testing suite for the AttendifyApp. The primary goal is to create a reliable, secure, and maintainable testing environment that supports the rapid development of a Minimum Viable Product (MVP) while establishing a strong foundation for future growth.

This plan is guided by the principle of using **real data instead of mocks** to ensure our tests accurately reflect the application's behavior. It places a strong emphasis on hardening security by thoroughly testing the JWT-based authentication, authorization policies, and critical user flows.

## 2. Guiding Principles

*   **No Mocks, Real Data:** All tests will run against a dedicated test database populated with realistic data. This avoids the brittleness of mocks and ensures tests are a true representation of the system.
*   **Security First:** The highest priority will be given to tests covering authentication, authorization, multi-tenancy, and other security-critical areas.
*   **Pragmatic for MVP:** We will focus on high-impact tests that provide the most value, ensuring we don't over-engineer the testing suite at the expense of development velocity.
*   **Data-Driven Testing:** We will create a robust set of seed data to represent various user roles and scenarios, making it easy to write tests for complex authorization and business logic.
*   **Developer Experience:** The testing suite should be easy for any developer to run, understand, and contribute to.

## 3. Phased Implementation Plan

This plan is divided into three phases, starting with foundational improvements and progressively moving towards more comprehensive test coverage.

---

### **Phase 1: Foundational Improvements (Configuration & Data)**

This phase focuses on fixing configuration issues and establishing a solid data strategy, which is critical for mock-free testing.

#### **Task 1.1: Unify and Configure the Testing Environment**

1.  **Consolidate Test Directories:**
    *   Move the Playwright tests from `test/e2e` to a new `spec/e2e` directory. This will unify all tests under the `spec` folder, which is standard for Rails applications.
    *   Update the `testDir` in `playwright.config.ts` to point to `'./spec/e2e'`.
2.  **Fix Playwright Configuration:**
    *   In `playwright.config.ts`, change the hardcoded `baseURL` from `'http://************:5100'` to `'http://localhost:5100'`. Allow this to be overridden by an environment variable for flexibility in CI.
    *   Enable the `webServer` command in `playwright.config.ts` to automatically start the Rails server before running E2E tests. This streamlines the process and prevents errors.
3.  **Implement a Robust Database Strategy:**
    *   Add the `database_cleaner-active_record` gem to the `Gemfile`.
    *   Configure it in `spec/rails_helper.rb` to use the `truncation` strategy for JavaScript-driven tests (feature specs and E2E tests). This ensures a clean state for tests that run in a separate browser thread.
    *   Continue using the faster `transaction` strategy for all other RSpec tests.

#### **Task 1.2: Develop a Comprehensive Test Data Seeding Strategy**

1.  **Create a Test Seed System:**
    *   Create a new directory: `db/seeds/test/`.
    *   Develop a set of focused seed files within this directory to be loaded before the test suite runs.
2.  **Define Core Seed Files:**
    *   **`01_roles_and_permissions.rb`:** Seeds the database with foundational data like user roles (`owner`, `employee`, `admin`).
    *   **`02_core_scenarios.rb`:** Creates a standard set of users and companies that will be available in every test run. This should include:
        *   An **Admin User** (`<EMAIL>`) in an admin-designated company.
        *   A **Company Owner** with full control over a "Primary Test Company".
        *   An **Employee User** within the "Primary Test Company".
        *   A **Multi-Company User** who belongs to two or more companies.
        *   An **Invited/Unconfirmed User** who has not yet accepted an invitation.
        *   A **User in a different, "Isolated Test Company"** to test authorization boundaries.
    *   **`03_policy_and_security_scenarios.rb`:** Creates specific data setups required for testing complex authorization policies. For example, create a `Work` record owned by the "Primary Test Company" that the "Isolated" user should not be able to access.
3.  **Load Seeds in `rails_helper`:**
    *   Modify `spec/rails_helper.rb` to load these seed files once before the entire test suite runs. This ensures all tests start with a consistent and predictable database state.

---

### **Phase 2: Refactoring and Hardening Security Tests**

With a solid foundation, we can now focus on improving the quality and coverage of our security-related tests.

#### **Task 2.1: Refactor Brittle and Inconsistent Tests**

1.  **Refactor Model Specs:**
    *   Rewrite the `admin_user?` tests in `spec/models/user_spec.rb` to use the seeded Admin User instead of relying on hardcoded IDs and emails.
2.  **Refactor Feature Specs:**
    *   Modify `spec/features/invitation_login_workflow_spec.rb` to simulate real user behavior. Instead of calling the `InvitationService` directly, the test should visit the invitation URL, fill out the registration form, and then attempt to log in.
3.  **Refactor Controller Specs:**
    *   Update `spec/controllers/application_controller_dual_auth_spec.rb` to test the controller's public interface. Instead of calling the private `require_login` method, make a request to a protected action and assert that the response is correct (e.g., a redirect or a 401 Unauthorized status).

#### **Task 2.2: Build a Comprehensive E2E Security Testing Suite (Playwright)**

Create a new Playwright test file, `spec/e2e/security_flow.spec.ts`, that uses the seeded data to perform end-to-end validation of all critical security flows:

*   **Authentication:**
    *   Successful login and redirection for each user role (owner, employee).
    *   Login failure with an incorrect password.
    *   Login failure for an unconfirmed user.
    *   Full user invitation and registration flow, culminating in a successful login.
*   **JWT Lifecycle:**
    *   Verify that accessing a protected API route succeeds with a valid JWT token.
    *   Verify that accessing a protected API route fails with an invalid or expired token.
    *   Test the JWT refresh mechanism.
    *   Test the logout process, ensuring the JWT can no longer be used.
*   **Multi-Tenancy:**
    *   Log in as the **User in "Isolated Test Company"** and attempt to access URLs and API endpoints related to the "Primary Test Company". All attempts should be denied.

#### **Task 2.3: Implement Granular Authorization Policy Testing (RSpec)**

1.  **Introduce `pundit-rspec`:** Add the `pundit-rspec` gem to simplify testing of your authorization policies.
2.  **Create Policy Specs:** For each policy in `app/policies`, create a corresponding spec file in `spec/policies`.
3.  **Write Data-Driven Policy Tests:** Use the seeded users to write clear and expressive tests for each policy rule. For example, in `spec/policies/work_policy_spec.rb`:
    ```ruby
    describe WorkPolicy do
      subject { described_class }

      permissions :update?, :edit?, :destroy? do
        it "denies access if user is an employee" do
          expect(subject).not_to permit(seeded_employee_user, work_record)
        end

        it "grants access if user is the company owner" do
          expect(subject).to permit(seeded_company_owner, work_record)
        end
      end
    end
    ```

---

### **Phase 3: Best Practices and Documentation**

This final phase focuses on long-term maintainability.

#### **Task 3.1: Create a Project Testing Guide**

Create a `TESTING_GUIDE.md` file in the `docs/` directory. This document will be the source of truth for all testing-related practices and should include:

*   An overview of the testing strategy (RSpec, Playwright, Data-Driven).
*   Clear instructions on how to run each part of the test suite.
*   A guide to the test seed data: what's available and how to add new scenarios.
*   The project's official stance on "No Mocks".
*   Best practices for writing new tests, ensuring consistency across the team.

## 4. Conclusion

By executing this plan, we will transform the AttendifyApp's testing suite into a powerful asset. It will provide strong security assurances, increase developer confidence, and enable the team to build and iterate on the MVP quickly and safely. This investment in quality will pay dividends in application stability and maintainability.
