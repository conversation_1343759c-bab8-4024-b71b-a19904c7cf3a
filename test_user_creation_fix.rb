# ABOUTME: Rails console script to test personal workspace creation fix
# ABOUTME: Verifies that new users get primary_company properly loaded

# Run this in Rails console: rails runner test_user_creation_fix.rb

puts "=== Testing User Creation with Personal Workspace ==="
puts ""

# Create a test user
test_email = "test_user_#{Time.now.to_i}@example.com"
puts "Creating user: #{test_email}"

user = User.new(
  email: test_email,
  password: 'Test123456!',
  password_confirmation: 'Test123456!'
)

if user.save
  puts "✓ User created with ID: #{user.id}"
  
  # Check if personal workspace was created
  puts "\nBefore reload:"
  puts "  primary_company: #{user.primary_company.inspect}"
  puts "  company_user_roles count: #{user.company_user_roles.count}"
  
  # Now reload the user (this is what the fix does)
  user.reload
  
  puts "\nAfter reload:"
  puts "  primary_company: #{user.primary_company.inspect}"
  puts "  primary_company.id: #{user.primary_company&.id}"
  puts "  primary_company.name: #{user.primary_company&.name}"
  puts "  company_user_roles count: #{user.company_user_roles.count}"
  
  # Verify the personal workspace
  if user.primary_company
    puts "\n✅ SUCCESS: User has primary company after reload!"
    puts "  Company ID: #{user.primary_company.id}"
    puts "  Company Name: #{user.primary_company.name}"
    puts "  Is Personal: #{user.primary_company.is_personal}"
    
    # Check JWT payload
    jwt_payload = user.jwt_payload
    puts "\nJWT Payload would include:"
    puts "  user_id: #{jwt_payload[:user_id]}"
    puts "  email: #{jwt_payload[:email]}"
    
    # Check what handle_successful_jwt_login would return
    puts "\nIn JWT login response, company_id would be: #{user.primary_company&.id}"
  else
    puts "\n❌ FAILURE: User still has no primary company after reload!"
    puts "This means the fix didn't work or there's another issue."
  end
  
  # Clean up test user
  puts "\nCleaning up test data..."
  user.company_user_roles.destroy_all
  user.primary_company&.destroy if user.primary_company&.is_personal
  user.destroy
  puts "✓ Test user cleaned up"
else
  puts "✗ Failed to create user: #{user.errors.full_messages.join(', ')}"
end

puts "\n=== Test Complete ===\n"