# Works and Service Contracts Role-Based Access Control Implementation

## Project Overview
**Objective**: Restrict works and service contracts management to owners/supervisors/admins only, while allowing employees read-only access plus indirect work status management via activities.

**Estimated Time**: 6-9 hours coding + 1-3 hours debugging = 7-12 total hours (1-2 days)

## Current System Analysis

### Authorization Infrastructure
- **ActionPolicy gem** with policy-based authorization
- **Multi-tenant architecture** with ActsAsTenant
- **JWT authentication** with company context
- **Roles**: owner, employee, supervisor, admin
- **Existing patterns**: Some components already check `isManager`

### Current Access Control
**WorkPolicy** (`app/policies/work_policy.rb`):
- Line 28-30: `manage_works?` allows ALL roles: `["owner", "employee", "supervisor", "admin"]`
- Line 19-21: `access_works?` allows same roles
- Lines 39-51: All CRUD methods use `manage_works?`

**CompanyPolicy** (`app/policies/company_policy.rb`):
- Line 156-158: `manage_works?` allows ALL roles including employees

**Controllers using `manage_works?`**:
- `WorksController` line 85 (create), line 155 (update)
- `Api::V1::WorksController` line 9 (index), line 122 (create)
- `Api::V1::ServiceContractsController` lines 9, 30, 51, 63, 73

### Frontend Components
**WorksIndex.vue**:
- Lines 15-17: "New Service Contract" button
- Lines 18-20: "New Work" button  
- Lines 140-143: "Add Work" button in works section
- Lines 154-157: "Create First Work" button

**WorkShow.vue**:
- Lines 8-26: Dropdown menu with actions
- Line 23: Already has `v-if="isManager"` for some actions
- Lines 17-21: Edit action

**Permission Integration**:
- `app/controllers/api/v1/users_controller.rb` line 22-28: Defines user permissions
- Need to add `can_manage_works` and `can_view_works` permissions

## Implementation Plan

### Phase 1: Backend Policy Changes (2-3 hours)

#### 1.1 Update WorkPolicy (`app/policies/work_policy.rb`)
```ruby
# ADD: New read-only access method
def view_works?
  has_any_role?("owner", "employee", "supervisor", "admin")
end

# UPDATE: Restrict management to non-employees (line 28-30)
def manage_works?
  has_any_role?("owner", "supervisor", "admin") # Remove "employee"
end

# UPDATE: Change show method (line 23-25)
def show?
  view_works? # Changed from access_works?
end

# Keep create?, update?, destroy? using manage_works?
```

#### 1.2 Update CompanyPolicy (`app/policies/company_policy.rb`)
```ruby
# UPDATE: Line 156-158
def manage_works?
  user_role&.name.in?(["owner", "supervisor", "admin"]) # Remove "employee"
end

# ADD: New method
def view_works?
  user_role&.name.in?(["owner", "employee", "supervisor", "admin"])
end
```

#### 1.3 Update Controller Authorization

**WorksController** (`app/controllers/works_controller.rb`):
```ruby
# UPDATE: show action (around line 74-81)
def show
  authorize! @company, to: :view_works? # Changed from @work authorization
  # Keep existing JSON response logic
end
# Keep create/update/destroy using manage_works?
```

**Api::V1::WorksController** (`app/controllers/api/v1/works_controller.rb`):
```ruby
# UPDATE: Line 9
def index
  authorize! @company, to: :view_works? # Changed from manage_works?
end

# ADD: show action if missing
def show
  authorize! @company, to: :view_works?
end

# Keep create/update/destroy/reschedule/force_close_activities using manage_works?
```

**Api::V1::ServiceContractsController** (`app/controllers/api/v1/service_contracts_controller.rb`):
```ruby
# UPDATE: Line 9
def index
  authorize! @company, to: :view_works? # Changed
end

# UPDATE: Line 30
def show
  authorize! @company, to: :view_works? # Changed
end

# Keep create/update/destroy using manage_works?
```

#### 1.4 Add User Permissions API
**Update** `app/controllers/api/v1/users_controller.rb` (line 22-28):
```ruby
permissions = {
  can_manage_contracts: allowed_to?(:manage_contract?, @company),
  can_manage_works: allowed_to?(:manage_works?, @company), # ADD
  can_view_works: allowed_to?(:view_works?, @company), # ADD
  can_view_owner_section: allowed_to?(:view_owner_section?, @company),
  # ... existing permissions
}
```

### Phase 2: Work Status Transition (45 minutes)

#### 2.1 Find Activity Creation Endpoint
**Trace the flow**:
1. `TodayWorkSection.vue` → `quickStartWork` method (line 586)
2. Calls `startActivity` with `work_id` and `activity_type`
3. Find backend endpoint (likely `DailyActivitiesController`)

#### 2.2 Add Status Transition Logic
```ruby
# In activity creation method (find the correct controller/service)
if activity.work_id.present? && activity.work.status == 'scheduled'
  # Only update if user is assigned to this work
  if activity.work.work_assignments.exists?(contract: current_user_contract)
    activity.work.update!(status: 'in_progress')
  end
end
```

**Critical**: 
- Get `current_user_contract` or equivalent
- Validate work assignment before status change
- Only transition from 'scheduled' to 'in_progress'
- Don't break existing activity tracking

### Phase 3: Frontend Permission Integration (3-4 hours)

#### 3.1 Find Permission Storage Pattern
**Investigate**:
- How existing permissions like `can_manage_contracts` are stored/accessed
- Likely in Vuex store or component props
- Ensure `can_manage_works` follows same pattern

#### 3.2 Update WorksIndex.vue
```vue
<!-- ADD: Computed property -->
<script>
computed: {
  canManageWorks() {
    // Follow existing permission pattern
    return this.$store.state.user?.permissions?.can_manage_works || false;
  }
}

<!-- UPDATE: Header buttons (lines 15-20) -->
<button 
  v-if="canManageWorks" 
  @click="showServiceContractForm = true" 
  class="btn btn-outline">
  {{ $t('works.new_service_contract', 'Nová zakázka') }}
</button>

<button 
  v-if="canManageWorks" 
  @click="showWorkForm = true" 
  class="btn btn-primary">
  {{ $t('works.new_work', 'Nová práce') }}
</button>

<!-- UPDATE: Work creation buttons (lines 140-143, 154-157) -->
<button 
  v-if="canManageWorks" 
  @click="createWorkForContract" 
  class="btn btn-small">
  <Plus :size="16" />
  {{ $t('works.add_work', 'Přidat práci') }}
</button>
</script>
```

#### 3.3 Update WorkShow.vue
```vue
<script>
computed: {
  canManageWorks() {
    return this.$store.state.user?.permissions?.can_manage_works || false;
  },
  isManager() {
    // Verify this aligns with new policy
    return this.canManageWorks;
  }
}
</script>

<!-- UPDATE: Edit action (lines 17-21) -->
<a href="#"
   v-if="canManageWorks"
   class="dropdown-item"
   @click.prevent="editWork">
  <Clipboard :size="12" class="inline mr-2" />
  {{ $t('edit', 'Upravit') }}
</a>
```

#### 3.4 Form Access Control
**WorksIndex.vue modal section** (lines 192-208):
```vue
<!-- UPDATE: Work form modal -->
<div v-if="showWorkForm && canManageWorks" class="modal-overlay">
  <!-- Existing modal content -->
</div>

<!-- ADD: Unauthorized message -->
<div v-if="showWorkForm && !canManageWorks" class="modal-overlay">
  <div class="modal-container">
    <div class="modal-header">
      <h3>{{ $t('access_denied', 'Přístup odepřen') }}</h3>
      <button @click="showWorkForm = false" class="close-btn">×</button>
    </div>
    <div class="central-modal-content">
      <p>{{ $t('works.no_permission', 'Nemáte oprávnění vytvářet práce') }}</p>
    </div>
  </div>
</div>

<!-- Same pattern for service contract form -->
```

## Critical Dependencies & Gotchas

### Backend Dependencies
1. **Policy method caching**: `user_roles_in_company` (WorkPolicy line 8-12) - ensure performance
2. **Tenant scoping**: All authorization must respect `ActsAsTenant.current_tenant`
3. **Work assignment validation**: Only assigned employees can trigger status changes
4. **Activity system**: Don't break existing `DailyActivity` and `WorkSession` tracking

### Frontend Dependencies  
1. **Permission propagation**: JWT → Controllers → Frontend storage → Components
2. **Existing `isManager` logic**: Verify alignment with new `can_manage_works`
3. **Modal state management**: Forms must handle permission-based access
4. **Component reactivity**: Permission changes reflected immediately

### Integration Points
1. **Activity creation flow**: `TodayWorkSection.vue` → Backend → Status transition
2. **Work assignment relationship**: `WorkAssignment` model (lines 8-9) validation
3. **JSON responses**: Ensure appropriate data for employees vs managers
4. **JWT token refresh**: Permission changes may require token update

## Testing Requirements

### Backend Testing
- [ ] Each role's access to each endpoint (owner/supervisor/admin: full, employee: read-only)
- [ ] Work status transitions only for assigned employees
- [ ] Cross-tenant access prevention
- [ ] Policy method caching works correctly

### Frontend Testing  
- [ ] Buttons show/hide correctly for each role
- [ ] Forms accessible only to authorized users
- [ ] Read-only views work for employees
- [ ] Permission changes reflected immediately
- [ ] No broken functionality for any role

### Edge Cases
- [ ] Unassigned work status changes (should fail)
- [ ] Work already in progress (don't re-transition)
- [ ] Multiple employees on same work
- [ ] JWT permission caching issues
- [ ] Company switching permission updates

## Key Files to Modify

### Backend
- `app/policies/work_policy.rb` (lines 19-21, 28-30, 23-25)
- `app/policies/company_policy.rb` (lines 156-158, add view_works?)
- `app/controllers/works_controller.rb` (line 75)
- `app/controllers/api/v1/works_controller.rb` (line 9, add show)
- `app/controllers/api/v1/service_contracts_controller.rb` (lines 9, 30)
- `app/controllers/api/v1/users_controller.rb` (lines 22-28)
- Activity creation controller (find and update)

### Frontend
- `app/frontend/components/works/WorksIndex.vue` (lines 15-20, 140-143, 154-157, 192-208)
- `app/frontend/components/works/WorkShow.vue` (lines 17-21, verify isManager)
- Permission storage/access pattern (investigate and implement)

## No Database Changes Required
- Existing role system (`CompanyUserRole`, `Role` models) supports this
- Work status enum already includes needed states
- `WorkAssignment` relationships already exist
- Multi-tenant scoping already implemented

## Implementation Order
1. **Backend policies and controllers** (can work independently)
2. **User permissions API** (required for frontend)
3. **Frontend permission integration** (depends on #2)
4. **Activity status transition** (depends on #1)
5. **Testing and validation** (after all components)

This plan provides all technical details, exact line numbers, code examples, dependencies, and gotchas needed for successful implementation.

## Additional Technical Details

### Current System Deep Dive

#### WorkAssignment Model Analysis (`app/models/work_assignment.rb`)
```ruby
# Lines 1-31: Key relationships and validations
belongs_to :work
belongs_to :contract
belongs_to :company
has_many :daily_activities
has_many :work_sessions

# Line 16: Critical validation
validates :contract_id, uniqueness: { scope: :work_id }

# Lines 18-30: Deletion protection
before_destroy :check_for_related_activities
```

#### Work Model Analysis (`app/models/work.rb`)
```ruby
# Lines 24-31: Status enum
enum status: {
  scheduled: "scheduled",
  in_progress: "in_progress",
  completed: "completed",
  cancelled: "cancelled",
  rescheduled: "rescheduled",
  unprocessed: "unprocessed"
}

# Lines 9-12: Key relationships
has_many :work_assignments, dependent: :destroy
has_many :contracts, through: :work_assignments
has_many :work_sessions, dependent: :destroy
has_many :daily_activities, dependent: :nullify
```

#### ServiceContract Model Analysis (`app/models/service_contract.rb`)
```ruby
# Lines 7-10: Relationships
has_many :works, dependent: :destroy
has_many :work_assignments, through: :works
has_many :work_sessions, through: :works

# Lines 16-22: Status enum (same as Work)
enum status: { scheduled: "scheduled", in_progress: "in_progress", completed: "completed", cancelled: "cancelled", unprocessed: "unprocessed" }

# Lines 25-34: Time calculation methods
def total_time_spent_by_user(user)
  total_seconds = work_sessions.where(user: user).sum { |ws| ws.duration || 0 }
  (total_seconds.to_f / 60).round
end
```

### Activity System Deep Analysis

#### TodayWorkSection.vue Activity Flow
```javascript
// Lines 586-611: quickStartWork method
async quickStartWork(work) {
  if (this.currentWorkActivity && work.id === this.currentWorkActivity.work_id) {
    return; // Already working on this
  }

  const success = await this.startActivity({
    work_id: work.id,
    activity_type: 'work_at_location'
  });

  if (success) {
    // Add work to today's list if not already there
    const workExists = this.assignedWorks.find(w => w.id === work.id);
    if (!workExists) {
      this.assignedWorks.unshift(work);
    }
  }
}

// Lines 482-503: fetchDailyState method
async fetchDailyState() {
  const response = await axios.get('/daily_logs/current_status');
  const { last_log, activities } = response.data;

  this.allActivities = activities || [];

  if (last_log && !last_log.end_time) {
    this.currentDailyLog = last_log;
    this.isWorking = true;
  }
}
```

#### DailyActivity Model Integration Points
**Find these controllers/endpoints**:
- Daily logs controller handling `/daily_logs/current_status`
- Activity creation endpoint called by `startActivity`
- Look for `DailyActivitiesController` or similar

### Authorization Pattern Analysis

#### Current Permission Usage Examples
```javascript
// From app/controllers/api/v1/users_controller.rb (lines 22-28)
permissions = {
  can_manage_contracts: allowed_to?(:manage_contract?, @company),
  can_view_owner_section: allowed_to?(:view_owner_section?, @company),
  can_receive_team_status_emails?: allowed_to?(:receive_team_status_emails?, @company),
  can_manage_logo: allowed_to?(:manage_logo?, @company),
  can_manage_work_scheduling: allowed_to?(:manage_work_scheduling?, @company),
  can_view_all_company_events: allowed_to?(:view_all_company_events?, @company),
}
```

#### Policy Inheritance Pattern
```ruby
# ApplicationPolicy base class (app/policies/application_policy.rb)
authorize :current_jwt_user, optional: true
authorize :jwt_company_id, optional: true

# Helper methods available in all policies
def jwt_authenticated?
  authorization_context[:current_jwt_user].present?
end

def current_tenant
  ActsAsTenant.current_tenant
end

def tenant_matches_jwt?
  jwt_company_id = authorization_context[:jwt_company_id]
  current_tenant&.id == jwt_company_id
end
```

### Frontend Permission Integration Patterns

#### Existing Permission Usage in Components
**Find these patterns in the codebase**:
- How `can_manage_contracts` is accessed in Vue components
- Vuex store structure for user permissions
- Component prop passing for permissions
- Permission reactivity patterns

#### Component State Management
```javascript
// Pattern to investigate and follow
computed: {
  userPermissions() {
    return this.$store.state.user?.permissions || {};
  },
  canManageContracts() {
    return this.userPermissions.can_manage_contracts || false;
  }
}
```

### Critical Implementation Notes

#### Work Status Transition Rules
```ruby
# Implement in activity creation controller
def create_activity_with_work_transition
  # Create the activity first
  activity = current_company.daily_activities.build(activity_params)

  if activity.save
    # Handle work status transition
    if activity.work_id.present?
      work = activity.work
      user_contract = current_user.contracts.find_by(company: current_company)

      # Only transition if:
      # 1. Work is scheduled
      # 2. User is assigned to work
      # 3. This is a work activity (not break/travel)
      if work.status == 'scheduled' &&
         work.work_assignments.exists?(contract: user_contract) &&
         activity.activity_type.in?(['work_at_location', 'work_remote'])

        work.update!(status: 'in_progress')
      end
    end

    render json: activity
  else
    render json: { errors: activity.errors }, status: :unprocessable_entity
  end
end
```

#### Multi-Tenant Security Validation
```ruby
# Ensure all work-related queries are tenant-scoped
# In controllers, verify these patterns:

# CORRECT: Tenant-scoped queries
@company.works.find(params[:id])
@company.service_contracts.includes(:works)
current_user.contracts.where(company: @company)

# INCORRECT: Direct model access
Work.find(params[:id])  # Could access other tenants' data
ServiceContract.all     # Not tenant-scoped
```

### Performance Optimization Notes

#### Policy Method Caching
```ruby
# In WorkPolicy, optimize role checking
def user_roles_in_company
  @user_roles ||= user.company_user_roles.joins(:role)
                       .where(company: company)
                       .pluck('roles.name')
end

# Cache company reference
def company
  @company ||= record.company
end
```

#### Frontend Permission Caching
```javascript
// Avoid repeated permission API calls
// Cache permissions in Vuex store
// Use computed properties for reactive permission checks
// Consider permission change events for real-time updates
```

### Error Handling & User Experience

#### Backend Error Responses
```ruby
# Consistent error responses for unauthorized access
rescue ActionPolicy::Unauthorized
  render json: {
    success: false,
    error: I18n.t('errors.unauthorized'),
    code: 'UNAUTHORIZED_ACCESS'
  }, status: :forbidden
end
```

#### Frontend Error Handling
```vue
<!-- Graceful degradation for missing permissions -->
<template>
  <div v-if="loading">Loading...</div>
  <div v-else-if="!canViewWorks">
    <p>{{ $t('works.no_access', 'Nemáte přístup k pracím') }}</p>
  </div>
  <div v-else>
    <!-- Normal works interface -->
  </div>
</template>
```

### Testing Strategy Details

#### Backend Integration Tests
```ruby
# Test policy integration
describe 'WorkPolicy' do
  let(:company) { create(:company) }
  let(:owner) { create(:user_with_owner_role, company: company) }
  let(:employee) { create(:user_with_employee_role, company: company) }
  let(:work) { create(:work, company: company) }

  it 'allows owners to manage works' do
    expect(WorkPolicy.new(owner, work).manage_works?).to be true
  end

  it 'denies employees work management' do
    expect(WorkPolicy.new(employee, work).manage_works?).to be false
  end

  it 'allows employees to view works' do
    expect(WorkPolicy.new(employee, work).view_works?).to be true
  end
end

# Test controller authorization
describe 'Api::V1::WorksController' do
  context 'as employee' do
    it 'allows index access' do
      get :index
      expect(response).to have_http_status(:ok)
    end

    it 'denies create access' do
      post :create, params: { work: work_params }
      expect(response).to have_http_status(:forbidden)
    end
  end
end
```

#### Frontend Component Tests
```javascript
// Test permission-based rendering
describe('WorksIndex.vue', () => {
  it('hides create buttons for employees', () => {
    const wrapper = mount(WorksIndex, {
      store: createStore({
        state: {
          user: {
            permissions: { can_manage_works: false }
          }
        }
      })
    });

    expect(wrapper.find('[data-test="new-work-button"]').exists()).toBe(false);
  });

  it('shows create buttons for managers', () => {
    const wrapper = mount(WorksIndex, {
      store: createStore({
        state: {
          user: {
            permissions: { can_manage_works: true }
          }
        }
      })
    });

    expect(wrapper.find('[data-test="new-work-button"]').exists()).toBe(true);
  });
});
```

This comprehensive technical documentation provides all the deep analysis, exact implementation details, code examples, testing strategies, and edge cases needed for successful implementation of the role-based access control system.
