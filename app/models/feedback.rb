class Feedback < ApplicationRecord
  # Not using acts_as_tenant to allow anonymous (no-tenant) records safely

  belongs_to :company, optional: true
  belongs_to :user, optional: true

  enum category: { bug_report: 0, something_confusing: 1, missing_feature: 2, general_message: 3 }
  enum status: { new_: 0, reviewed: 1, useful: 2, duplicate: 3, noise: 4 }

  validates :page_url, presence: true
  validates :category, presence: true
  validates :message, presence: true, length: { maximum: 300 }
  validates :user_email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
end

