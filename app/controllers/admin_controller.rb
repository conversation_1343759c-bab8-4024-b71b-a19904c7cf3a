# ABOUTME: AdminController provides server-rendered admin interface outside SPA
# ABOUTME: Implements secure admin access with hardcoded company IDs and email/profile validation
class AdminController < ApplicationController
  # Skip SPA layout and use admin layout
  layout 'admin'
  
  # Admin-specific authentication and authorization
  before_action :ensure_admin_access
  before_action :disable_tenant_scoping
  
  # GET /admin
  # GET /:locale/admin
  def index
    redirect_to admin_dashboard_path(locale: params[:locale] || I18n.locale)
  end
  
  # GET /admin/dashboard
  # GET /:locale/admin/dashboard
  def dashboard
    @stats = calculate_dashboard_stats
    @recent_companies = recent_companies_data
  end
  
  # GET /admin/companies
  # GET /:locale/admin/companies
  def companies
    @companies = load_companies_data
    @total_companies = @companies.count
  end
  
  # GET /admin/companies/:id
  # GET /:locale/admin/companies/:id
  def show_company
    @company = Company.includes(company_user_roles: [:user, :role, { user: :user_profile }]).find(params[:id])
    @owner = find_company_owner(@company)
    @employees = @company.contracts.includes(:user).order('contracts.first_name, contracts.last_name')
    @subscription = @company.current_subscription
    @plan = @company.current_plan
  end
  
  # GET /admin/subscriptions
  # GET /:locale/admin/subscriptions
  def subscriptions
    @subscriptions = load_subscriptions_data
    @total_subscriptions = @subscriptions.count
  end

  # GET /admin/subscriptions/new
  # GET /:locale/admin/subscriptions/new
  def new_subscription
    @company_id = params[:company_id]
    @company = Company.find(@company_id) if @company_id
    @plans = Plan.active.order(:name)
    @subscription = Subscription.new
  end

  # POST /admin/subscriptions
  # POST /:locale/admin/subscriptions
  def create_subscription
    @subscription = Subscription.new(subscription_params)
    @plans = Plan.active.order(:name)

    if @subscription.save
      redirect_to admin_companies_path(locale: params[:locale] || I18n.locale),
                  notice: 'Subscription created successfully.'
    else
      @company = @subscription.company
      render :new_subscription
    end
  end

  # GET /admin/subscriptions/:id/edit
  # GET /:locale/admin/subscriptions/:id/edit
  def edit_subscription
    @subscription = Subscription.find(params[:id])
    @company = @subscription.company
    @plans = Plan.active.order(:name)
  end

  # PATCH /admin/subscriptions/:id
  # PATCH /:locale/admin/subscriptions/:id
  def update_subscription
    @subscription = Subscription.find(params[:id])
    @plans = Plan.active.order(:name)

    if @subscription.update(subscription_params)
      redirect_to admin_companies_path(locale: params[:locale] || I18n.locale),
                  notice: 'Subscription updated successfully.'
    else
      @company = @subscription.company
      render :edit_subscription
    end
  end

  # DELETE /admin/subscriptions/:id
  # DELETE /:locale/admin/subscriptions/:id
  def destroy_subscription
    @subscription = Subscription.find(params[:id])
    @subscription.destroy
    redirect_to admin_companies_path(locale: params[:locale] || I18n.locale),
                notice: 'Subscription deleted successfully.'
  end
  
  private
  
  def ensure_admin_access
    unless current_user&.admin_user?
      Rails.logger.warn "Admin access denied for user: #{current_user&.id || 'anonymous'}"
      
      respond_to do |format|
        format.html do
          flash[:error] = t('admin.access_denied', default: 'Access denied. Admin privileges required.')
          redirect_to root_path
        end
        format.json do
          render json: { 
            error: 'Access denied',
            message: 'Admin access required'
          }, status: :forbidden
        end
      end
    end
  end
  
  def disable_tenant_scoping
    # Admin operations should see all data across tenants
    ActsAsTenant.current_tenant = nil
  end
  
  def calculate_dashboard_stats
    {
      total_companies: Company.count,
      active_subscriptions: Subscription.where(status: 'active').count,
      trial_subscriptions: Subscription.where(status: 'trialing').count,
      total_users: User.count,
      total_contracts: Contract.count,
      recent_registrations: User.where('created_at > ?', 7.days.ago).count
    }
  end
  
  def recent_companies_data
    Company.includes({ subscriptions: :plan }, company_user_roles: [:user, :role, { user: :user_profile }])
           .order(created_at: :desc)
           .limit(5)
           .map do |company|
      # Use the already loaded associations to avoid N+1 queries
      owner_role = company.company_user_roles.find { |cur| cur.role.name == 'owner' }
      owner = owner_role&.user
      
      # Find current subscription in memory to avoid N+1 queries
      current_subscription = company.subscriptions.select(&:active?).max_by(&:expire_date)

      {
        company: company,
        owner: owner,
        employee_count: company.contracts_count, # Use counter cache
        subscription: current_subscription,
        plan: current_subscription&.plan
      }
    end
  end
  
  def load_companies_data
    Company.includes({ subscriptions: :plan }, company_user_roles: [:user, :role, { user: :user_profile }])
           .order(:name)
           .map do |company|
      owner_role = company.company_user_roles.find { |cur| cur.role.name == 'owner' }
      owner = owner_role&.user
      
      # Find current subscription in memory to avoid N+1 queries
      current_subscription = company.subscriptions.select(&:active?).max_by(&:expire_date)

      {
        id: company.id,
        name: company.name,
        created_at: company.created_at,
        owner: owner ? {
          id: owner.id,
          email: owner.email,
          name: "#{owner.user_profile&.first_name} #{owner.user_profile&.last_name}".strip
        } : nil,
        employee_count: company.contracts_count, # Use counter cache
        subscription: current_subscription,
        plan: current_subscription&.plan
      }
    end
  end
  
  def find_company_owner(company)
    owner_role = company.company_user_roles.find { |cur| cur.role.name == 'owner' }
    owner_role&.user
  end

  def load_subscriptions_data
    Subscription.includes(:company, :plan)
               .order(created_at: :desc)
               .map do |subscription|
      {
        id: subscription.id,
        company: subscription.company,
        plan: subscription.plan,
        status: subscription.status,
        start_date: subscription.start_date,
        expire_date: subscription.expire_date,
        is_trial: subscription.is_trial,
        created_at: subscription.created_at
      }
    end
  end

  def subscription_params
    params.require(:subscription).permit(:company_id, :plan_id, :start_date, :expire_date, :status, :is_trial)
  end
end
