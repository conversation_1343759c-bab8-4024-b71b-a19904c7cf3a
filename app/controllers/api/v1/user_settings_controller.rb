# ABOUTME: API controller for user settings management with JWT authentication
# ABOUTME: Provides endpoints for fetching and updating user-specific preferences and visibility settings

class Api::V1::UserSettingsController < Api::V1::ApiController
  
  include ActionPolicy::Controller

  before_action :set_user_setting

  authorize :user, through: :current_user

  def show
    # Return current user settings including visibility preferences
    render json: { 
      user_setting: user_setting_json,
      permissions: {
        can_customize_visibility: can_customize_visibility?
      }
    }
  end

  def update
    # Update user settings including visibility preferences
    if @user_setting.update(user_setting_params)
      render json: { 
        success: true, 
        user_setting: user_setting_json,
        message: I18n.t('controllers.user_settings.messages.updated', default: 'Settings updated successfully') 
      }
    else
      render json: { 
        errors: @user_setting.errors.full_messages 
      }, status: :unprocessable_entity
    end
  end

  private

  def set_user_setting
    @user_setting = current_user.user_setting || current_user.create_user_setting
  end

  def user_setting_json
    @user_setting.as_json(
      only: [
        :id, :start_time, :end_time, :break_start, :break_duration,
        :auto_break, :timezone, :country_code, :language, :language_code
      ],
      methods: [:show_works_menu, :show_bookings_menu, :show_meetings_menu, :show_daily_logs]
    )
  end

  def user_setting_params
    # Only allow visibility preferences for managers
    if can_customize_visibility?
      params.require(:user_setting).permit(
        :start_time, :end_time, :break_start, :break_duration,
        :auto_break, :timezone, :country_code, :language, :language_code,
        :hide_works_menu, :hide_bookings_menu, :hide_meetings_menu, :hide_daily_logs
      )
    else
      params.require(:user_setting).permit(
        :start_time, :end_time, :break_start, :break_duration,
        :auto_break, :timezone, :country_code, :language, :language_code
      )
    end
  end

  def can_customize_visibility?
    # Only managers (owner, admin, supervisor) can customize visibility
    current_user.role_in(current_tenant)&.name.in?(['owner', 'admin', 'supervisor'])
  end
end