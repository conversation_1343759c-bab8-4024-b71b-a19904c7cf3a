# ABOUTME: API controller for company settings management with JWT authentication
# ABOUTME: Provides endpoints for fetching and updating company-wide configuration settings

class Api::V1::CompanySettingsController < Api::V1::ApiController
  
  include ActionPolicy::Controller

  # JWT authentication and company context handled by <PERSON>piController
  before_action :set_company_setting

  authorize :user, through: :current_user

  def show
    # Return current company settings
    authorize! @company_setting.company, to: :manage?
    
    render json: { company_setting: company_setting_json }
  end

  def update
    # Update company settings
    authorize! @company_setting.company, to: :manage?

    if @company_setting.update(company_setting_params)
      render json: { 
        success: true, 
        company_setting: company_setting_json,
        message: I18n.t('controllers.company_settings.messages.updated', default: 'Company settings updated successfully') 
      }
    else
      render json: { 
        errors: @company_setting.errors.full_messages 
      }, status: :unprocessable_entity
    end
  end

  private

  def set_company_setting
    unless current_tenant
      render json: { 
        error: 'No company context available',
        message: 'Please select a company or refresh your authentication'
      }, status: :unauthorized
      return
    end
    
    @company_setting = current_tenant.company_setting
  end

  def company_setting_json
    @company_setting.as_json(only: [
      :id, :break_duration, :auto_end, :auto_break, :allow_overtime,
      :timezone, :approve_vacations, :daily_team_reports, :show_works_menu,
      :show_bookings_menu, :show_meetings_menu
    ])
  end

  def company_setting_params
    params.require(:company_setting).permit(
      :break_duration,
      :auto_end,
      :auto_break,
      :allow_overtime,
      :timezone,
      :approve_vacations,
      :daily_team_reports,
      :show_works_menu,
      :show_bookings_menu,
      :show_meetings_menu
    )
  end
end