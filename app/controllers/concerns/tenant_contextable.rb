# ABOUTME: Establishes tenant context from X-Company-ID header or legacy JWT payload
# ABOUTME: Validates user has access to the requested company before setting context

module TenantContextable
  extend ActiveSupport::Concern

  private

  # Main method to establish tenant context for the request
  # Called AFTER authentication has identified the user
  def establish_tenant_context
    return unless @current_jwt_user

    company_id = extract_company_id_from_request
    return unless company_id

    company = validate_user_has_access(company_id, @current_jwt_user)
    return unless company

    ActsAsTenant.current_tenant = company
    Rails.logger.debug "[TENANT] Context established: company_id=#{company.id} for user_id=#{@current_jwt_user.id}"
    true
  end

  # Extract company ID from various sources in priority order
  def extract_company_id_from_request
    # Priority 1: X-Company-ID header (new standard way)
    if request.headers['X-Company-ID'].present?
      Rails.logger.debug "[TENANT] Using company_id from X-Company-ID header"
      return request.headers['X-Company-ID']
    end

    # Priority 2: JWT payload company_id (legacy, with deprecation warning)
    if @jwt_payload&.dig('company_id').present?
      Rails.logger.warn "[DEPRECATION] company_id in JWT payload is deprecated. Use X-Company-ID header instead."
      return @jwt_payload['company_id']
    end

    # Priority 3: Route parameter for nested resources (e.g., /companies/:company_id/resources)
    if params[:company_id].present? && !switching_company?
      Rails.logger.debug "[TENANT] Using company_id from route parameter"
      return params[:company_id]
    end

    nil
  end

  # Validate that the user has access to the requested company
  def validate_user_has_access(company_id, user)
    company = Company.find_by(id: company_id)
    
    unless company
      Rails.logger.warn "[TENANT] Company not found: company_id=#{company_id}"
      return nil
    end

    # Check if user has a role in this company
    unless user.company_user_roles.exists?(company: company)
      Rails.logger.warn "[SECURITY] User #{user.id} attempted to access company #{company_id} without authorization"
      return nil
    end

    company
  rescue => e
    Rails.logger.error "[TENANT] Error validating company access: #{e.message}"
    nil
  end

  # Check if we're in the process of switching companies
  # (to avoid using route param when explicitly switching)
  def switching_company?
    controller_name == 'companies' && action_name == 'switch_company'
  end

  # Clear tenant context (useful for logout or error scenarios)
  def clear_tenant_context
    ActsAsTenant.current_tenant = nil
    Rails.logger.debug "[TENANT] Context cleared"
  end

  # Check if tenant context is required for current action
  def tenant_context_required?
    # Override in controllers to specify tenant-agnostic actions
    true
  end

  # Legacy support: Alias for migration period
  # TODO: Remove after all references updated
  def set_tenant_from_jwt(payload, user)
    @jwt_payload = payload
    @current_jwt_user = user
    establish_tenant_context
  end
end