# ABOUTME: Service to generate company-wide monthly ServiceContract summaries for owners and managers
# ABOUTME: Provides aggregated time tracking and project overview across all ServiceContracts and their Works

class ServiceContractSummaryService
  def self.generate_summary(company, year = nil, month = nil)
    # Overall view by default, monthly when year/month provided
    if year && month
      # Monthly logic (commented out for overall view but kept for future use)
      start_date = Date.new(year, month, 1)
      end_date = start_date.end_of_month
      time_filter = start_date.beginning_of_day..end_date.end_of_day
    else
      # Overall view - no time filtering
      time_filter = nil
    end
    
    # Fetch all service contracts for the company with related data
    service_contracts = company.service_contracts
                               .includes(:works, work_sessions: [:user])
    
    # Bulk fetch work sessions (all time or filtered by month)
    all_work_sessions_query = WorkSession.includes(:work, user: :user_profile)
                                         .joins(work: :service_contract)
                                         .where(service_contracts: { company_id: company.id })
    
    if time_filter
      all_work_sessions = all_work_sessions_query.where(start_time: time_filter)
    else
      all_work_sessions = all_work_sessions_query # All work sessions for overall view
    end
    
    # Group work sessions by service_contract_id for O(1) lookup
    work_sessions_by_contract = all_work_sessions.group_by { |session| session.work.service_contract_id }
    
    # Group work sessions by work_id for Works details
    work_sessions_by_work = all_work_sessions.group_by(&:work_id)
    
    # Process each service contract with pre-fetched, grouped data
    contract_summaries = service_contracts.map do |contract|
      contract_work_sessions = work_sessions_by_contract[contract.id] || []
      calculate_contract_summary(contract, time_filter, contract_work_sessions, work_sessions_by_work)
    end
    
    # Calculate overall summary stats
    summary_stats = calculate_summary_stats(contract_summaries, all_work_sessions)
    
    {
      service_contracts: contract_summaries,
      summary_stats: summary_stats
    }
  end
  
  private
  
  def self.calculate_contract_summary(contract, time_filter, work_sessions, work_sessions_by_work)
    total_time_seconds = work_sessions.sum { |ws| ws.duration || 0 }
    works_count = contract.works.count
    completed_works = contract.works.where(status: 'completed').count
    
    # Find last activity from work sessions
    last_activity = work_sessions.map(&:start_time).max
    
    # Calculate Works details for this contract
    works_details = contract.works.map do |work|
      work_work_sessions = work_sessions_by_work[work.id] || []
      work_total_time = work_work_sessions.sum { |ws| ws.duration || 0 }
      
      # Get unique users working on this work
      work_users = work_work_sessions.map(&:user).uniq.compact.map do |user|
        user_sessions = work_work_sessions.select { |ws| ws.user_id == user.id }
        user_time = user_sessions.sum { |ws| ws.duration || 0 }
        
        # Build name from user profile or fallback to email
        user_name = if user.user_profile&.first_name.present? || user.user_profile&.last_name.present?
          [user.user_profile.first_name, user.user_profile.last_name].compact.join(' ').strip
        else
          user.email
        end
        
        {
          id: user.id,
          name: user_name,
          time_seconds: user_time,
          sessions_count: user_sessions.length
        }
      end
      
      {
        id: work.id,
        title: work.title,
        description: work.description,
        status: work.status,
        location: work.location,
        scheduled_start_date: work.scheduled_start_date,
        total_time_seconds: work_total_time,
        sessions_count: work_work_sessions.length,
        users: work_users,
        created_at: work.created_at,
        updated_at: work.updated_at
      }
    end
    
    {
      id: contract.id,
      title: contract.title,
      description: contract.description,
      status: contract.status,
      client_name: contract.client_id ? "Client ##{contract.client_id}" : '-',
      total_time_seconds: total_time_seconds,
      works_count: works_count,
      completed_works: completed_works,
      work_sessions_count: work_sessions.length,
      last_activity: last_activity,
      works: works_details
    }
  end
  
  def self.calculate_summary_stats(contract_summaries, all_work_sessions)
    total_contracts = contract_summaries.length
    total_time_seconds = contract_summaries.sum { |c| c[:total_time_seconds] }
    
    status_distribution = contract_summaries.group_by { |c| c[:status] }
                                           .transform_values(&:count)
    
    # Calculate work status distribution
    all_works = contract_summaries.flat_map { |c| c[:works] }
    work_status_distribution = all_works.group_by { |w| w[:status] }
                                       .transform_values(&:count)
    
    # Calculate top performing contracts by time
    top_contracts = contract_summaries
                    .select { |c| c[:total_time_seconds] > 0 }
                    .sort_by { |c| -c[:total_time_seconds] }
                    .first(5)
                    .map { |c| { title: c[:title], time_seconds: c[:total_time_seconds] } }
    
    {
      total_contracts: total_contracts,
      total_time_seconds: total_time_seconds,
      total_sessions: all_work_sessions.length,
      status_distribution: status_distribution,
      work_status_distribution: work_status_distribution,
      total_works: contract_summaries.sum { |c| c[:works_count] },
      completed_works: contract_summaries.sum { |c| c[:completed_works] },
      top_contracts: top_contracts
    }
  end
  
end