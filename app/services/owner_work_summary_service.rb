# ABOUTME: Service to generate company-wide monthly work summaries for owners
# ABOUTME: Extends existing ReportDataService logic to include weekend and holiday days worked

class OwnerWorkSummaryService
  extend HolidayFetcher
  
  def self.generate_summary(company, year, month)
    start_date = Date.new(year, month, 1)
    end_date = start_date.end_of_month
    
    holidays = fetch_holidays(year, month)
    holiday_dates = holidays.map { |date_str| Date.parse(date_str) }.to_set
    
    # Fetch all contracts for the company
    contracts = company.contracts.includes(:user)
    
    # Bulk fetch all daily_logs for the month with breaks (single query)
    all_daily_logs = DailyLog.includes(:breaks)
      .joins(:contract)
      .where(contracts: { company_id: company.id })
      .where(start_time: start_date.beginning_of_day..end_date.end_of_day)
    
    # Group daily logs by contract_id for O(1) lookup
    daily_logs_by_contract = all_daily_logs.group_by(&:contract_id)
    
    # Bulk fetch all events for the month (single query)
    all_events = Event.joins(:contract)
      .where(contracts: { company_id: company.id })
      .where(start_time: start_date.beginning_of_day..end_date.end_of_day)
      .where(event_type: [:vacation, :illness, :family_sick, :day_care, :other])
    
    # Group events by contract_id for O(1) lookup
    events_by_contract = all_events.group_by(&:contract_id)
    
    # Process each contract with pre-fetched, grouped data
    contracts.map do |contract|
      contract_daily_logs = daily_logs_by_contract[contract.id] || []
      contract_events = events_by_contract[contract.id] || []
      
      calculate_employee_summary(contract, start_date, end_date, holiday_dates, contract_daily_logs, contract_events)
    end
  end
  
  private
  
  def self.calculate_employee_summary(contract, start_date, end_date, holiday_dates, monthly_logs_array, monthly_events_array)
    # Convert pre-fetched logs array to hash indexed by date for O(1) lookup
    monthly_logs = monthly_logs_array.index_by { |log| log.start_time.to_date }

    # Use pre-fetched events array directly
    monthly_events = monthly_events_array

    total_worked_hours = 0
    days_worked = 0
    weekend_days_worked = 0
    holiday_days_worked = 0
    
    event_counts = {
      'vacation' => 0,
      'illness' => 0,
      'day_care' => 0,
      'family_sick' => 0,
      'other' => 0
    }

    # Calculate event days (same logic as ReportDataService)
    monthly_events.each do |event|
      event_type = event.event_type.to_s
      next unless event_counts.key?(event_type)
      
      start_event_date = [event.start_time.to_date, start_date].max
      end_event_date = [event.end_time.to_date, end_date].min
      
      working_days = (start_event_date..end_event_date).count do |date| 
        (1..5).include?(date.wday) && !holiday_dates.include?(date) && !monthly_logs.key?(date)
      end
      
      event_counts[event_type] += working_days
    end

    # Process each day of the month
    (start_date..end_date).each do |date|
      log_data = monthly_logs[date]
      next unless log_data

      # Calculate worked hours (same logic as ReportDataService)
      breaks = log_data.breaks.first
      break_duration = breaks&.duration || 0
      net_duration = [(log_data.duration || 0) - break_duration, 0].max
      total_worked_hours += net_duration

      days_worked += 1

      # Count weekend days worked
      if date.saturday? || date.sunday?
        weekend_days_worked += 1
      end

      # Count holiday days worked  
      if holiday_dates.include?(date)
        holiday_days_worked += 1
      end
    end

    {
      employee_id: contract.id,
      employee_name: [contract.first_name, contract.last_name].compact.join(' '),
      employee_email: contract.email,
      total_hours: total_worked_hours,
      days_worked: days_worked,
      weekend_days_worked: weekend_days_worked,
      holiday_days_worked: holiday_days_worked,
      event_counts: event_counts,
      last_activity: monthly_logs.values.map(&:start_time).max
    }
  end
end