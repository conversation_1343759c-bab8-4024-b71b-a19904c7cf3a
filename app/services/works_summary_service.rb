# ABOUTME: Service to generate company-wide monthly Works summaries for owners and managers  
# ABOUTME: Provides detailed work-level time tracking and activity overview across all Works in ServiceContracts

class WorksSummaryService
  def self.generate_summary(company, year, month)
    start_date = Date.new(year, month, 1)
    end_date = start_date.end_of_month
    
    # Fetch all works for the company with related data
    works = Work.includes(:service_contract, :work_sessions, :daily_activities)
               .joins(:service_contract)
               .where(service_contracts: { company_id: company.id })
    
    # Bulk fetch all work sessions for the month (single query)
    all_work_sessions = WorkSession.includes(:work, :user)
                                   .joins(work: :service_contract)
                                   .where(service_contracts: { company_id: company.id })
                                   .where(start_time: start_date.beginning_of_day..end_date.end_of_day)
    
    # Bulk fetch all daily activities for the month (single query)
    all_daily_activities = DailyActivity.includes(:work, :contract)
                                       .joins(work: :service_contract)
                                       .where(service_contracts: { company_id: company.id })
                                       .where(start_time: start_date.beginning_of_day..end_date.end_of_day)
    
    # Group by work_id for O(1) lookup
    work_sessions_by_work = all_work_sessions.group_by(&:work_id)
    activities_by_work = all_daily_activities.group_by(&:work_id)
    
    # Process each work with pre-fetched, grouped data
    work_summaries = works.map do |work|
      work_sessions = work_sessions_by_work[work.id] || []
      activities = activities_by_work[work.id] || []
      calculate_work_summary(work, start_date, end_date, work_sessions, activities)
    end
    
    # Calculate overall summary stats
    {
      works: work_summaries,
      summary_stats: calculate_summary_stats(work_summaries)
    }
  end
  
  private
  
  def self.calculate_work_summary(work, start_date, end_date, work_sessions, activities)
    total_time_seconds = work_sessions.sum { |ws| ws.duration || 0 }
    
    # Find last activity from work sessions or daily activities
    last_session_time = work_sessions.map(&:start_time).max
    last_activity_time = activities.map(&:start_time).max
    last_activity = [last_session_time, last_activity_time].compact.max
    
    {
      id: work.id,
      title: work.title,
      service_contract_title: work.service_contract.title,
      service_contract_id: work.service_contract.id,
      status: work.status,
      total_time_seconds: total_time_seconds,
      work_sessions_count: work_sessions.length,
      daily_activities_count: activities.length,
      last_activity: last_activity
    }
  end
  
  def self.calculate_summary_stats(work_summaries)
    total_works = work_summaries.length
    total_time_seconds = work_summaries.sum { |w| w[:total_time_seconds] }
    
    status_distribution = work_summaries.group_by { |w| w[:status] }
                                       .transform_values(&:count)
    
    {
      total_works: total_works,
      total_time_seconds: total_time_seconds,
      status_distribution: status_distribution,
      total_work_sessions: work_summaries.sum { |w| w[:work_sessions_count] },
      total_activities: work_summaries.sum { |w| w[:daily_activities_count] }
    }
  end
end