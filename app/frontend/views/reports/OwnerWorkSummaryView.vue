// ABOUTME: Owner work summary view - wrapper for the OwnerWorkSummary component
// ABOUTME: Provides route-level component for displaying team monthly work statistics

<template>
  <div class="owner-work-summary-view">
    <OwnerWorkSummary />
  </div>
</template>

<script>
import OwnerWorkSummary from '../../components/reports/OwnerWorkSummary.vue';

export default {
  name: 'OwnerWorkSummaryView',
  components: {
    OwnerWorkSummary
  }
};
</script>

<style scoped>
.owner-work-summary-view {
  width: 100%;
  height: 100%;
}
</style>