// ABOUTME: ServiceContract summary view wrapper - renders ServiceContractSummary component
// ABOUTME: Provides full page layout for company-wide ServiceContract overview and analytics

<template>
  <div class="service-contract-summary-view">
    <ServiceContractSummary />
  </div>
</template>

<script>
import ServiceContractSummary from '../../components/reports/ServiceContractSummary.vue';

export default {
  name: 'ServiceContractSummaryView',
  components: {
    ServiceContractSummary
  }
};
</script>

<style scoped>
.service-contract-summary-view {
  width: 100%;
  height: 100%;
}
</style>