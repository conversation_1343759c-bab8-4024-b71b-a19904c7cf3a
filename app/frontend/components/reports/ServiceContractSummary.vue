<template>
  <div class="service-contract-summary">
    <!-- Header -->
    <div class="summary-header">
      <h1 class="text-2xl md:text-3xl font-bold text-gray-900">
        {{ $t('service_contract_summary.title', '<PERSON><PERSON><PERSON><PERSON> firemní<PERSON>') }}
      </h1>
      <!-- Monthly selector commented out - overall view is default now -->
      <!-- 
      <div class="header-actions">
        <select :value="getCurrentMonthValue()" @change="onMonthSelect($event.target.value)" class="month-selector">
          <option value="" disabled selected>{{ $t('select_month', 'Vyberte měsíc') }}</option>
          <option v-for="month in availableMonths" 
                  :key="month.value" 
                  :value="month.value">
            {{ month.label }}
          </option>
        </select>
      </div>
      -->
    </div>

    <!-- Main Layout -->
    <div class="contracts-layout">
      <!-- Service Contracts Sidebar -->
      <div class="service-contracts-sidebar">
        <div v-if="isLoading" class="sidebar-loading">
          <Clock :size="20" class="animate-spin text-gray-500" />
          <span class="text-sm text-gray-500">{{ $t('loading', 'Načítání...') }}</span>
        </div>

        <div v-else-if="summaryData.length === 0" class="sidebar-empty">
          <div class="empty-contracts-content">
            <FileText :size="32" class="text-gray-300 mb-2" />
            <p class="text-sm text-gray-500 mb-3">{{ $t('service_contract_summary.no_data', 'Žádná data pro vybraný měsíc') }}</p>
          </div>
        </div>

        <div v-else class="contracts-list">
          <div
            v-for="contract in summaryData"
            :key="contract.id"
            @click="selectContract(contract)"
            :class="['contract-item', { 'active': selectedContract?.id === contract.id }]"
          >
            <div class="contract-header">
              <h3 class="contract-title">{{ contract.title }}</h3>
              <span class="badge" :class="getStatusBadgeClass(contract.status)">
                {{ $t(`works.status.${contract.status}`, contract.status) }}
              </span>
            </div>
            <div class="contract-meta">
              <div class="contract-time">
                <Clock :size="12" class="text-gray-400" />
                <span>{{ formatHours(contract.total_time_seconds) }}</span>
              </div>
              <div class="contract-client" v-if="contract.client_name">
                <span class="text-gray-500">{{ contract.client_name }}</span>
              </div>
            </div>
            <div v-if="contract.works && contract.works.length > 0" class="contract-works-count">
              {{ contract.works.length }} {{ contract.works.length === 1 ? 'práce' : 'prací' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="main-content-area">
        <div v-if="!selectedContract" class="empty-state">
          <div class="empty-state-content">
            <FileText :size="48" class="text-gray-300" />
            <h3 class="text-lg font-medium text-gray-600">{{ $t('works.select_contract', 'Vyberte kontrakt') }}</h3>
            <p class="text-gray-500">{{ $t('works.select_contract_desc', 'Vyberte kontrakt ze seznamu pro zobrazení detailů a prací') }}</p>
          </div>
        </div>

        <div v-else class="contract-details">
          <!-- Contract Header -->
          <div class="contract-details-header">
            <div class="contract-info">
              <h2 class="contract-details-title">{{ selectedContract.title }}</h2>
              <div v-if="selectedContract.client_name" class="contract-details-meta">
                <span class="text-muted">{{ selectedContract.client_name }}</span>
              </div>
              <!-- Works List for Easy Copy -->
              <div v-if="selectedContract.works && selectedContract.works.length > 0" class="works-copy-list">
                <table class="works-copy-table">
                  <tr v-for="work in selectedContract.works" :key="work.id">
                    <td class="work-copy-title">{{ work.title }}</td>
                    <td class="work-copy-hours">{{ formatHours(work.total_time_seconds) }}</td>
                  </tr>
                </table>
                <div class="contract-total-time">
                  <span class="total-label">Celkem:</span>
                  <span class="total-hours">{{ formatHours(selectedContract.total_time_seconds) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Works List -->
          <div class="works-section">
            <h3 class="section-title">{{ $t('works_details', 'Detail prací') }}</h3>
            <div v-if="selectedContract.works && selectedContract.works.length > 0" class="works-grid">
              <div v-for="work in selectedContract.works" :key="work.id" class="work-card">
                <div class="work-card-header">
                  <h4 class="work-title">{{ work.title }}</h4>
                  <span class="badge badge-sm" :class="getStatusBadgeClass(work.status)">
                    {{ $t(`works.status.${work.status}`, work.status) }}
                  </span>
                </div>
                <div class="work-description">
                  <span v-if="work.location">{{ work.location }}</span><span v-if="work.location && work.scheduled_start_date"> </span><span v-if="work.scheduled_start_date">{{ formatDate(work.scheduled_start_date) }}</span><span v-if="(work.location || work.scheduled_start_date) && work.description"> </span><span v-if="work.description">{{ work.description }}</span>
                </div>
                <div class="work-users">
                  <div class="users-list">
                    <div class="user-item total-item">
                      <span class="user-name">Celkem:</span>
                      <span class="user-time">{{ formatHours(work.total_time_seconds) }}</span>
                    </div>
                    <div v-for="user in work.users" :key="user.id" class="user-item">
                      <span class="user-name">{{ user.name }}</span>
                      <span class="user-time">{{ formatHours(user.time_seconds) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="no-works">
              {{ $t('no_works_in_contract', 'Žádné práce v tomto kontraktu') }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Summary Stats -->
    <div v-if="summaryData.length > 0" class="summary-stats">
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">{{ $t('total_contracts', 'Celkem kontraktů') }}:</span>
          <span class="stat-value">{{ summaryStats.total_contracts || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">{{ $t('total_time_spent', 'Celkem času') }}:</span>
          <span class="stat-value">{{ formatHours(summaryStats.total_time_seconds || 0) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { Clock, FileText } from 'lucide-vue-next';
import { sendFlashMessage } from '@/utils/flashMessage';
import dayjs from 'dayjs';

export default {
  name: 'ServiceContractSummary',
  components: {
    Clock,
    FileText
  },
  data() {
    // Monthly logic commented out - overall view is default now
    // const persistedDate = localStorage.getItem('serviceContractSummary_lastViewedMonth');
    // const selectedDate = persistedDate ? dayjs(persistedDate) : dayjs();
    
    return {
      summaryData: [],
      summaryStats: {},
      isLoading: false,
      // Monthly fields commented out
      // currentMonth: '',
      // availableMonths: [],
      // currentYear: selectedDate.year(),
      // currentMonthNumber: selectedDate.month() + 1,
      companyName: '',
      selectedContract: null
    };
  },
  async mounted() {
    // Monthly initialization commented out - overall view is default now
    // this.initializeMonths();
    // await this.fetchSummaryData(this.currentYear, this.currentMonthNumber);
    await this.fetchSummaryData(); // Overall data without month/year filter
  },
  methods: {
    async fetchSummaryData(year, month) {
      this.isLoading = true;
      
      try {
        // Overall view - no monthly filtering
        const params = year && month ? { year, month } : {}; // Support both overall and monthly (when called with params)
        console.log('🔍 DEBUG ServiceContract API Call parameters:', params);
        
        const response = await axios.get('/api/v1/daily_logs/service_contract_summary', {
          params: params,
          headers: { 'Accept': 'application/json' }
        });
        
        console.log('🔍 DEBUG ServiceContract API Response:', response.data);
        
        this.summaryData = response.data.service_contracts || [];
        this.summaryStats = response.data.summary_stats || {};
        this.companyName = response.data.company_name || '';
        
        // Monthly logic commented out
        // this.currentYear = year;
        // this.currentMonthNumber = month;
        // this.updateCurrentMonth(year, month);
        
      } catch (error) {
        console.error('Error fetching service contract summary data:', error);
        if (error.response?.status === 403) {
          sendFlashMessage(
            this.$t('unauthorized_access', 'Nemáte oprávnění zobrazit tyto údaje'),
            'error'
          );
        } else {
          sendFlashMessage(
            error.response?.data?.message || this.$t('service_contract_summary.error_loading', 'Chyba při načítání přehledu'),
            'error'
          );
        }
      } finally {
        this.isLoading = false;
      }
    },
    
    // Monthly methods commented out - overall view is default now
    /*
    initializeMonths() {
      const months = [];
      let date = dayjs();
      
      // Show last 12 months using dayjs
      for(let i = 0; i < 12; i++) {
        months.unshift({
          value: `${date.year()}-${date.month() + 1}`,
          label: date.toDate().toLocaleString(this.$i18n.locale, { month: 'long', year: 'numeric' })
        });
        date = date.subtract(1, 'month');
      }
      
      this.availableMonths = months;
    },
    
    getCurrentMonthValue() {
      if (!this.currentYear || !this.currentMonthNumber) {
        const now = dayjs();
        return `${now.year()}-${now.month() + 1}`;
      }
      return `${this.currentYear}-${this.currentMonthNumber}`;
    },
    
    async onMonthSelect(value) {
      if (!value) return;
      
      const [year, month] = value.split('-').map(Number);
      
      // Persist selected month to localStorage
      const selectedDate = dayjs().year(year).month(month - 1).startOf('month');
      localStorage.setItem('serviceContractSummary_lastViewedMonth', selectedDate.toISOString());
      
      await this.fetchSummaryData(year, month);
    },
    
    updateCurrentMonth(year, month) {
      const date = dayjs().year(year).month(month - 1).date(1);
      this.currentMonth = date.toDate().toLocaleDateString(this.$i18n.locale, { month: 'long', year: 'numeric' });
    },
    */
    
    formatHours(totalSeconds) {
      if (!totalSeconds) return '0:00';
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.round((totalSeconds % 3600) / 60);
      return `${hours}:${minutes.toString().padStart(2, '0')}`;
    },
    
    formatDate(dateString) {
      if (!dateString) return '-';
      return dayjs(dateString).format('DD.MM.YYYY');
    },
    
    selectContract(contract) {
      this.selectedContract = contract;
    },
    
    getStatusBadgeClass(status) {
      const statusClassMap = {
        scheduled: 'badge-info',
        in_progress: 'badge-primary', 
        completed: 'badge-success',
        cancelled: 'badge-danger',
        rescheduled: 'badge-warning',
        unprocessed: 'badge-gray'
      };
      return statusClassMap[status] || 'badge-gray';
    }
  }
};
</script>

<style scoped>
.service-contract-summary {
  padding: 1rem;
  min-height: 100vh;
}

/* Header */
.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
}

.month-selector {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  min-width: 180px;
}

.month-selector:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Main Layout */
.contracts-layout {
  display: flex;
  gap: 1.5rem;
  min-height: 600px;
}

/* Sidebar */
.service-contracts-sidebar {
  width: 320px;
  flex-shrink: 0;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.sidebar-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  gap: 0.5rem;
  color: #6b7280;
}

.sidebar-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.empty-contracts-content {
  text-align: center;
}

.contracts-list {
  max-height: 600px;
  overflow-y: auto;
}

.contract-item {
  padding: 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

.contract-item:hover {
  background-color: #f9fafb;
}

.contract-item.active {
  background-color: #eff6ff;
  border-right: 3px solid #3b82f6;
}

.contract-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.25rem;
  gap: 0.5rem;
}

.contract-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.9rem;
  line-height: 1.3;
  flex: 1;
}

.contract-meta {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
}

.contract-time,
.contract-client {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.contract-works-count {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.125rem;
}

/* Main Content Area */
.main-content-area {
  flex: 1;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.empty-state-content {
  text-align: center;
}

/* Contract Details */
.contract-details {
  display: flex;
  flex-direction: column;
}

/* Works Copy List */
.works-copy-list {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.works-copy-table {
  width: 100%;
  font-size: 0.875rem;
  border-spacing: 0;
}

.work-copy-title {
  color: #374151;
  padding: 0.25rem 0.5rem 0.25rem 0;
  user-select: all;
}

.work-copy-hours {
  color: #059669;
  font-weight: 500;
  padding: 0.25rem 0;
  text-align: right;
  user-select: all;
}

.contract-total-time {
  margin-top: 0.75rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  font-weight: 600;
}

.total-label {
  color: #374151;
}

.total-hours {
  color: #059669;
}

.contract-details-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.contract-details-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.contract-details-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Works Section */
.works-section {
  padding: 1.5rem;
  flex: 1;
  /* overflow-y: auto; */
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.works-grid {
  display: grid;
  gap: 1rem;
}

.work-card {
  background: #f8fafc;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

.work-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  gap: 0.5rem;
}

.work-title {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.9rem;
  flex: 1;
}


.work-description {
  color: #6b7280;
  font-size: 0.8rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.work-users {
  border-top: 1px solid #e5e7eb;
  padding-top: 0.75rem;
}

.users-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.2rem 0.4rem;
  background: white;
  border-radius: 3px;
  font-size: 0.7rem;
  border: 1px solid #e5e7eb;
}

.user-name {
  color: #374151;
  font-weight: 500;
}

.user-time {
  color: #059669;
  font-weight: 500;
}

.total-item {
  border: 2px solid #059669;
  background: #ecfdf5;
}

.total-item .user-name {
  font-weight: 600;
  color: #059669;
}

.total-item .user-time {
  font-weight: 600;
}


.no-works {
  color: #6b7280;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

.badge-sm {
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
}

/* Summary Stats */
.summary-stats {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #374151;
}

.stat-label {
  font-weight: 500;
}

.stat-value {
  font-weight: 600;
  color: #1f2937;
}

/* Responsive */
@media (max-width: 768px) {
  .contracts-layout {
    flex-direction: column;
  }
  
  .service-contracts-sidebar {
    width: 100%;
    max-height: 300px;
  }
  
  .summary-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .month-selector {
    width: 100%;
    max-width: 200px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
  }
}
</style>
