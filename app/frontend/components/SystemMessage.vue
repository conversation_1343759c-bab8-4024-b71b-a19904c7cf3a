<!-- ABOUTME: System-wide announcement card component for displaying important updates
ABOUTME: Follows the standard card pattern with collapsible content and dismissible messages -->
<template>
  <div v-if="!isMessageDismissed" class="card">
    <div class="card-content">
      <h2 class="text-title mb-3">
        {{ $t('system_messages.announcements') }}
      </h2>
      <div class="announcement-item" :data-testid="`system-message-${currentMessageId}`">
        <div class="flex items-start mb-3">
          <div class="flex-1">
            <div class="text-gray-400 text-sm mb-3">
              11.08.2025 v3.0.1
            </div>
            <h4 class="text-gray-800 font-semibold mb-2">
              {{ $t('system_messages.version_title') }}
            </h4>
            <ul>
              <li class="text-gray-700 text-sm mb-2">
                  {{ $t('system_messages.version_item1_desc') }}
              </li>
              <li class="text-gray-700 text-sm mb-2">
                {{ $t('system_messages.version_item2_desc') }}
              </li>
              <li class="text-gray-700 text-sm mb-2">
                {{ $t('system_messages.version_item3_desc') }}
              </li>
              <li class="text-gray-700 text-sm mb-2">
                {{ $t('system_messages.version_item4_desc') }}
              </li>
            </ul>

            <div class="flex mt-3 justify-end">
              <button
                @click="dismissMessage"
                class="btn btn-small btn-light"
                :data-testid="`dismiss-${currentMessageId}`"
              >
                {{ $t('system_messages.announcement_dismiss', 'Ok') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Megaphone, ChevronUp, ChevronDown } from 'lucide-vue-next';

export default {
  name: 'SystemMessage',
  components: {
    Megaphone,
    ChevronUp,
    ChevronDown
  },
  data() {
    return {
      isAnnouncementsCollapsed: false,
      isMessageDismissed: false,
      // When changing message, update this ID to reset dismissals
      currentMessageId: 'version-2025-08-10'
    };
  },
  methods: {
    checkIfDismissed() {
      try {
        const stored = localStorage.getItem('dismissed_system_message_id');
        // Message is dismissed only if the stored ID matches current message ID
        return stored === this.currentMessageId;
      } catch (e) {
        console.warn('Could not read dismissed message:', e);
        return false;
      }
    },
    
    dismissMessage() {
      try {
        // Store the current message ID as dismissed
        localStorage.setItem('dismissed_system_message_id', this.currentMessageId);
        this.isMessageDismissed = true;
      } catch (e) {
        console.error('Could not save dismissed message:', e);
      }
    },
    
    toggleAnnouncements() {
      this.isAnnouncementsCollapsed = !this.isAnnouncementsCollapsed;
      localStorage.setItem('system_announcements_collapsed', JSON.stringify(this.isAnnouncementsCollapsed));
    }
  },
  mounted() {
    // Check if current message is dismissed
    this.isMessageDismissed = this.checkIfDismissed();

    // Load collapsed state from localStorage
    const storedCollapsed = localStorage.getItem('system_announcements_collapsed');
    if (storedCollapsed !== null) {
      try {
        this.isAnnouncementsCollapsed = JSON.parse(storedCollapsed);
      } catch (e) {
        this.isAnnouncementsCollapsed = false;
      }
    }
  }
};
</script>

<style scoped>
/* Keep styles minimal - mostly using Tailwind classes */
.announcement-item {
  transition: all 0.2s ease;
}

/* Proper bullet point styling */
.announcement-item ul {
  list-style-type: disc;
  padding-left: 1.5rem;
}

.announcement-item li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}
</style>