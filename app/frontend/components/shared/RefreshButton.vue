// ABOUTME: Simple refresh icon component for PWA manual refresh capability
// ABOUTME: Triggers page reload for reliable data updates in PWA context

<template>
  <button
    @click="handleRefresh"
    class="refresh-button hidden-mobile"
    :aria-label="$t('refresh.button', 'Obnovit')"
    data-action="refresh"
    :data-testid="`refresh-${target}`"
  >
    <svg
      class="refresh-icon"
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="18"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <polyline points="23 4 23 10 17 10"></polyline>
      <polyline points="1 20 1 14 7 14"></polyline>
      <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
    </svg>
    <span class="sr-only">{{ $t('refresh.button', 'Obnovit') }}</span>
  </button>
</template>

<script>
export default {
  name: 'RefreshButton',
  
  props: {
    target: {
      type: String,
      required: false,
      default: 'page'
    }
  },

  methods: {
    handleRefresh() {
      // Simple page reload - reliable for PWA refresh
      window.location.reload();
    }
  }
};
</script>

<style scoped>
.refresh-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.refresh-button:hover {
  color: #333;
}

.refresh-icon {
  width: 18px;
  height: 18px;
}

/* Hide on mobile devices - users use pull-to-refresh */
@media (max-width: 768px) {
  .hidden-mobile {
    display: none;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>