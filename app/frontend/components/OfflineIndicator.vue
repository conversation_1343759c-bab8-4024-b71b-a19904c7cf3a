<!-- ABOUTME: Offline indicator component showing network status banner -->
<!-- ABOUTME: Displays when user is offline with option to retry connection -->

<template>
  <transition name="slide-down">
    <div 
      v-if="showOfflineIndicator" 
      class="offline-indicator"
      data-vue-component="offline-indicator"
      data-testid="offline-indicator"
    >
      <div class="offline-content">
        <span class="offline-icon">⚠️</span>
        <span class="offline-message">{{ $t('network.offline_message', 'Jste offline') }}</span>
        <button 
          @click="handleRetry"
          :disabled="isRetrying"
          class="retry-button"
          data-action="retry-connection"
          data-testid="retry-button"
        >
          {{ isRetrying ? $t('network.retrying', 'Zkouším...') : $t('network.retry', 'Zkusit znovu') }}
        </button>
        <button 
          @click="hideIndicator"
          class="close-button"
          data-action="hide-offline-indicator"
          data-testid="close-button"
          :title="$t('network.hide', 'Skrýt')"
        >
          ✕
        </button>
      </div>
    </div>
  </transition>
</template>

<script>
import { mapGetters } from 'vuex';
import { retryConnection } from '../utils/networkDetection';

export default {
  name: 'OfflineIndicator',
  
  data() {
    return {
      isRetrying: false
    };
  },
  
  computed: {
    ...mapGetters('networkStore', ['showOfflineIndicator'])
  },
  
  methods: {
    async handleRetry() {
      this.isRetrying = true;
      
      try {
        const isOnline = await retryConnection();
        if (!isOnline) {
          // Connection still not available
          console.log('[Network] Retry failed - still offline');
        }
      } catch (error) {
        console.error('[Network] Retry error:', error);
      } finally {
        this.isRetrying = false;
      }
    },
    
    hideIndicator() {
      this.$store.dispatch('networkStore/hideOfflineIndicator');
    }
  }
};
</script>

<style scoped>
.offline-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #d97706;
  color: white;
  padding: 12px 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.offline-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  max-width: 1200px;
  margin: 0 auto;
}

.offline-icon {
  font-size: 16px;
}

.offline-message {
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.retry-button {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.retry-button:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.3);
}

.retry-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.close-button:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

/* Transition animations */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: transform 0.3s ease-out;
}

.slide-down-enter-from {
  transform: translateY(-100%);
}

.slide-down-leave-to {
  transform: translateY(-100%);
}

/* Responsive design */
@media (max-width: 768px) {
  .offline-content {
    flex-direction: column;
    gap: 8px;
  }
  
  .offline-message {
    text-align: center;
  }
  
  .retry-button {
    align-self: center;
  }
}
</style>