// ABOUTME: Network detection utility for handling online/offline browser events
// ABOUTME: Initializes network state management and provides cleanup for event listeners

let store = null;
let isInitialized = false;

// Initialize network detection with Vuex store
export function initNetworkDetection(vuexStore) {
  if (isInitialized) {
    console.log('[Network] Already initialized');
    return;
  }
  
  store = vuexStore;
  
  // Set initial state
  store.dispatch('networkStore/setOnlineStatus', navigator.onLine);
  
  // Listen to online/offline events
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  isInitialized = true;
  console.log('[Network] Detection initialized');
}

// Handle online event
function handleOnline() {
  console.log('[Network] Connection restored');
  if (store) {
    store.dispatch('networkStore/setOnlineStatus', true);
  }
}

// Handle offline event
function handleOffline() {
  console.log('[Network] Connection lost');
  if (store) {
    store.dispatch('networkStore/setOnlineStatus', false);
  }
}

// Cleanup function for removing event listeners
export function cleanupNetworkDetection() {
  if (!isInitialized) return;
  
  window.removeEventListener('online', handleOnline);
  window.removeEventListener('offline', handleOffline);
  
  isInitialized = false;
  store = null;
  console.log('[Network] Detection cleaned up');
}

// Manual retry function for testing connection
export function retryConnection() {
  console.log('[Network] Retrying connection...');
  
  // Simple network test using a small image request
  return new Promise((resolve) => {
    const img = new Image();
    const timeout = setTimeout(() => {
      img.onload = img.onerror = null;
      resolve(false);
    }, 5000);
    
    img.onload = () => {
      clearTimeout(timeout);
      if (store) {
        store.dispatch('networkStore/setOnlineStatus', true);
      }
      resolve(true);
    };
    
    img.onerror = () => {
      clearTimeout(timeout);
      resolve(false);
    };
    
    // Use a small favicon or icon for the test
    img.src = '/icons/icon-192x192.png?' + Date.now();
  });
}