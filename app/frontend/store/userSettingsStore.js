// ABOUTME: Vuex store module for managing user-specific settings and workspace visibility preferences
// ABOUTME: Handles fetching and updating individual user configuration including feature visibility

import axios from 'axios';
import { sendFlashMessage } from '../utils/flashMessage.js';

const state = {
  settings: {
    id: null,
    start_time: '08:00',
    end_time: '16:00',
    break_start: '12:00',
    break_duration: 30,
    auto_break: true,
    timezone: 'Prague',
    country_code: null,
    language: null,
    language_code: 'cs',
    // Feature visibility (default to showing everything)
    show_works_menu: true,
    show_bookings_menu: true,
    show_meetings_menu: true,
    show_daily_logs: true
  },
  permissions: {
    can_customize_visibility: false
  },
  loading: false,
  lastFetched: null
};

const getters = {
  // Feature visibility getters (replacing company settings)
  showWorksMenu: (state) => state.settings.show_works_menu,
  showBookingsMenu: (state) => state.settings.show_bookings_menu,
  showMeetingsMenu: (state) => state.settings.show_meetings_menu,
  showDailyLogs: (state) => state.settings.show_daily_logs,
  
  // Permission getters
  canCustomizeVisibility: (state) => state.permissions.can_customize_visibility,
  
  // Settings getters
  allSettings: (state) => state.settings,
  isLoading: (state) => state.loading,
  
  // Check if settings have been fetched recently (within 5 minutes)
  areSettingsFresh: (state) => {
    if (!state.lastFetched) return false;
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    return state.lastFetched > fiveMinutesAgo;
  }
};

const mutations = {
  SET_SETTINGS(state, settings) {
    state.settings = { ...state.settings, ...settings };
    state.lastFetched = Date.now();
  },
  
  SET_PERMISSIONS(state, permissions) {
    state.permissions = { ...state.permissions, ...permissions };
  },
  
  SET_LOADING(state, loading) {
    state.loading = loading;
  },
  
  UPDATE_SETTING(state, { key, value }) {
    state.settings[key] = value;
  },
  
  UPDATE_VISIBILITY(state, { feature, visible }) {
    const key = `show_${feature}`;
    if (state.settings.hasOwnProperty(key)) {
      state.settings[key] = visible;
    }
  },
  
  RESET_SETTINGS(state) {
    state.settings = {
      id: null,
      start_time: '08:00',
      end_time: '16:00',
      break_start: '12:00',
      break_duration: 30,
      auto_break: true,
      timezone: 'Prague',
      country_code: null,
      language: null,
      language_code: 'cs',
      show_works_menu: true,
      show_bookings_menu: true,
      show_meetings_menu: true,
      show_daily_logs: true
    };
    state.permissions = {
      can_customize_visibility: false
    };
    state.lastFetched = null;
  }
};

const actions = {
  // Fetch user settings from API
  async fetchSettings({ commit, getters }, { force = false } = {}) {
    // Skip if settings are fresh unless forced
    if (!force && getters.areSettingsFresh) {
      return;
    }
    
    commit('SET_LOADING', true);
    
    try {
      const response = await axios.get('/api/v1/user_settings');
      const { user_setting, permissions } = response.data;
      
      commit('SET_SETTINGS', user_setting);
      commit('SET_PERMISSIONS', permissions);
      
      console.log('👤 User settings fetched successfully:', user_setting);
      
    } catch (error) {
      console.error('👤 ERROR: Failed to fetch user settings:', error);
      // Don't show error message for 404 (user might not have settings yet)
      if (error.response?.status !== 404) {
        sendFlashMessage('Failed to load user settings', 'error');
      }
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  // Update user settings
  async updateSettings({ commit, state }, updates) {
    commit('SET_LOADING', true);
    
    // Convert visibility settings to hide_ format for backend
    const settingsToSend = { ...updates };
    
    // Convert show_* to hide_* for backend
    if ('show_works_menu' in updates) {
      settingsToSend.hide_works_menu = !updates.show_works_menu;
      delete settingsToSend.show_works_menu;
    }
    if ('show_bookings_menu' in updates) {
      settingsToSend.hide_bookings_menu = !updates.show_bookings_menu;
      delete settingsToSend.show_bookings_menu;
    }
    if ('show_meetings_menu' in updates) {
      settingsToSend.hide_meetings_menu = !updates.show_meetings_menu;
      delete settingsToSend.show_meetings_menu;
    }
    if ('show_daily_logs' in updates) {
      settingsToSend.hide_daily_logs = !updates.show_daily_logs;
      delete settingsToSend.show_daily_logs;
    }
    
    try {
      const response = await axios.patch('/api/v1/user_settings', {
        user_setting: settingsToSend
      });
      
      const { user_setting, permissions } = response.data;
      commit('SET_SETTINGS', user_setting);
      if (permissions) {
        commit('SET_PERMISSIONS', permissions);
      }
      
      sendFlashMessage(response.data.message || 'Settings updated successfully', 'success');
      console.log('👤 User settings updated successfully:', user_setting);
      
      return { success: true, settings: user_setting };
      
    } catch (error) {
      console.error('👤 ERROR: Failed to update user settings:', error);
      const errorMessage = error.response?.data?.errors?.join(', ') || 'Failed to update settings';
      sendFlashMessage(errorMessage, 'error');
      
      return { success: false, error: errorMessage };
    } finally {
      commit('SET_LOADING', false);
    }
  },
  
  // Update a single setting
  async updateSetting({ dispatch }, { key, value }) {
    return await dispatch('updateSettings', { [key]: value });
  },
  
  // Update visibility for a specific feature
  async updateVisibility({ commit, dispatch }, { feature, visible }) {
    commit('UPDATE_VISIBILITY', { feature, visible });
    return await dispatch('updateSettings', { [`show_${feature}`]: visible });
  },
  
  // Initialize settings when logging in or switching companies
  async initializeSettings({ dispatch }) {
    await dispatch('fetchSettings', { force: true });
  },
  
  // Clear settings when logging out
  clearSettings({ commit }) {
    commit('RESET_SETTINGS');
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};