// ABOUTME: Network state Vuex store module for tracking online/offline status
// ABOUTME: Manages network connectivity state and provides offline indicator visibility control

export default {
  namespaced: true,
  
  state: {
    isOnline: navigator.onLine,
    showOfflineIndicator: false
  },
  
  mutations: {
    SET_ONLINE_STATUS(state, isOnline) {
      state.isOnline = isOnline;
      state.showOfflineIndicator = !isOnline;
    },
    
    HIDE_OFFLINE_INDICATOR(state) {
      state.showOfflineIndicator = false;
    }
  },
  
  actions: {
    setOnlineStatus({ commit }, isOnline) {
      commit('SET_ONLINE_STATUS', isOnline);
    },
    
    hideOfflineIndicator({ commit }) {
      commit('HIDE_OFFLINE_INDICATOR');
    }
  },
  
  getters: {
    isOnline: state => state.isOnline,
    isOffline: state => !state.isOnline,
    showOfflineIndicator: state => state.showOfflineIndicator
  }
};