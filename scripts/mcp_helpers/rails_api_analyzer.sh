#!/bin/bash
# Rails API Analyzer for JWT Implementation
# Analyzes API controllers and their authentication methods

cd /home/<USER>/Projects/attendifyapp

echo "=== API Base Controller ==="
echo "File: app/controllers/api/v1/api_controller.rb"
echo "---"
if [ -f app/controllers/api/v1/api_controller.rb ]; then
    cat app/controllers/api/v1/api_controller.rb
else
    echo "API controller not found at expected location"
fi
echo ""

echo "=== Application Controller (Authentication Methods) ==="
echo "Searching for authentication methods..."
echo "---"
grep -n -A 5 -B 2 "authenticate\|current_user\|signed_in" app/controllers/application_controller.rb

echo -e "\n=== API Controllers List ==="
find app/controllers/api -name "*.rb" -type f | sort

echo -e "\n=== Authentication-related Methods in API Controllers ==="
grep -r "authenticate\|current_user\|skip_before_action.*authenticate" app/controllers/api/ --include="*.rb" | grep -v "\.log"