# Guide to Setting Up Vite, Vue.js, and Tailwind CSS in a Rails 7 Application

This guide provides a step-by-step process to integrate Vite, Vue.js, and Tailwind CSS into a Rails 7 application, based on the setup in this project.

## 1. Prerequisites

- Ruby 3.1.2 or later
- Rails 7.0.8 or later
- Node.js and Yarn or npm

## 2. Gem Installation

Add the `vite_rails` gem to your `Gemfile`:

```ruby
# Gemfile
gem 'vite_rails'
```

Then run `bundle install`.

## 3. Initial Vite Setup

Run the Vite installer for Rails:

```bash
bundle exec vite install
```

This will create the following files:

- `vite.config.ts`
- `app/frontend/entrypoints/application.js`
- `config/vite.json`
- `app/views/layouts/application.html.erb` will be updated with `vite_client_tag` and `vite_javascript_tag`.

## 4. Frontend Dependencies Installation

Install the necessary npm packages.

### Core Dependencies:

```bash
npm install vite vite-plugin-ruby @vitejs/plugin-vue vue vue-router vuex vue-i18n tailwindcss autoprefixer postcss
```

### Development Dependencies:

```bash
npm install --save-dev @playwright/test @rollup/plugin-terser @testing-library/jest-dom @testing-library/user-event @testing-library/vue @vitest/ui @vue/test-utils autoprefixer jsdom msw playwright tailwindcss vite vite-plugin-ruby vitest
```

Your `package.json` should look similar to this:

```json
{
  "private": true,
  "type": "module",
  "devDependencies": {
    "@playwright/test": "^1.52.0",
    "@rollup/plugin-terser": "^0.4.4",
    "@testing-library/jest-dom": "^6.6.3",
    "@testing-library/user-event": "^14.6.1",
    "@testing-library/vue": "^8.1.0",
    "@vitest/ui": "^2.1.9",
    "@vue/test-utils": "^2.4.6",
    "autoprefixer": "^10.4.21",
    "jsdom": "^24.1.3",
    "msw": "^2.8.4",
    "playwright": "^1.52.0",
    "tailwindcss": "^3.4.1",
    "vite": "^5.4.19",
    "vite-plugin-ruby": "^5.1.1",
    "vitest": "^2.1.9"
  },
  "dependencies": {
    "@hotwired/turbo-rails": "^8.0.12",
    "@rails/actioncable": "^7.0.8",
    "@vitejs/plugin-vue": "^5.2.4",
    "axios": "^1.7.9",
    "vue": "^3.5.13",
    "vue-i18n": "^11.1.3",
    "vue-router": "^4.5.1",
    "vuex": "^4.0.2"
  },
  "scripts": {
    "dev": "vite dev",
    "build": "vite build --emptyOutDir",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:e2e:debug": "playwright test --debug"
  }
}
```

## 5. Configuration Files

### Vite Configuration (`vite.config.ts`)

Update your `vite.config.ts` to include the Vue plugin and configure the development server for network access.

```typescript
import { defineConfig } from 'vite'
import RubyPlugin from 'vite-plugin-ruby'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [
    RubyPlugin(),
    vue(),
  ],
  server: {
    host: '0.0.0.0',  // Allow external connections
    port: 3036,
    strictPort: true,
    hmr: {
      overlay: true,
      host: 'YOUR_LOCAL_IP',  // Server's actual IP for HMR WebSocket
      port: 3036,
      clientPort: 3036
    },
    cors: true
  },
  build: {
    target: 'esnext',
    minify: 'esbuild',
    sourcemap: true
  },
  optimizeDeps: {
    include: ['vue', 'axios', 'vue-i18n'],
    force: true
  }
})
```
**Note:** Replace `YOUR_LOCAL_IP` with your actual local IP address to enable Hot Module Replacement (HMR) on other devices on your network.

### Tailwind CSS Configuration

Create `tailwind.config.js` and `postcss.config.js` in the root of your project.

**`tailwind.config.js`:**

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/views/**/*.html.erb',
    './app/helpers/**/*.rb',
    './app/frontend/**/*.js',
    './app/frontend/**/*.vue',
  ],
  theme: {
    extend: {
      // Your custom theme
    },
  },
  plugins: [],
}
```

**`postcss.config.js`:**

```javascript
export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
```

### Rails Vite Configuration (`config/vite.json`)

Update `config/vite.json` to specify the `sourceCodeDir` and development server settings.

```json
{
  "all": {
    "sourceCodeDir": "app/frontend",
    "watchAdditionalPaths": []
  },
  "development": {
    "autoBuild": true,
    "publicOutputDir": "vite-dev",
    "port": 3036,
    "host": "YOUR_LOCAL_IP",
    "assetHost": "http://YOUR_LOCAL_IP:3036"
  },
  "test": {
    "autoBuild": true,
    "publicOutputDir": "vite-test",
    "port": 3037
  }
}
```
**Note:** Replace `YOUR_LOCAL_IP` with your actual local IP address.

## 6. Frontend Structure

### Entrypoints

Your main JavaScript entry point will be in `app/frontend/entrypoints/application.js`. You can create other entrypoints for specific pages if needed.

Create a CSS entrypoint at `app/frontend/entrypoints/application.css` and import the Tailwind directives:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

### Vue Application

Create your Vue application structure inside `app/frontend`. A typical structure would be:

```
app/frontend/
├── components/
├── views/
├── router/
├── store/
├── App.vue
└── entrypoints/
    ├── application.js
    └── application.css
```

**`app/frontend/entrypoints/application.js`:**

```javascript
import { createApp } from 'vue';
import App from '../App.vue';
import router from '../router';
import store from '../store';
import './application.css'; // Import Tailwind CSS

const app = createApp(App);

app.use(router);
app.use(store);

app.mount('#app'); // Mount your Vue app to an element in your layout
```

## 7. Rails Views

In your `app/views/layouts/application.html.erb`, ensure you have the `vite_client_tag` and `vite_javascript_tag`.

```html
<!DOCTYPE html>
<html>
  <head>
    ...
    <%= vite_client_tag %>
    <%= vite_javascript_tag 'application' %>
    ...
  </head>
  <body>
    <div id="app">
      <%= yield %>
    </div>
  </body>
</html>
```

## 8. Running the Development Server

Start your Rails server and the Vite development server in separate terminals:

```bash
./bin/dev
```

This will use the `Procfile.dev` to run both servers.

```yaml
# Procfile.dev
web: bin/rails server -p 3000
vite: bin/vite dev
```

Now you should have a working Rails 7 application with Vite, Vue.js, and Tailwind CSS.
