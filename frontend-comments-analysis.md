# Frontend Comments Analysis

This document contains all TODO comments, Linear issue references (TYM-*), and other development-related comments found in the frontend codebase.

## TODO Comments

### app/frontend/components/Mainbox.vue
- **Line 746**: `// TODO: Add meetings data fetching when implemented`

### app/frontend/components/companies/EditCompany.vue
- **Line 146**: `// TODO: Logo-related properties disabled due to security vulnerability - see Linear issue TYM-61`
- **Line 180**: `// TODO: Logo data disabled due to security vulnerability - see Linear issue TYM-61`
- **Line 254**: `// TODO: Logo upload methods disabled due to security vulnerability - see Linear issue TYM-61`

### app/frontend/store/userStore.js
- **Line 82**: `// TODO: (Future Enhancement - Post Session Removal) Simplify isAuthenticated logic to derive from jwtToken/jwtUser presence`
- **Line 227**: `// TODO: (Future Enhancement - Post JWT Core) Consider consolidating user data fetching`

### app/frontend/services/authService.js
- **Line 239**: `* TODO: (Future Enhancement - Post JWT Core) Consider removing this method`
- **Line 331**: `// TODO: (Future Enhancement - Post JWT Core) Consolidate error handling`

### app/frontend/services/cable.js
- **Line 4**: `// TODO (Future Enhancement): Implement CableService as EventEmitter`
- **Line 9**: `// TODO (Future Enhancement): Implement stale connection detection`

### app/frontend/utils/axiosSetup.js
- **Line 38**: `// TODO: This interceptor will be enhanced with automatic token refresh logic`
- **Line 39**: `// TODO: (Future Enhancement) Consider adding request ID for debugging dual auth scenarios`

## Linear Issue References (TYM-*)

### TYM-61 - Logo Security Vulnerability
**Files affected:**
- `app/frontend/components/companies/EditCompany.vue` (Lines 146, 180, 254)
- **Context**: Logo-related functionality disabled due to security vulnerability

### TYM-83 - PWA Controller Changes
**Files affected:**
- `app/frontend/entrypoints/application.js` (Lines 194, 256)
- **Context**: PWA service worker controller change handling and update prompts

### TYM-115 - Company Context Management
**Files affected:**
- `app/frontend/store/userStore.js` (Lines 23, 50, 123, 185, 193, 199, 237)
- `app/frontend/services/authService.js` (Lines 80, 192, 306, 376, 406, 421, 435, 439)
- `app/frontend/services/cable.js` (Lines 54, 60, 62)
- `app/frontend/utils/axiosSetup.js` (Line 74)
- `app/frontend/views/auth/AcceptInvitationView.vue` (Lines 227, 235)
- **Context**: Company switching and tenant context management

### TYM-46 - Security Fix
**Files affected:**
- `app/frontend/views/auth/AcceptInvitationView.vue` (Line 235)
- **Context**: Security fix for invitation acceptance redirect

## Development Comments (DEBUG, NOTE, CRITICAL, etc.)

### DEBUG Comments
- `app/frontend/components/meetings/MeetingForm.vue:293`: `// DEBUG: Check axios instance before API call`
- `app/frontend/components/reports/OwnerMonthlyReports.vue:110`: `<!-- DEBUG: {{ JSON.stringify(day.event) }} -->`
- `app/frontend/entrypoints/application.js:7`: `// DEBUG: Check axios instance after import`
- `app/frontend/services/authService.js:280`: `// DEBUG: Check if this method is being called`
- `app/frontend/utils/axiosSetup.js:1`: `// DEBUG: Module evaluation tracking`
- `app/frontend/utils/axiosSetup.js:8`: `// DEBUG: Assign unique ID to this axios instance`
- `app/frontend/utils/axiosSetup.js:372`: `// DEBUG: Module evaluation complete`

### NOTE Comments
- `app/frontend/store/userStore.js:70`: `// NOTE: Authorization header is now managed by Axios request interceptor`
- `app/frontend/store/userStore.js:77`: `// NOTE: Authorization header clearing is now handled by Axios interceptor`
- `app/frontend/store/userStore.js:109`: `// NOTE: Authorization header clearing is now handled by Axios interceptor`
- `app/frontend/utils/axiosSetup.js:45`: `// NOTE: /api/v1/companies needs X-Company-ID to show which is current, so it's NOT tenantless`
- `app/frontend/utils/axiosSetup.js:78`: `// NOTE: JWT-only authentication mode - no session dependencies`

### CRITICAL Comments
- `app/frontend/store/userStore.js:277`: `// CRITICAL: Clear service worker caches on logout to prevent stale data`
- `app/frontend/store/userStore.js:290`: `// CRITICAL: Clear service worker caches even on error`
- `app/frontend/services/cable.js:122`: `// CRITICAL: Always clear connectionPromise regardless of outcome`
- `app/frontend/views/auth/AcceptInvitationView.vue:224`: `// CRITICAL: Set the company context from the user's primary company`

## Performance Optimization Comments

### app/frontend/components/calendar/CalendarWorkItem.vue
- **Lines 114-118**: Performance optimization notes about removing expensive CSS operations:
  - Removed `box-shadow` from hover and drag states
  - Removed `linear-gradient` striped background
  - Changed `transition: all` to be specific
  - Enhanced `transform` on drag state

### app/frontend/components/calendar/CalendarEventItem.vue
- **Lines 193-197**: Similar performance optimization notes as CalendarWorkItem

## Other Development Comments

### app/frontend/components/Sidebar.vue
- **Line 432**: `// Don't show error to user, might be transient network issue`

### app/frontend/components/events/NewMonthlyCalendar.vue
- **Line 408**: `// Remove the item from the local array immediately to prevent UI issues`

## CSS Animation Comments (Linear Gradients and Animations)

Several files contain CSS `linear` references in animations and gradients:
- `app/frontend/components/events/MonthlyEventTable.vue` (Lines 718, 790, 801)
- `app/frontend/components/events/EventList.vue` (Line 259)
- `app/frontend/components/MonthlyReport.vue` (Line 886)
- `app/frontend/components/TimeTracking.vue` (Line 827)
- `app/frontend/components/meetings/MeetingCalendar.vue` (Line 528)
- `app/frontend/components/reports/WorksSummary.vue` (Line 332)
- `app/frontend/components/reports/OwnerMonthlyReports.vue` (Lines 637, 738)
- `app/frontend/components/reports/OwnerWorkSummary.vue` (Line 325)
- `app/frontend/components/BookingCalendar.vue` (Lines 189, 737, 1108)

These are primarily CSS animations (`animation: spin 1s linear infinite`) and gradient backgrounds, not development comments.

## Summary

**Total TODO Comments**: 11
**Total Linear Issue References**: 3 unique issues (TYM-61, TYM-83, TYM-115)
**Total Development Comments**: 15+ (DEBUG, NOTE, CRITICAL, etc.)

The most significant findings are:
1. **TYM-61**: Logo functionality disabled due to security vulnerability
2. **TYM-115**: Extensive company context management implementation
3. **TYM-83**: PWA service worker handling improvements
4. Multiple TODO items for future enhancements, particularly around JWT authentication
5. Performance optimization comments in calendar components
6. Debug comments that may need cleanup in production
