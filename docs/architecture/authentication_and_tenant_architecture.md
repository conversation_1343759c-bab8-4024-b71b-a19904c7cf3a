# Authentication and Tenant Context Architecture

## Overview

This document describes the refactored authentication and tenant context architecture implemented to properly separate concerns and follow industry best practices.

## Problem Statement

The original implementation had several issues:
1. **Misnamed methods**: `set_tenant_from_jwt` actually read from headers, not JWT
2. **Multiple tenant-setting locations**: Three different places tried to set tenant context
3. **Ordering conflicts**: ApplicationController's `set_current_tenant` ran before authentication
4. **Single Responsibility violations**: Authentication logic mixed with tenant validation
5. **Hacky fixes**: Using `skip_before_action` to work around architectural issues

## Solution Architecture

### Clean Separation of Concerns

The new architecture follows these principles:
- **Authentication (Who are you?)**: Handled by `JwtAuthenticatable`
- **Tenant Context (What company?)**: Handled by `TenantContextable`
- **Authorization (What can you do?)**: Handled by ActionPolicy

### Module Responsibilities

#### 1. JwtAuthenticatable (Pure Authentication)
```ruby
module JwtAuthenticatable
  # ONLY handles JWT validation and user identification
  # NO tenant logic
  
  def authenticate_user_from_jwt!
    # Validates JWT token
    # Sets @current_jwt_user
    # Sets @jwt_payload for other concerns
  end
end
```

#### 2. TenantContextable (Tenant Management)
```ruby
module TenantContextable
  # ONLY handles tenant context establishment
  # Reads from multiple sources in priority order
  
  def establish_tenant_context
    # 1. X-Company-ID header (new standard)
    # 2. JWT payload company_id (legacy)
    # 3. Route parameters (nested resources)
    # Validates user access
    # Sets ActsAsTenant.current_tenant
  end
end
```

#### 3. Controller Hierarchy

```ruby
class ApplicationController
  include JwtAuthenticatable
  include TenantContextable
  
  # SINGLE authentication hook
  before_action :authenticate_request
  
  # SINGLE tenant hook (AFTER authentication)
  before_action :establish_tenant_for_request
end

class Api::V1::ApiController < ApplicationController
  # Skip web-oriented authentication
  skip_before_action :authenticate_request
  skip_before_action :establish_tenant_for_request
  
  # Use API-specific authentication
  before_action :authenticate_api_request!
  before_action :establish_and_require_tenant_context
end
```

### Request Flow

1. **Request arrives** → Controller receives request
2. **Authentication** → Identify user from JWT token
3. **Tenant Context** → Establish company context from header/JWT
4. **Authorization** → Check permissions via ActionPolicy
5. **Action Execution** → Process the request
6. **Response** → Return appropriate response

### Tenant Context Sources (Priority Order)

1. **X-Company-ID Header** (Recommended)
   - New standard approach
   - Allows fast company switching
   - No JWT regeneration needed

2. **JWT Payload company_id** (Legacy)
   - Backward compatibility
   - Logs deprecation warnings
   - Will be removed in future

3. **Route Parameters** (Nested Resources)
   - For URLs like `/companies/:company_id/resources`
   - Automatic context from URL structure

### Tenant-Agnostic Endpoints

Some endpoints don't require tenant context:

```ruby
class Api::V1::CompaniesController < Api::V1::ApiController
  def skip_tenant_validation?
    %w[index switch_company].include?(action_name)
  end
end

class Api::V1::AuthController < Api::V1::ApiController
  def skip_tenant_validation?
    true  # All auth endpoints are tenant-agnostic
  end
end
```

## Security Considerations

1. **Always validate** user has access to requested company
2. **Never trust** X-Company-ID header without validation
3. **Clear context** on logout
4. **Log attempts** to access unauthorized companies
5. **Fail closed** - require explicit tenant context

## Migration Strategy

### Phase 1: Current State (Dual-Mode Support)
- Backend accepts both X-Company-ID header and JWT payload
- Frontend sends X-Company-ID header
- Legacy support with deprecation warnings

### Phase 2: Monitor (2 weeks)
- Track deprecation warnings
- Ensure all clients updated
- Monitor error rates

### Phase 3: Cleanup
- Remove JWT payload company_id support
- Remove legacy aliases
- Clean up backward compatibility code

## Benefits

1. **Performance**: Company switch from ~500ms to ~50ms
2. **Clarity**: Each module has single responsibility
3. **Maintainability**: Easy to understand and modify
4. **Security**: Proper validation at each layer
5. **Standards**: Follows industry best practices (GitHub, Google, Slack)

## Implementation Files

### Core Concerns
- `app/controllers/concerns/jwt_authenticatable.rb` - JWT authentication
- `app/controllers/concerns/tenant_contextable.rb` - Tenant context management

### Controllers
- `app/controllers/application_controller.rb` - Base controller with new architecture
- `app/controllers/api/v1/api_controller.rb` - API base controller
- `app/controllers/api/v1/companies_controller.rb` - Company switching
- `app/controllers/api/v1/auth_controller.rb` - Authentication endpoints

### Tests
- `spec/requests/api/v1/jwt_tenant_decoupling_spec.rb` - Integration tests

## Common Patterns

### Making an Endpoint Tenant-Agnostic

```ruby
class Api::V1::SomeController < Api::V1::ApiController
  def skip_tenant_validation?
    %w[public_action other_action].include?(action_name)
  end
end
```

### Requiring Different Authentication

```ruby
class Api::V1::PublicController < Api::V1::ApiController
  skip_before_action :authenticate_api_request!, only: [:public_endpoint]
end
```

## Troubleshooting

### "Please provide X-Company-ID header"
- Client needs to send X-Company-ID header with valid company ID
- Check user has access to the company
- Verify company ID exists

### Authentication failures after refactor
- Ensure controllers inherit from correct base class
- Check for leftover `authenticate_user!` calls
- Verify JWT token is valid

### Tenant context not set
- Check X-Company-ID header is present
- Verify user has role in company
- Check endpoint doesn't skip tenant validation

## Related Documentation

- [TYM-115 JWT Tenant Decoupling](../features/TYM-115_jwt_tenant_decoupling.md)
- [JWT Implementation Notes](../development/jwt_implementation_notes.md)
- [API Endpoints Guide](../development/05_spa_endpoints.md)