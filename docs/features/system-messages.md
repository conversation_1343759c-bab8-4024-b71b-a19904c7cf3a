# System Messages Feature

## Overview
A simple, template-based message system for displaying announcements to users with proper internationalization support. Messages are hardcoded in the template with translations in YAML files.

## How It Works
1. Message content is hardcoded directly in the `SystemMessage.vue` template
2. All text uses locale translations (Czech and Slovak)
3. Users can dismiss the message - it won't show again until a new message is published
4. The announcement block can be collapsed/expanded like other Mainbox sections
5. New messages automatically reset dismissals when the message ID changes

## Implementation Details

### Message ID Management
- Current message ID is stored in the component: `currentMessageId: 'version-2025-08-10'`
- When publishing a new message, change this ID to reset all dismissals
- LocalStorage stores only one ID: `dismissed_system_message_id`

### Translation Structure
Messages are fully internationalized in:
- `/config/locales/system_messages.cs.yml` - Czech translations
- `/config/locales/system_messages.sk.yml` - Slovak translations

Translation keys follow the pattern:
```yaml
front:
  system_messages:
    version_title: "Title text"
    version_item1_title: "Item title"
    version_item1_desc: "Item description"
```

### LocalStorage Management
- Single dismissed message ID stored in `dismissed_system_message_id`
- Collapsed state stored in `system_announcements_collapsed`
- When message ID changes, previous dismissals are automatically ignored

## Publishing New Messages

1. Edit translation files:
   - `/config/locales/system_messages.cs.yml`
   - `/config/locales/system_messages.sk.yml`
   
2. Update the template in `/app/frontend/components/SystemMessage.vue`:
   - Replace the hardcoded message content in the template
   - Update translation keys to match your new content
   
3. **IMPORTANT**: Change the `currentMessageId` in the component data:
```javascript
data() {
  return {
    currentMessageId: 'version-2025-01-15'  // New ID resets dismissals
  };
}
```

4. Deploy the application

## Styling & Color Scheme
- **Gray theme** for informational announcements (no action required)
- **Amber theme** for "action required" notifications (bookings, invitations, owner tasks)
- Matches the existing notification card style with consistent icons
- Supports collapsing/expanding like other Mainbox sections
- Action link below content: "Přečetl jsem si to, nemusíte dále zobrazovat"

### Notification Color Scheme
- **Gray** (SystemMessage): Informational announcements, version updates
- **Amber** (NEWS, OwnerMainbox, CompanyConnections): Action required notifications
- Icons added to all notification blocks for consistency

## Markdown Support
Basic markdown is supported in message content:
- `**bold text**` for emphasis
- `[link text](url)` for links

## Testing
1. Add a test message to the component
2. Load the application - message should appear at top of Mainbox
3. Dismiss the message - it should disappear
4. Refresh the page - message should remain dismissed
5. Clear localStorage - message should reappear

## Future Enhancements
If needed, the system can be enhanced to:
- Load messages from environment variables or config files
- Support message scheduling (start/end dates)
- Add message priorities
- Integrate with backend API for dynamic messages