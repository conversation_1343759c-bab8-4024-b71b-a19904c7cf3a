# ImageMagick Security Policy Hardening

**Date**: August 14, 2025  
**Linear Issue**: [TYM-102](https://linear.app/tymbox/issue/TYM-102/)  
**CVE Reference**: CVE-2025-24293  

## Problem Statement

Current ImageMagick security policy at `/etc/ImageMagick-6/policy.xml` is permissive and vulnerable to command injection exploits through image processing operations.

## Current Vulnerable Configuration

### Policy Gaps Identified
1. **Delegate Processing**: External programs can be executed
2. **Filter Operations**: Image filters unrestricted  
3. **Coder Access**: All image formats allowed (including dangerous ones)
4. **Module Access**: Dangerous modules (MSL, MVG, PS, SVG, XPS) accessible
5. **System Paths**: Limited protection against system file access

## Required Security Policy Changes

### Primary Configuration File
- **Location**: `/etc/ImageMagick-6/policy.xml`
- **Backup Required**: Yes (before making changes)

### Policy Rules to Add
```xml
<!-- Block all delegate processing -->
<policy domain="delegate" rights="none" pattern="*"/>

<!-- Block all filter operations --> 
<policy domain="filter" rights="none" pattern="*"/>

<!-- Restrict to safe image formats only -->
<policy domain="coder" rights="read|write" pattern="{JPEG,PNG,GIF,WEBP}"/>

<!-- Block dangerous modules -->
<policy domain="module" rights="none" pattern="{MSL,MVG,PS,SVG,URL,XPS}"/>

<!-- Block system path access -->
<policy domain="path" rights="none" pattern="/etc/*"/>
<policy domain="path" rights="none" pattern="/proc/*"/>
<policy domain="path" rights="none" pattern="/sys/*"/>
```

## Application Impact Assessment

### Files That Process Images
- `app/models/company.rb` - Logo processing methods
- Active Storage variant routes - Image transformations

### Critical Methods to Test
- `Company#logo_thumbnail_url` (line 77)
- `Company#original_logo_url` (line 84)  
- Logo attachment display in frontend components

### Dependencies
- **ImageMagick 6.x**: System-wide installation
- **mini_magick gem**: Ruby interface to ImageMagick
- **image_processing gem**: Rails image processing
- **Active Storage**: File attachment system

## Testing Protocol

### Pre-Implementation
- [ ] Backup current policy file
- [ ] Document current configuration
- [ ] Test existing logo functionality

### Post-Implementation  
- [ ] Verify application starts without ImageMagick errors
- [ ] Test existing company logo display
- [ ] Generate new logo thumbnails (if logos exist)
- [ ] Check Rails logs for ImageMagick warnings
- [ ] Verify Active Storage variant processing works

### Test Commands
```bash
# Backup current policy
sudo cp /etc/ImageMagick-6/policy.xml /etc/ImageMagick-6/policy.xml.backup

# Test image processing (after changes)
rails console
> Company.first.logo_url if Company.first&.logo&.attached?
```

## Rollback Procedure

### If Issues Occur
1. **Restore original policy**:
   ```bash
   sudo cp /etc/ImageMagick-6/policy.xml.backup /etc/ImageMagick-6/policy.xml
   ```
2. **Restart application** (if needed)
3. **Verify functionality restored**
4. **Document issues encountered**

### Common Issues to Watch For
- ImageMagick initialization errors
- Variant processing failures  
- Logo thumbnail generation errors
- Application startup failures

## Validation Checklist

### Security Validation
- [ ] Policy blocks delegate execution
- [ ] Policy restricts dangerous file formats
- [ ] Policy prevents system path access
- [ ] Policy blocks dangerous modules

### Functionality Validation  
- [ ] Company logos display correctly
- [ ] Logo thumbnails generate successfully
- [ ] No ImageMagick errors in application logs
- [ ] Active Storage variant processing functional

## Implementation Notes

### System Requirements
- Requires sudo/root access for policy file modification
- Changes take effect immediately (no service restart needed)
- Policy applies system-wide to all ImageMagick operations

### Application Compatibility
- Current logo processing uses only basic resize operations
- Hardcoded transformation parameters (no user input)
- Should not break existing functionality

### Performance Impact
- Minimal performance impact expected
- Policy restrictions may slightly improve security processing time
- No database or application code changes required

---

**Implementation Status**: Ready for deployment  
**Risk Level**: Low (existing functionality should remain intact)  
**Estimated Time**: 15-30 minutes including testing