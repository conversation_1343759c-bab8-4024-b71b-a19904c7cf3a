# Redis Render Deployment Guide - AttendifyApp

## Overview

This guide covers deploying the AttendifyApp JWT-only Rails application with Redis on Render. The app heavily relies on Redis for JWT authentication, session management, rate limiting, and WebSocket connections.

## Current Redis Configuration

### Core Setup
- **Main initializer**: `config/initializers/01_redis.rb` 
- **Connection pool**: 5 connections, 5-second timeout
- **Default URL**: `redis://localhost:6379/1`
- **Namespace**: `tymboxapp` (configurable via `REDIS_NAMESPACE`)

### Redis Dependencies
```ruby
gem 'redis', '~> 5.0'
gem 'redis-rails', '~> 5.0'
gem 'connection_pool', '~> 2.5'
```

## Redis Usage in Application

### 1. JWT Authentication System
- **JWT Sessions** (`app/services/jwt_session_service.rb`)
  - 90-day TTL for "forever login" UX
  - User session tracking with IP/User-Agent validation
- **JWT Revocation** (`app/models/jwt_revocation_strategy.rb`) 
  - Token blacklisting and family revocation
- **Redis Service** (`app/services/redis_service.rb`)
  - Centralized Redis operations with error handling

### 2. Namespacing System
- **Base namespace**: `tymboxapp`
- **Feature namespaces**:
  - JWT: `jwt_sessions`, `jwt_revoked`, `jwt_refresh`
  - Auth: `password_reset`, `email_confirmation`
  - Infrastructure: `rate_limit`, `cache`, `sidekiq`, `websocket`

### 3. Rate Limiting (Rack::Attack)
- Uses Redis for rate limiting storage
- Protects JWT endpoints and authentication routes

### 4. Action Cable Integration
- WebSocket connections require Redis in production
- Uses same Redis instance with channel prefix

## Step-by-Step Render Deployment

### 1. Add Redis Add-on
**In Render Dashboard:**
1. Navigate to your service dashboard
2. Click "Environment" tab
3. Add Redis add-on:
   - Click "Add Database"
   - Select "Redis" 
   - Choose plan (free tier available)
   - Wait for provisioning to complete

### 2. Environment Variables
**Set in Render Environment tab:**
```bash
# Automatically provided by Redis add-on
REDIS_URL=redis://red-xxxxx:6379

# Required for production
RAILS_ENV=production

# Optional: Production namespace isolation
REDIS_NAMESPACE=tymboxapp_production
```

### 3. Build Script Configuration
**Create/update `bin/render-build.sh`:**
```bash
#!/usr/bin/env bash
set -o errexit

bundle install
bundle exec rails assets:precompile
bundle exec rails assets:clean

# Verify Redis connectivity
bundle exec rails jwt:health
```

### 4. Pre-Deployment Health Checks
**Run locally before deploying:**
```bash
# Test Redis connectivity
bin/rails jwt:health

# Comprehensive JWT system check
bin/rails jwt:preflight_check

# Check Redis configuration
bin/rails console
> Redis.current.ping
```

### 5. Deploy Sequence
1. **Add Redis add-on first** and wait for provisioning
2. **Configure environment variables**
3. **Deploy application**
4. **Run post-deployment verification**

### 6. Post-Deployment Verification
**Test critical endpoints:**
```bash
# Health check
curl https://your-app.onrender.com/api/v1/auth/health

# JWT login flow
curl -X POST https://your-app.onrender.com/api/v1/auth/jwt_login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'

# Check Redis connectivity via Rails console
heroku run rails console -a your-app
> RedisService.health_check
```

## Critical Redis Dependencies

**Application will fail without Redis for:**
- ✅ **JWT Authentication** - Core login/logout functionality
- ✅ **Session Management** - User session persistence  
- ✅ **Token Security** - Revocation and breach detection
- ✅ **Rate Limiting** - DDoS and brute-force protection
- ✅ **WebSocket Connections** - Real-time features
- ✅ **Password Reset** - Secure token management

## Production Considerations

### Connection Pool Settings
- **Current**: 5 connections, 5-second timeout
- **Recommendation**: Monitor usage and adjust based on traffic
- **Render compatibility**: Well-suited for typical Render dyno sizes

### Redis Database Usage
- **Development**: Uses database `/1`
- **Render default**: Typically database `/0`
- **Compatibility**: Should work without changes

### SSL Configuration
- ✅ Already configured for production Redis connections
- ✅ Proper certificate validation enabled

## Monitoring and Debugging

### Health Check Commands
```bash
# JWT system health
bin/rails jwt:health

# Redis connectivity
bin/rails console
> Redis.current.ping
> RedisService.health_check

# Connection pool status
> Redis.current.connection[:pool].size
```

### Log Monitoring
```bash
# Authentication events
tail -f log/production.log | grep "\[SECURITY\]"

# Redis operations
tail -f log/production.log | grep "Redis"
```

## Troubleshooting

### Common Issues
1. **Redis connection timeout**
   - Check `REDIS_URL` environment variable
   - Verify Redis add-on is running

2. **JWT authentication failures**
   - Run `bin/rails jwt:health`
   - Check Redis connectivity
   - Verify namespace configuration

3. **Rate limiting not working**
   - Check Rack::Attack configuration
   - Verify Redis connection in initializer

### Emergency Fallback
If Redis fails in production:
1. Check Render Redis add-on status
2. Verify environment variables
3. Review connection pool configuration
4. Use health check commands for diagnosis

## Security Notes

- ✅ **Namespacing**: Prevents key collisions
- ✅ **SSL/TLS**: Encrypted connections in production  
- ✅ **Token rotation**: Automatic refresh token rotation
- ✅ **Rate limiting**: Protection against abuse
- ✅ **Session validation**: IP/User-Agent checking

Your Redis configuration is production-ready with comprehensive security measures and proper error handling.