# Investigation: Latent Bugs and Incomplete Refactoring in TYM-115

## 1. Executive Summary

A detailed investigation following the deployment of feature TYM-115 (Decouple JWT from Tenant Context) has revealed that the refactoring was incomplete. While the core authentication flow was updated, several critical satellite systems that depend on tenant context were not, leading to significant, user-facing bugs.

The root cause is consistent across all issues: a new, stricter tenant validation filter was added to the base `ApiController`, but controllers responsible for tenant-less operations, real-time connections, and background processing were not updated to handle this change.

This document outlines the four critical areas identified: **Action Cable**, **Invitations**, **Background Jobs**, and **Caching**.

---

## 2. Critical Bug: Action Cable Connection Failure

-   **Impact:** All real-time features (e.g., notifications, live updates) are non-functional. This is a critical, user-facing bug.
-   **Root Cause:** The Action Cable connection logic in `app/channels/application_cable/connection.rb` authenticates the user but **does not establish the tenant context**. It was not updated to account for the `company_id` being removed from the JWT payload. Because WebSocket connections do not support custom headers in the same way as HTTP requests, the new `X-Company-ID` header is not available at connection time.
-   **Recommended Fix:** The frontend client must be updated to pass the selected `company_id` as a query parameter when establishing the WebSocket connection. The `connection.rb` file on the backend must then be modified to:
    1.  Read the `company_id` from the connection parameters.
    2.  Verify that the authenticated `current_user` has access to that company.
    3.  Set `ActsAsTenant.current_tenant` for the scope of the connection.

---

## 3. Critical Bug: User Invitation Flow Broken

-   **Impact:** The entire user invitation workflow is broken. New users cannot accept invitations to join a company. This is a critical, user-facing bug that halts team expansion.
-   **Root Cause:** The `Api::V1::InvitationsController` inherits from `ApiController` but was not updated to be compatible with the new tenant validation. Actions that must be tenant-less (e.g., a new user viewing or accepting an invitation before they are part of a company) are being incorrectly subjected to the tenant validation filter, resulting in a `403 Forbidden` error.
-   **Recommended Fix:** The `InvitationsController` must be updated to tell the parent controller which of its actions are exempt from tenant validation. The most robust solution is to override the `skip_tenant_validation?` method within the controller:

    ```ruby
    # app/controllers/api/v1/invitations_controller.rb
    private

    def skip_tenant_validation?
      # Actions like 'show' and 'accept' must be tenant-less
      %w[show accept].include?(action_name)
    end
    ```

---

## 4. High-Risk Area: Background Jobs & Mailers

-   **Impact:** Background jobs that are tenant-dependent are at high risk of failing or, worse, processing incorrect data. This could lead to silent data corruption, failed report generations, or emails being sent with the wrong information.
-   **Root Cause:** The investigation found numerous calls to `.perform_later` and `.deliver_later` where the tenant context (`company_id`) is not being explicitly passed to the job. The previous implementation may have implicitly relied on a tenant ID being available in the session, a fragile pattern that is now guaranteed to fail.
-   **Recommended Fix:** A full audit of all background jobs and mailers is required. Any job or mailer that operates on tenant-specific data **must** be refactored to accept the `company_id` as an argument. The job's `perform` method must then use this ID to set the tenant context for its operations, preferably using a block like `ActsAsTenant.with_tenant(company) do ... end`.

---

## 5. Low-Risk Area: Caching Logic

-   **Impact:** Low. The application does not currently have extensive caching, so the risk of a bug is minimal.
-   **Root Cause:** This is a preventative finding. If tenant-specific caching were added without proper keying, a user switching companies could be served stale data from their previous company.
-   **Recommended Fix:** No immediate action is required. However, the team must adopt the best practice of including the tenant ID in all cache keys for tenant-specific content going forward. Example: `cache ["v1", ActsAsTenant.current_tenant, current_user, "permissions"]`.

---

## 6. Expanded Investigation: Other Potentially Affected Controllers

A deeper investigation was conducted to identify any other controllers that might be affected by the same root cause. This involved analyzing `config/routes.rb` and searching the codebase for all controllers inheriting from `Api::V1::ApiController`.

### Findings

The investigation confirmed that the issue is isolated to controllers that must handle tenant-less actions but were not updated to bypass the new, stricter tenant validation.

-   **`Api::V1::TranslationsController`**: This controller was initially considered a risk. However, analysis of the file confirmed it inherits from `ApplicationController`, not `ApiController`, and is therefore **not affected**.

-   **`Api::V1::FeedbacksController`**: The routes file incorrectly suggested this controller might allow anonymous access. Analysis of the controller confirmed it is correctly designed to be **tenant-dependent**, requiring an authenticated user and company context. It is **not affected**.

-   **Definitive List of Affected Controllers**: A codebase search for all `ApiController` subclasses confirmed that the only controllers with tenant-less actions are `AuthController` and `InvitationsController`. All other subclasses correctly manage tenant-specific resources and are not affected by this bug.

### Conclusion

The expanded investigation confirms that the impact of this bug, while critical, is contained. The required fixes are limited to `AuthController` and `InvitationsController` on the backend, and the URL matching logic in `axiosSetup.js` on the frontend.
