# Temporary Offline Solution - PWA Middle Step

**Date**: 2025-08-15  
**Status**: Proposed  
**Priority**: Medium  
**Estimated Time**: 4-6 hours  

## Problem Analysis

### Current Issues with Offline Functionality

The app currently has a **broken offline experience** that misleads users and degrades UX:

#### 1. **False Offline Claims**
The `public/offline.html` page makes **false promises** to users:
- Claims "Týmbox funguje i offline s ome<PERSON><PERSON><PERSON> funkce<PERSON>" (works offline with limited features)
- Lists non-existent features:
  - "Prohl<PERSON>žen<PERSON> uložených dat" (viewing saved data) - **FALSE**
  - "Základní navigace" (basic navigation) - **FALSE** 
  - "<PERSON><PERSON><PERSON><PERSON> úpravy (budou synchronizovány)" (local edits will sync) - **FALSE**

#### 2. **Broken SPA Experience**
The service worker's fetch handler breaks the SPA:
```javascript
// Current problematic code in public/sw.js
event.respondWith(
  fetch(request).catch(() => {
    return caches.match('/offline.html'); // Kicks users out of SPA
  })
);
```

When any navigation fails, users get:
- Kicked out of the Vue SPA to static HTML
- Loss of application state
- Loss of current page context
- Loss of form data
- Loss of authentication state

#### 3. **No Real Offline Functionality**
The service worker explicitly avoids caching API responses for security:
```javascript
// SECURITY: Do NOT cache API responses to prevent sensitive data exposure
// SECURITY: Do NOT serve API requests from cache as fallback
```

While security-conscious, this means **zero offline functionality** exists.

#### 4. **Missing Network State Management**
The Vue application has no awareness of online/offline state:
- No Vuex store for network status
- No offline indicators in components
- No graceful degradation of features
- No cached data display capabilities

## Proposed Minimalistic Solution

### **Goal**: Simple offline status indicator without complex functionality

### **Approach**: Keep users in the SPA with clear network status feedback

## Implementation Plan

### 1. Network State Store Module

Create a simple Vuex store module to track online/offline status:

```javascript
// app/frontend/store/networkStore.js
const networkStore = {
  namespaced: true,
  state: {
    isOnline: navigator.onLine,
    showOfflineIndicator: false,
    lastOnlineTime: null
  },
  mutations: {
    SET_ONLINE_STATUS(state, isOnline) {
      state.isOnline = isOnline;
      state.showOfflineIndicator = !isOnline;
      if (isOnline) {
        state.lastOnlineTime = new Date();
      }
    }
  },
  actions: {
    updateNetworkStatus({ commit }, isOnline) {
      commit('SET_ONLINE_STATUS', isOnline);
    }
  },
  getters: {
    isOnline: state => state.isOnline,
    showOfflineIndicator: state => state.showOfflineIndicator,
    networkStatus: state => state.isOnline ? 'online' : 'offline'
  }
};

export default networkStore;
```

### 2. Simple Offline Indicator Component

Create a minimal banner that appears when offline:

```vue
<!-- app/frontend/components/OfflineIndicator.vue -->
<template>
  <div v-if="showOfflineIndicator" class="offline-banner">
    <div class="offline-content">
      <span class="offline-icon">📡</span>
      <span class="offline-text">{{ $t('offline.you_are_offline') }}</span>
      <button @click="retry" class="retry-btn">
        {{ $t('offline.retry') }}
      </button>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'OfflineIndicator',
  computed: {
    ...mapGetters('networkStore', ['showOfflineIndicator'])
  },
  methods: {
    retry() {
      // Simple page reload to check connectivity
      window.location.reload();
    }
  }
};
</script>

<style scoped>
.offline-banner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #f59e0b;
  color: white;
  padding: 0.75rem;
  text-align: center;
  z-index: 9999;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.offline-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.retry-btn {
  background: rgba(255,255,255,0.2);
  border: 1px solid rgba(255,255,255,0.3);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.retry-btn:hover {
  background: rgba(255,255,255,0.3);
}
</style>
```

### 3. Network Detection Utility

Simple event listeners for online/offline events:

```javascript
// app/frontend/utils/networkDetection.js
export function initNetworkDetection(store) {
  const updateStatus = () => {
    const isOnline = navigator.onLine;
    store.dispatch('networkStore/updateNetworkStatus', isOnline);
    
    // Optional: Log network status changes
    console.log(`[Network] Status changed: ${isOnline ? 'online' : 'offline'}`);
  };
  
  // Listen for network events
  window.addEventListener('online', updateStatus);
  window.addEventListener('offline', updateStatus);
  
  // Set initial status
  updateStatus();
  
  // Return cleanup function
  return () => {
    window.removeEventListener('online', updateStatus);
    window.removeEventListener('offline', updateStatus);
  };
}
```

### 4. Minimal Service Worker Update

Prevent redirecting to offline.html for document requests:

```javascript
// public/sw.js - Update the fetch handler
self.addEventListener('fetch', (event) => {
  const { request } = event;
  
  // Handle static assets only
  if (request.destination === 'script' || 
      request.destination === 'style' ||
      request.destination === 'image') {
    event.respondWith(handleStaticRequest(request));
    return;
  }
  
  // For document requests, return 503 instead of offline.html
  // This lets the SPA handle the offline state
  event.respondWith(
    fetch(request).catch(() => {
      if (request.destination === 'document') {
        return new Response('', { 
          status: 503,
          statusText: 'Service Unavailable'
        });
      }
      return new Response('Network Error', { status: 503 });
    })
  );
});
```

### 5. Integration with Main App

Update the main App.vue to include the offline indicator:

```vue
<!-- app/frontend/App.vue -->
<template>
  <div id="app">
    <OfflineIndicator />
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    <CentralModal />
    <FlashMessages />
  </div>
</template>

<script>
import CentralModal from './components/shared/CentralModal.vue'
import FlashMessages from './components/FlashMessages.vue'
import OfflineIndicator from './components/OfflineIndicator.vue'

export default {
  name: 'App',
  components: {
    CentralModal,
    FlashMessages,
    OfflineIndicator
  }
}
</script>
```

### 6. Initialize Network Detection

Update the application entry point:

```javascript
// app/frontend/entrypoints/application.js
import { initNetworkDetection } from '../utils/networkDetection';

// After store creation
document.addEventListener('DOMContentLoaded', async () => {
  // ... existing code ...
  
  // Initialize network detection
  initNetworkDetection(store);
  
  // ... rest of app initialization ...
});
```

### 7. Add Translations

Add offline status translations:

```yaml
# app/frontend/locales/cs.yml
cs:
  offline:
    you_are_offline: "Jste offline"
    retry: "Zkusit znovu"
    connection_restored: "Připojení obnoveno"

# app/frontend/locales/sk.yml  
sk:
  offline:
    you_are_offline: "Ste offline"
    retry: "Skúsiť znovu"
    connection_restored: "Pripojenie obnovené"
```

## What This Solution Provides

✅ **Simple offline status indicator** within the SPA  
✅ **Preserves application state** - no redirect to static HTML  
✅ **No false promises** about offline functionality  
✅ **No security concerns** - no JWT or API handling  
✅ **Easy to implement** - minimal code changes  
✅ **Clear user feedback** - users know when they're offline  
✅ **Maintainable** - simple code that's easy to understand  

## What This Does NOT Include

❌ API request caching  
❌ JWT token handling in service worker  
❌ Offline data storage  
❌ Request queuing  
❌ Background sync  
❌ Complex offline functionality  
❌ Security-sensitive features  

## Files to Create/Modify

### New Files
1. `app/frontend/store/networkStore.js` - Network state module
2. `app/frontend/components/OfflineIndicator.vue` - Offline indicator component  
3. `app/frontend/utils/networkDetection.js` - Network detection utility

### Modified Files
1. `app/frontend/App.vue` - Add OfflineIndicator component
2. `app/frontend/store/index.js` - Register networkStore module
3. `app/frontend/entrypoints/application.js` - Initialize network detection
4. `public/sw.js` - Minimal update to avoid serving offline.html
5. `app/frontend/locales/cs.yml` - Add offline translations
6. `app/frontend/locales/sk.yml` - Add offline translations

## Success Criteria

- [ ] Users see offline indicator when network is unavailable
- [ ] Users stay in the Vue SPA when offline  
- [ ] Offline indicator disappears when network returns
- [ ] No false claims about offline functionality
- [ ] No security-sensitive features implemented
- [ ] Simple, maintainable code
- [ ] Proper translations in Czech and Slovak
- [ ] No impact on existing authentication or API functionality

## Benefits

1. **Improved User Experience**: Clear feedback about network status
2. **Preserves SPA Context**: Users don't lose their place in the application
3. **No False Advertising**: Honest about what works offline
4. **Foundation for Future**: Provides base for more advanced offline features
5. **Low Risk**: Minimal changes with no security implications
6. **Quick Implementation**: Can be completed in a few hours

## Future Enhancements

This minimalistic approach provides a foundation for future offline features:
- Selective API caching (with proper security)
- Offline request queuing
- Background sync
- Cached data indicators
- Progressive enhancement of features

## Related Issues

- Addresses false offline claims in `public/offline.html`
- Provides foundation for future offline functionality  
- Improves PWA user experience
- Maintains security by avoiding complex offline data handling
