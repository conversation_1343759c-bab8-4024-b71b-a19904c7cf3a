# require_login Method Complexity Analysis

## Current Structure (90 lines, 6+ responsibilities):

### Responsibilities:
1. **Logging** - Extensive debug logging
2. **Devise password reset check** - Special case handling
3. **Public page detection** - Complex path matching
4. **JWT authentication** - Silent JWT auth attempt
5. **Session cookie authentication** - Fallback auth
6. **Response formatting** - Different responses for SPA/API/HTML

## Why This Complexity Exists:

### 1. **Dual Authentication Transition**
The app is transitioning from session-based to JWT auth, requiring both systems to work.

### 2. **Multiple Entry Points**
- SPA routes (Vue.js app)
- API routes (JSON endpoints)
- Legacy Rails views (HTML)
- Devise password reset (special case)

### 3. **Redirect Loop Prevention**
Complex logic to prevent infinite redirects when auth fails

### 4. **Public Page Detection**
Must allow unauthenticated access to login/register pages

## Could It Be Simplified?

### Option 1: Break into smaller methods ✅
```ruby
def require_login
  return if skip_authentication?
  
  return if authenticate_with_jwt
  return if authenticate_with_session_cookie
  
  handle_authentication_failure
end

private

def skip_authentication?
  devise_password_reset_flow? || public_spa_page?
end

def authenticate_with_jwt
  authenticate_user_from_jwt_silent
  @current_jwt_user.present?
end

def handle_authentication_failure
  # Response logic here
end
```

### Option 2: Separate controllers for different auth types ❌
Would require major refactoring of entire app

### Option 3: Remove dual auth support ❌
Not possible until full JWT migration complete

## VERDICT: COMPLEXITY IS NECESSARY BUT COULD BE REFACTORED

The complexity serves real purposes:
- Prevents redirect loops
- Handles multiple authentication methods
- Supports different response formats
- Manages transition period

However, it COULD be refactored into smaller, more focused methods for better maintainability.

## Severity: LOW (Code Quality Issue)

This is not a bug, just a maintainability concern. The method works correctly but is hard to understand and modify.