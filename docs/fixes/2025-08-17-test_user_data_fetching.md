# User Data Fetching Analysis

## Current fetchUserData Calls:

### 1. **Login Flow** (userStore.js:188)
- User logs in via `store.dispatch('userStore/login')`
- AuthService.login() is called
- AuthService.attemptJwtLogin() - does NOT fetch user data (line 80 removed)
- Returns to userStore.login
- Calls `dispatch('fetchUserData')` at line 188

### 2. **Registration Flow** (userStore.js:266)
- Similar to login, calls fetchUserData after successful registration

### 3. **Password Reset Flow** (authService.js:531)
- After password reset, calls fetchUserData

### 4. **Email Confirmation Flow** (authService.js:580)
- After email confirmation, calls fetchUserData

### 5. **App Initialization** (authService.js:214)
- ensureAuthenticated() calls fetchUserData to validate token

## Race Condition Analysis:

### Scenario 1: Normal Login
1. User logs in
2. ONE call to fetchUserData from userStore.login
3. ✅ NO RACE CONDITION

### Scenario 2: App Initialization
1. App starts
2. ensureAuthenticated() → fetchUserData
3. ✅ NO RACE CONDITION (single call)

### Scenario 3: Page Refresh After Login
1. App initializes
2. ensureAuthenticated() → restoreSessionToken() → success
3. ensureAuthenticated() → fetchUserData (line 214)
4. ✅ NO RACE CONDITION

## The Comment at Line 80:
```javascript
// REMOVED: Call to this.fetchUserDataWithJwt();
// Rationale: The calling context (e.g., userStore.actions.login) is responsible
// for fetching detailed user data after successful login.
```

This shows the issue was ALREADY IDENTIFIED AND FIXED!

## VERDICT: NO CURRENT ISSUE

The potential race condition was already resolved by removing the duplicate call.
Current implementation has proper separation of concerns:
- AuthService handles authentication
- userStore handles user data fetching
- No duplicate calls in normal flows