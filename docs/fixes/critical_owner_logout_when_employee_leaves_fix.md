# CRITICAL FIX: Owner Gets Logged Out When Employee Leaves Company

## Problem
When any employee left a company, the company owner (and all other users) would get logged out upon page refresh. This was a critical authentication bug affecting multi-tenant functionality.

## Root Cause Discovery
In `app/models/user.rb`, the `leave_company` method had a critical bug on line 117:

```ruby
# BEFORE (BUG):
company_user_roles.unscoped.where(company: company).update_all(is_primary: false)
```

### The Problem with `unscoped`
When `.unscoped` is called on a Rails association, it removes **ALL** scopes, including:
1. The default scope (`where(active: true)`) from CompanyUserRole model
2. **The association's foreign key constraint** (`user_id = ?`)

This resulted in SQL without the user_id constraint:
```sql
UPDATE "company_user_roles" SET "is_primary" = false WHERE "company_user_roles"."company_id" = 2
```

This query was updating ALL users in the company, not just the leaving user!

### Bug Timeline
- **July 24, 2025** (commit 56ea4c0) - TYM-52: Added to fix a uniqueness constraint issue
- The intent was to remove only the `active: true` default scope
- But it inadvertently removed the user association scope too

## Solution
Used the Rails-idiomatic approach with the existing `with_inactive` scope:

```ruby
# AFTER (FIXED):
company_user_roles.with_inactive.where(company: company).update_all(is_primary: false)
```

This properly maintains the user_id constraint while only removing the active scope, producing:
```sql
UPDATE "company_user_roles" SET "is_primary" = false 
WHERE "company_user_roles"."user_id" = 25 AND "company_user_roles"."company_id" = 2
```

## File Changed
- `app/models/user.rb` - Line 118 (previously 117)

## Test Coverage
Added comprehensive tests:
- `spec/models/user_leave_company_spec.rb` - Unit tests for the leave_company method
- `spec/features/employee_leave_doesnt_logout_owner_spec.rb` - Integration test verifying owner remains logged in

## Impact
This fix ensures:
- When an employee leaves a company, only their role is deactivated
- The owner and other users maintain their `is_primary` flag
- No one gets unexpectedly logged out
- JWT tokens remain valid for all other users
- Multi-tenant authentication remains intact

## Verification
The logs showed the problematic SQL query:
```sql
UPDATE "company_user_roles" SET "is_primary" = false WHERE "company_user_roles"."company_id" = 2
```

After the fix, the query correctly includes the user_id:
```sql
UPDATE "company_user_roles" SET "is_primary" = false WHERE "company_user_roles"."user_id" = 25 AND "company_user_roles"."company_id" = 2
```

## Date Fixed
2025-08-24