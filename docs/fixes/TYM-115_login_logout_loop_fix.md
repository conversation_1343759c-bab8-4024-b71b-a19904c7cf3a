# TYM-115: Login-Logout Loop Fix

## Issue
Users were experiencing a login-logout loop where they would successfully authenticate but immediately be logged out. This was caused by missing tenant context (X-Company-ID header) in API requests after JWT authentication.

## Root Cause
After successful JWT login, the frontend was not storing the user's primary company ID in the Vuex store's `selectedCompanyId` state. This caused all API requests to fail with 403 Forbidden because they lacked the required `X-Company-ID` header. The frontend's axios interceptor misinterpreted these failures as authentication issues and triggered a logout.

## Solution Implemented

### 1. Frontend Changes

#### authService.js
- **Login**: Store company_id from login response in Vuex state
- **Token Refresh**: Update company context when refreshing tokens  
- **Session Restore**: Restore company context from session

```javascript
// After successful login/refresh/restore:
if (response.data.user && response.data.user.company_id) {
  store.commit('userStore/SET_SELECTED_COMPANY', response.data.user.company_id);
}
```

#### userStore.js
- **fetchCompanies**: Added automatic company selection logic with fallbacks:
  1. Use primary_company_id from response
  2. Find company marked with is_primary flag
  3. Select first available company
- **login action**: Now fetches companies list after login to ensure full context

### 2. Backend Changes

#### companies_controller.rb
- Enhanced `/api/v1/companies` endpoint to include:
  - `is_primary` flag for each company_user_role
  - `primary_company_id` at root level for convenience

### 3. Database Setup
- Ensure users have at least one company_user_role with `is_primary: true`
- The User model's `primary_company` method returns the company marked as primary

## Testing

### Backend Test Script
Created `test_tym115_login_fix.sh` to verify:
- JWT login returns company_id
- API calls require X-Company-ID header
- API calls succeed with proper header
- Companies endpoint includes primary flag
- Token refresh maintains company context
- Session restore maintains company context

### Frontend Test
Created RSpec feature test `spec/features/tym115_login_fix_spec.rb` to verify:
- User can login and stay logged in
- Navigation between pages works without logout
- Page refresh maintains session
- Fallback to first company when no primary is set

## Verification Steps

1. **Set primary company for user**:
```ruby
rails runner "User.find_by(email: '<EMAIL>').company_user_roles.first.update!(is_primary: true)"
```

2. **Run backend test**:
```bash
./test_tym115_login_fix.sh
```

3. **Test in browser**:
- Login with test credentials
- Verify you stay logged in
- Navigate between pages
- Refresh the page
- Check browser console for `[TYM-115]` logs showing company context

## Key Points

- **Backward Compatible**: Existing functionality preserved
- **Multiple Fallbacks**: Ensures a company is always selected
- **Security Maintained**: No changes to authentication flow
- **Minimal Changes**: Only essential fixes to resolve the issue

## Files Modified

### Frontend
- `app/frontend/services/authService.js` - Store company_id after auth
- `app/frontend/store/userStore.js` - Auto-select company with fallbacks

### Backend  
- `app/controllers/api/v1/companies_controller.rb` - Include primary flag

### Tests
- `test_tym115_login_fix.sh` - Backend API test script
- `spec/features/tym115_login_fix_spec.rb` - Frontend feature test

## Rollback Plan

If issues arise, revert these specific changes:
1. Remove company_id storage from authService.js
2. Remove auto-selection logic from userStore.js  
3. Revert companies_controller.rb changes

The fix is isolated and can be safely rolled back without affecting other functionality.