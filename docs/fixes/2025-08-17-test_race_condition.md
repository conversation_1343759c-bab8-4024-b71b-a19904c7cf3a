# Race Condition Test Analysis

## Scenario: Multiple API calls fail with 401 simultaneously

### What happens when 3 API calls fail at once:

1. **Call 1 fails with 401** (at time T)
   - Enters interceptor, checks if it's a JWT request
   - Has token, not retrying yet, marks `_retry = true`
   - `isRefreshing` is false, so sets `isRefreshing = true`
   - Starts token refresh

2. **Call 2 fails with 401** (at time T+1ms)
   - Enters interceptor
   - Has token, not retrying yet, marks `_retry = true`
   - `isRefreshing` is TRUE, so enters queue
   - Waits for refresh to complete

3. **Call 3 fails with 401** (at time T+2ms)
   - Same as Call 2, enters queue

4. **Token refresh completes** (at time T+100ms)
   - If successful: processQueue(null, true), all queued requests retry
   - If failed: processQueue(error, null), AND calls `handleAuthFailureAndRedirect()`

### The Problem:

If token refresh FAILS, only ONE call to `handleAuthFailureAndRedirect()` happens from the refresh failure.

BUT if there are non-JWT requests or requests without tokens that also get 401:
- Line 286-288: These will ALSO call `handleAuthFailureAndRedirect()`
- These calls are NOT synchronized

### Race Condition Confirmation:

**YES, there IS a race condition** if:
1. Multiple non-JWT requests fail with 401 simultaneously
2. OR a mix of JWT and non-JWT requests fail
3. OR requests that don't trigger the refresh queue (like those already marked with _retry)

### Impact:
- Multiple flash messages could appear
- Multiple router.push({ name: 'login' }) calls
- Potential navigation conflicts

### Severity: MEDIUM-HIGH
This can happen when:
- Server restarts and all requests fail
- Token expires and app makes multiple API calls
- Network issues cause multiple failures