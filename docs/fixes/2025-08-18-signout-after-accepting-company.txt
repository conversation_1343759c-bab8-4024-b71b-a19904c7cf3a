15:08:43 web.1  | Started POST "/api/v1/company_connections/9/accept" for ************ at 2025-08-18 15:08:43 +0200
15:08:43 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - POST /api/v1/company_connections/9/accept
15:08:43 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/api/v1/company_connections/9/accept", :method=>"POST", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:43 web.1  | Processing by Api::V1::CompanyConnectionsController#accept as JSON
15:08:43 web.1  |   Parameters: {"id"=>"9"}
15:08:43 web.1  | No JWT tenant context available
15:08:43 web.1  |   User Load (0.6ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2  [["id", 5], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
15:08:43 web.1  |   Company Load (0.5ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2  [["id", 6], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
15:08:43 web.1  |   CompanyUserRole Load (0.5ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5  [["active", true], ["user_id", 5], ["active", true], ["company_id", 6], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
15:08:43 web.1  |   Role Load (0.5ms)  SELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
15:08:43 web.1  | JWT tenant context set: company_id=6 for user=5 with role=owner
15:08:43 web.1  | [SECURITY] tenant_context_set: {:severity=>"low", :user_id=>5, :company_id=>6, :role=>"owner"}
15:08:43 web.1  | API authenticated via JWT for user: 5
15:08:43 web.1  |   Contract Load (0.7ms)  SELECT "contracts".* FROM "contracts" WHERE "contracts"."id" = $1 LIMIT $2  [["id", 9], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:17:in `block in accept'
15:08:43 web.1  |   Company Load (0.5ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2  [["id", 2], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:21:in `block in accept'
15:08:43 web.1  |   CompanyUserRole Load (0.9ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."company_id" = $3 AND "company_user_roles"."active" = $4 LIMIT $5  [["active", true], ["user_id", 5], ["company_id", 2], ["active", true], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:19:in `block in accept'
15:08:43 web.1  |   Role Load (0.5ms)  SELECT "roles".* FROM "roles" WHERE "roles"."name" = $1 LIMIT $2  [["name", "employee"], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:24:in `block in accept'
15:08:43 web.1  |   CompanyUserRole Exists? (0.6ms)  SELECT 1 AS one FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 LIMIT $3  [["active", true], ["user_id", 5], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:25:in `block in accept'
15:08:43 web.1  |   TRANSACTION (0.3ms)  BEGIN
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:27:in `block in accept'
15:08:43 web.1  |   CompanyUserRole Exists? (0.7ms)  SELECT 1 AS one FROM "company_user_roles" WHERE "company_user_roles"."company_id" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."role_id" = $3 AND "company_user_roles"."active" = $4 LIMIT $5  [["company_id", 2], ["user_id", 5], ["role_id", 2], ["active", true], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:27:in `block in accept'
15:08:43 web.1  |   CompanyUserRole Create (1.2ms)  INSERT INTO "company_user_roles" ("company_id", "user_id", "role_id", "created_at", "updated_at", "is_primary", "active", "previous_role_name") VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING "id"  [["company_id", 2], ["user_id", 5], ["role_id", 2], ["created_at", "2025-08-18 13:08:43.252878"], ["updated_at", "2025-08-18 13:08:43.252878"], ["is_primary", nil], ["active", true], ["previous_role_name", nil]]
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:27:in `block in accept'
15:08:43 web.1  |   TRANSACTION (3.3ms)  COMMIT
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:27:in `block in accept'
15:08:43 web.1  |   TRANSACTION (0.3ms)  BEGIN
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:28:in `block in accept'
15:08:43 web.1  |   Contract Exists? (0.8ms)  SELECT 1 AS one FROM "contracts" WHERE "contracts"."email" = $1 AND "contracts"."id" != $2 AND "contracts"."company_id" = $3 LIMIT $4  [["email", "<EMAIL>"], ["id", 9], ["company_id", 2], ["LIMIT", 1]]
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:28:in `block in accept'
15:08:43 web.1  |   Contract Update (1.1ms)  UPDATE "contracts" SET "user_id" = $1, "updated_at" = $2 WHERE "contracts"."id" = $3  [["user_id", 5], ["updated_at", "2025-08-18 13:08:43.274084"], ["id", 9]]
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:28:in `block in accept'
15:08:43 web.1  |   TRANSACTION (3.1ms)  COMMIT
15:08:43 web.1  |   ↳ app/controllers/api/v1/company_connections_controller.rb:28:in `block in accept'
15:08:43 web.1  | [AuthMetrics] JWT request tracked for user: 5
15:08:43 web.1  | Completed 200 OK in 131ms (Views: 0.3ms | ActiveRecord: 16.2ms | Allocations: 41011)
15:08:43 web.1  |
15:08:43 web.1  |
15:08:43 vite.1 | 3:08:43 PM [Browser] LOG [DEBUG] Response interceptor: SUCCESS path triggered for /api/v1/company_connections/9/accept
15:08:50 web.1  | Started GET "/api/v1/companies" for ************ at 2025-08-18 15:08:50 +0200
15:08:50 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - GET /api/v1/companies
15:08:50 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/api/v1/companies", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:50 web.1  | Processing by Api::V1::CompaniesController#index as JSON
15:08:50 web.1  | No JWT tenant context available
15:08:50 web.1  |   User Load (0.5ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2  [["id", 5], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
15:08:50 web.1  |   Company Load (0.5ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2  [["id", 6], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
15:08:50 web.1  |   CompanyUserRole Load (0.5ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5  [["active", true], ["user_id", 5], ["active", true], ["company_id", 6], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
15:08:50 web.1  |   Role Load (0.5ms)  SELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
15:08:50 web.1  | JWT tenant context set: company_id=6 for user=5 with role=owner
15:08:50 web.1  | [SECURITY] tenant_context_set: {:severity=>"low", :user_id=>5, :company_id=>6, :role=>"owner"}
15:08:50 web.1  | API authenticated via JWT for user: 5
15:08:50 web.1  |   CompanyUserRole Load (0.9ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 ORDER BY "company_user_roles"."created_at" ASC  [["active", true], ["user_id", 5], ["active", true]]
15:08:50 web.1  |   ↳ app/controllers/api/v1/companies_controller.rb:100:in `index'
15:08:50 web.1  |   Company Load (0.7ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" IN ($1, $2)  [["id", 6], ["id", 2]]
15:08:50 web.1  |   ↳ app/controllers/api/v1/companies_controller.rb:100:in `index'
15:08:50 web.1  |   Role Load (0.8ms)  SELECT "roles".* FROM "roles" WHERE "roles"."id" IN ($1, $2)  [["id", 1], ["id", 2]]
15:08:50 web.1  |   ↳ app/controllers/api/v1/companies_controller.rb:100:in `index'
15:08:50 web.1  |   ActiveStorage::Attachment Load (0.5ms)  SELECT "active_storage_attachments".* FROM "active_storage_attachments" WHERE "active_storage_attachments"."record_id" = $1 AND "active_storage_attachments"."record_type" = $2 AND "active_storage_attachments"."name" = $3 LIMIT $4  [["record_id", 6], ["record_type", "Company"], ["name", "logo"], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/models/company.rb:57:in `logo_url'
15:08:50 web.1  |   ActiveStorage::Attachment Load (0.4ms)  SELECT "active_storage_attachments".* FROM "active_storage_attachments" WHERE "active_storage_attachments"."record_id" = $1 AND "active_storage_attachments"."record_type" = $2 AND "active_storage_attachments"."name" = $3 LIMIT $4  [["record_id", 2], ["record_type", "Company"], ["name", "logo"], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/models/company.rb:57:in `logo_url'
15:08:50 web.1  | [AuthMetrics] JWT request tracked for user: 5
15:08:50 web.1  | Completed 200 OK in 124ms (Views: 0.7ms | ActiveRecord: 5.3ms | Allocations: 37484)
15:08:50 web.1  |
15:08:50 web.1  |
15:08:50 web.1  | user: mm
15:08:50 web.1  | GET /api/v1/companies
15:08:50 web.1  | USE eager loading detected
15:08:50 web.1  |   Company => [:logo_attachment]
15:08:50 web.1  |   Add to your query: .includes([:logo_attachment])
15:08:50 web.1  | Call stack
15:08:50 web.1  |   /home/<USER>/Projects/attendifyapp/app/models/company.rb:57:in `logo_url'
15:08:50 web.1  |   /home/<USER>/Projects/attendifyapp/app/controllers/api/v1/companies_controller.rb:100:in `index'
15:08:50 web.1  |   /home/<USER>/Projects/attendifyapp/app/controllers/api/v1/api_controller.rb:151:in `log_api_request'
15:08:50 web.1  |   /home/<USER>/Projects/attendifyapp/app/controllers/concerns/locale_handler.rb:17:in `block in use_locale_from_cookie_for_api'
15:08:50 web.1  |   /home/<USER>/Projects/attendifyapp/app/controllers/concerns/locale_handler.rb:17:in `use_locale_from_cookie_for_api'
15:08:50 web.1  |
15:08:50 web.1  |
15:08:50 vite.1 | 3:08:50 PM [Browser] LOG [DEBUG] Response interceptor: SUCCESS path triggered for /api/v1/companies
15:08:50 web.1  | Started GET "/companies/6/edit.json" for ************ at 2025-08-18 15:08:50 +0200
15:08:50 web.1  | Started GET "/companies/2/edit.json" for ************ at 2025-08-18 15:08:50 +0200
15:08:50 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - GET /companies/6/edit.json
15:08:50 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/companies/6/edit.json", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:50 web.1  | Processing by CompaniesController#edit as JSON
15:08:50 web.1  |   Parameters: {"id"=>"6"}
15:08:50 web.1  | [AUTH DEBUG] require_login START: controller=companies, action=edit, path=/companies/6/edit.json, format=application/json, accept=application/json
15:08:50 web.1  |   User Load (0.6ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2  [["id", 5], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
15:08:50 web.1  |   Company Load (0.5ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2  [["id", 6], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
15:08:50 web.1  |   CompanyUserRole Load (0.7ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5  [["active", true], ["user_id", 5], ["active", true], ["company_id", 6], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
15:08:50 web.1  |   Role Load (0.6ms)  SELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
15:08:50 web.1  | JWT tenant context set: company_id=6 for user=5 with role=owner
15:08:50 web.1  | [SECURITY] tenant_context_set: {:severity=>"low", :user_id=>5, :company_id=>6, :role=>"owner"}
15:08:50 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - GET /companies/2/edit.json
15:08:50 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/companies/2/edit.json", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:50 web.1  | Processing by CompaniesController#edit as JSON
15:08:50 web.1  | Rails controller JWT authentication successful for user: 5
15:08:50 web.1  |   Parameters: {"id"=>"2"}
15:08:50 web.1  | [AUTH DEBUG] require_login START: controller=companies, action=edit, path=/companies/2/edit.json, format=application/json, accept=application/json
15:08:50 web.1  | [AUTH DEBUG] require_login: Authenticated via JWT for user: 5
15:08:50 web.1  | Using JWT tenant context: company_id=6
15:08:50 web.1  |   User Load (3.0ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2  [["id", 5], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
15:08:50 web.1  |   Company Load (5.1ms)  SELECT "companies".* FROM "companies" INNER JOIN "company_user_roles" ON "companies"."id" = "company_user_roles"."company_id" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "companies"."id" = $3 LIMIT $4  [["active", true], ["user_id", 5], ["id", 6], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/companies_controller.rb:225:in `set_company'
15:08:50 web.1  |
15:08:50 web.1  |  ---------- current_tenant: #<Company id: 6, name: "Vyjimecne Silna Firma 2fd15", slug: "vyjimecne-silna-firma-2fd15", subdomain: "vyjimecne-silna-firma-2fd15", created_at: "2025-08-18 15:08:29.********* +0200", updated_at: "2025-08-18 15:08:29.********* +0200", paid_terms_accepted_at: false, is_personal: true, web: nil, description: nil, address: nil, phone: nil, contracts_count: 1> ----------
15:08:50 web.1  |
15:08:50 web.1  |   Company Load (3.6ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2  [["id", 6], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
15:08:50 web.1  |   CompanyUserRole Load (6.2ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."company_id" = $3 LIMIT $4  [["active", true], ["user_id", 5], ["company_id", 6], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/models/user.rb:76:in `role_in'
15:08:50 web.1  |   CompanyUserRole Load (2.1ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5  [["active", true], ["user_id", 5], ["active", true], ["company_id", 6], ["LIMIT", 1]]
15:08:50 web.1  |   CACHE Role Load (0.0ms)  SELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
15:08:50 web.1  |   ↳ app/models/user.rb:76:in `role_in'
15:08:50 web.1  |   Role Load (6.3ms)  SELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", 1]]
15:08:50 web.1  |   ActiveStorage::Attachment Load (0.6ms)  SELECT "active_storage_attachments".* FROM "active_storage_attachments" WHERE "active_storage_attachments"."record_id" = $1 AND "active_storage_attachments"."record_type" = $2 AND "active_storage_attachments"."name" = $3 LIMIT $4  [["record_id", 6], ["record_type", "Company"], ["name", "logo"], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
15:08:50 web.1  | JWT tenant context set: company_id=6 for user=5 with role=owner
15:08:50 web.1  |   ↳ app/models/company.rb:57:in `logo_url'
15:08:50 web.1  | Completed 200 OK in 125ms (Views: 0.6ms | ActiveRecord: 14.4ms | Allocations: 21189)
15:08:50 web.1  |
15:08:50 web.1  |
15:08:50 web.1  | [SECURITY] tenant_context_set: {:severity=>"low", :user_id=>5, :company_id=>6, :role=>"owner"}
15:08:50 web.1  | Rails controller JWT authentication successful for user: 5
15:08:50 web.1  | [AUTH DEBUG] require_login: Authenticated via JWT for user: 5
15:08:50 web.1  | Using JWT tenant context: company_id=6
15:08:50 web.1  |   Company Load (1.0ms)  SELECT "companies".* FROM "companies" INNER JOIN "company_user_roles" ON "companies"."id" = "company_user_roles"."company_id" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "companies"."id" = $3 LIMIT $4  [["active", true], ["user_id", 5], ["id", 2], ["LIMIT", 1]]
15:08:50 web.1  |   ↳ app/controllers/companies_controller.rb:225:in `set_company'
15:08:50 web.1  |
15:08:50 web.1  |  ---------- current_tenant: #<Company id: 6, name: "Vyjimecne Silna Firma 2fd15", slug: "vyjimecne-silna-firma-2fd15", subdomain: "vyjimecne-silna-firma-2fd15", created_at: "2025-08-18 15:08:29.********* +0200", updated_at: "2025-08-18 15:08:29.********* +0200", paid_terms_accepted_at: false, is_personal: true, web: nil, description: nil, address: nil, phone: nil, contracts_count: 1> ----------
15:08:50 web.1  |
15:08:51 web.1  |   CompanyUserRole Load (0.5ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."company_id" = $3 LIMIT $4  [["active", true], ["user_id", 5], ["company_id", 2], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/user.rb:76:in `role_in'
15:08:51 web.1  |   Role Load (0.4ms)  SELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2  [["id", 2], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/user.rb:76:in `role_in'
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] Response interceptor: SUCCESS path triggered for /companies/6/edit.json
15:08:51 web.1  |   Subscription Load (1.7ms)  SELECT "subscriptions".* FROM "subscriptions" WHERE "subscriptions"."company_id" = $1 AND (start_date <= '2025-08-18' AND expire_date >= '2025-08-18' AND status = 'active') ORDER BY "subscriptions"."expire_date" DESC LIMIT $2  [["company_id", 2], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/company.rb:45:in `current_subscription'
15:08:51 web.1  |   Plan Load (0.6ms)  SELECT "plans".* FROM "plans" WHERE "plans"."id" = $1 LIMIT $2  [["id", 2], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/company.rb:50:in `current_plan'
15:08:51 web.1  | Redirected to http://************:5100/?locale=cs
15:08:51 web.1  | Completed 302 Found in 103ms (ActiveRecord: 19.2ms | Allocations: 16986)
15:08:51 web.1  |
15:08:51 web.1  |
15:08:51 web.1  | Started GET "/?locale=cs" for ************ at 2025-08-18 15:08:51 +0200
15:08:51 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - GET /?locale=cs
15:08:51 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/?locale=cs", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:51 web.1  | Started GET "/cs" for ************ at 2025-08-18 15:08:51 +0200
15:08:51 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - GET /cs
15:08:51 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/cs", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:51 web.1  | Processing by SpaController#index as JSON
15:08:51 web.1  |   Parameters: {"locale"=>"cs"}
15:08:51 web.1  | [AUTH DEBUG] SpaController: Format=application/json, Path=/cs, Skipping auth
15:08:51 web.1  | No JWT tenant context available
15:08:51 web.1  | Completed 401 Unauthorized in 1ms (Views: 0.2ms | ActiveRecord: 0.0ms | Allocations: 208)
15:08:51 web.1  |
15:08:51 web.1  |
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] Response interceptor: ERROR path triggered {
15:08:51 vite.1 |   "status": 401,
15:08:51 vite.1 |   "url": "/companies/2/edit.json",
15:08:51 vite.1 |   "errorMessage": "Request failed with status code 401"
15:08:51 vite.1 | }
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] hasJwtToken: Checking store availability
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] hasJwtToken: Token exists? true isAuthenticated: true
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG JWT token expired, attempting refresh...
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] AuthService.refreshToken: Method called
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] AuthService.refreshToken: axios instance ID: 0.8147675283517427
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG Attempting to refresh JWT access token...
15:08:51 web.1  | Started POST "/api/v1/auth/refresh_token.json" for ************ at 2025-08-18 15:08:51 +0200
15:08:51 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - POST /api/v1/auth/refresh_token.json
15:08:51 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/api/v1/auth/refresh_token.json", :method=>"POST", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:51 web.1  | Processing by Api::V1::AuthController#refresh_token as JSON
15:08:51 web.1  |   Parameters: {"auth"=>{}}
15:08:51 web.1  |   User Load (0.5ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2  [["id", 5], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/controllers/api/v1/auth_controller.rb:261:in `refresh_token'
15:08:51 web.1  |   CompanyUserRole Load (0.6ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."is_primary" = $3 LIMIT $4  [["active", true], ["user_id", 5], ["is_primary", true], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/user.rb:49:in `primary_company'
15:08:51 web.1  |   Company Load (0.4ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2  [["id", 6], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/user.rb:49:in `primary_company'
15:08:51 web.1  | Revoked JWT with jti: 4cd8f2e2-da70-4b16-9a31-05f0b608c868 for user: 5
15:08:51 web.1  |   CACHE CompanyUserRole Load (0.0ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."is_primary" = $3 LIMIT $4  [["active", true], ["user_id", 5], ["is_primary", true], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/user.rb:49:in `primary_company'
15:08:51 web.1  |   CACHE Company Load (0.0ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2  [["id", 6], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/user.rb:49:in `primary_company'
15:08:51 web.1  | [SECURITY] Refresh token rotated - User: 5, Family: 13280f01-53e7-463b-aea9-6345d93d2b45, Rotation: 1
15:08:51 web.1  | Redis GET: tymboxapp:jwt:sessions:5:74cee41d-239b-42a1-96bc-97934a8ba5f9
15:08:51 web.1  | Redis SET: tymboxapp:jwt:sessions:5:74cee41d-239b-42a1-96bc-97934a8ba5f9 (TTL: 7776000)
15:08:51 web.1  | Redis JWT session updated with new access token
15:08:51 web.1  |   CACHE CompanyUserRole Load (0.0ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."is_primary" = $3 LIMIT $4  [["active", true], ["user_id", 5], ["is_primary", true], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/user.rb:49:in `primary_company'
15:08:51 web.1  |   CACHE Company Load (0.0ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2  [["id", 6], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/user.rb:49:in `primary_company'
15:08:51 web.1  | Completed 200 OK in 39ms (Views: 0.3ms | ActiveRecord: 1.6ms | Allocations: 8840)
15:08:51 web.1  |
15:08:51 web.1  |
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] Response interceptor: SUCCESS path triggered for /api/v1/auth/refresh_token.json
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG JWT token refresh successful
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] SET_JWT_TOKEN called with token: true
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [Cable] Reconnecting to Action Cable
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🟡 [Cable] Attempting connection to: ws://************:5100/cable?token=eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************.QP0ahuYXvvMXXb00lF6oOqiSTBms84AWK_wB_t3qols
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🔑 [Cable] Token length: 273 characters
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🌐 [Cable] Browser protocol: http:
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🌐 [Cable] Browser host: ************:5100
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🌐 [Cable] Full browser URL: http://************:5100/cs/companies
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🔧 [Cable] About to call createConsumer...
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🔧 [Cable] createConsumer returned: {
15:08:51 vite.1 |   "_url": "ws://************:5100/cable?token=eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************.QP0ahuYXvvMXXb00lF6oOqiSTBms84AWK_wB_t3qols",
15:08:51 vite.1 |   "subscriptions": {
15:08:51 vite.1 |     "consumer": "[Circular]",
15:08:51 vite.1 |     "guarantor": {
15:08:51 vite.1 |       "subscriptions": "[Circular]",
15:08:51 vite.1 |       "pendingSubscriptions": []
15:08:51 vite.1 |     },
15:08:51 vite.1 |     "subscriptions": []
15:08:51 vite.1 |   },
15:08:51 vite.1 |   "connection": {
15:08:51 vite.1 |     "consumer": "[Circular]",
15:08:51 vite.1 |     "subscriptions": "[Circular]",
15:08:51 vite.1 |     "monitor": {
15:08:51 vite.1 |       "connection": "[Circular]",
15:08:51 vite.1 |       "reconnectAttempts": 0
15:08:51 vite.1 |     },
15:08:51 vite.1 |     "disconnected": true
15:08:51 vite.1 |   },
15:08:51 vite.1 |   "subprotocols": []
15:08:51 vite.1 | }
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🔧 [Cable] Consumer connection state: null
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🔧 [Cable] Consumer connection URL: null
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🔧 [Cable] Consumer connection object: {
15:08:51 vite.1 |   "consumer": {
15:08:51 vite.1 |     "_url": "ws://************:5100/cable?token=eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************.QP0ahuYXvvMXXb00lF6oOqiSTBms84AWK_wB_t3qols",
15:08:51 vite.1 |     "subscriptions": {
15:08:51 vite.1 |       "consumer": "[Circular]",
15:08:51 vite.1 |       "guarantor": {
15:08:51 vite.1 |         "subscriptions": "[Circular]",
15:08:51 vite.1 |         "pendingSubscriptions": []
15:08:51 vite.1 |       },
15:08:51 vite.1 |       "subscriptions": []
15:08:51 vite.1 |     },
15:08:51 vite.1 |     "connection": "[Circular]",
15:08:51 vite.1 |     "subprotocols": []
15:08:51 vite.1 |   },
15:08:51 vite.1 |   "subscriptions": "[Circular]",
15:08:51 vite.1 |   "monitor": {
15:08:51 vite.1 |     "connection": "[Circular]",
15:08:51 vite.1 |     "reconnectAttempts": 0
15:08:51 vite.1 |   },
15:08:51 vite.1 |   "disconnected": true
15:08:51 vite.1 | }
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🔧 [Cable] Consumer connection events: {}
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🔧 [Cable] Setting up connection event listeners...
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG 🔧 [Cable] Explicitly opening connection...
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG Token refresh successful, retrying original request
15:08:51 web.1  | Started GET "/companies/2/edit.json" for ************ at 2025-08-18 15:08:51 +0200
15:08:51 web.1  | Started GET "/cable?token=[FILTERED]" for ************ at 2025-08-18 15:08:51 +0200
15:08:51 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - GET /companies/2/edit.json
15:08:51 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/companies/2/edit.json", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:51 web.1  | Processing by CompaniesController#edit as JSON
15:08:51 web.1  |   Parameters: {"id"=>"2"}
15:08:51 web.1  | [AUTH DEBUG] require_login START: controller=companies, action=edit, path=/companies/2/edit.json, format=application/json, accept=application/json
15:08:51 web.1  |   User Load (0.4ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2  [["id", 5], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:172:in `find_user_from_payload'
15:08:51 web.1  |   Company Load (0.4ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2  [["id", 6], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:189:in `set_tenant_from_jwt'
15:08:51 web.1  |   CompanyUserRole Load (0.6ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."active" = $3 AND "company_user_roles"."company_id" = $4 LIMIT $5  [["active", true], ["user_id", 5], ["active", true], ["company_id", 6], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:204:in `set_tenant_from_jwt'
15:08:51 web.1  |   Role Load (0.5ms)  SELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2  [["id", 1], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/controllers/concerns/jwt_authenticatable.rb:218:in `set_tenant_from_jwt'
15:08:51 web.1  | JWT tenant context set: company_id=6 for user=5 with role=owner
15:08:51 web.1  | [SECURITY] tenant_context_set: {:severity=>"low", :user_id=>5, :company_id=>6, :role=>"owner"}
15:08:51 web.1  | Rails controller JWT authentication successful for user: 5
15:08:51 web.1  | [AUTH DEBUG] require_login: Authenticated via JWT for user: 5
15:08:51 web.1  | Using JWT tenant context: company_id=6
15:08:51 web.1  |   Company Load (0.5ms)  SELECT "companies".* FROM "companies" INNER JOIN "company_user_roles" ON "companies"."id" = "company_user_roles"."company_id" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "companies"."id" = $3 LIMIT $4  [["active", true], ["user_id", 5], ["id", 2], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/controllers/companies_controller.rb:225:in `set_company'
15:08:51 web.1  |
15:08:51 web.1  |  ---------- current_tenant: #<Company id: 6, name: "Vyjimecne Silna Firma 2fd15", slug: "vyjimecne-silna-firma-2fd15", subdomain: "vyjimecne-silna-firma-2fd15", created_at: "2025-08-18 15:08:29.********* +0200", updated_at: "2025-08-18 15:08:29.********* +0200", paid_terms_accepted_at: false, is_personal: true, web: nil, description: nil, address: nil, phone: nil, contracts_count: 1> ----------
15:08:51 web.1  |
15:08:51 web.1  |   CompanyUserRole Load (0.6ms)  SELECT "company_user_roles".* FROM "company_user_roles" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "company_user_roles"."company_id" = $3 LIMIT $4  [["active", true], ["user_id", 5], ["company_id", 2], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/user.rb:76:in `role_in'
15:08:51 web.1  |   Role Load (0.4ms)  SELECT "roles".* FROM "roles" WHERE "roles"."id" = $1 LIMIT $2  [["id", 2], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/user.rb:76:in `role_in'
15:08:51 web.1  |   Subscription Load (0.6ms)  SELECT "subscriptions".* FROM "subscriptions" WHERE "subscriptions"."company_id" = $1 AND (start_date <= '2025-08-18' AND expire_date >= '2025-08-18' AND status = 'active') ORDER BY "subscriptions"."expire_date" DESC LIMIT $2  [["company_id", 2], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/company.rb:45:in `current_subscription'
15:08:51 web.1  |   Plan Load (0.4ms)  SELECT "plans".* FROM "plans" WHERE "plans"."id" = $1 LIMIT $2  [["id", 2], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/models/company.rb:50:in `current_plan'
15:08:51 web.1  | Redirected to http://************:5100/?locale=cs
15:08:51 web.1  | Completed 302 Found in 61ms (ActiveRecord: 4.3ms | Allocations: 12537)
15:08:51 web.1  |
15:08:51 web.1  |
15:08:51 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - GET /cable?token=eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************.QP0ahuYXvvMXXb00lF6oOqiSTBms84AWK_wB_t3qols
15:08:51 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/cable?token=eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************.QP0ahuYXvvMXXb00lF6oOqiSTBms84AWK_wB_t3qols", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:51 web.1  | Started GET "/cable?token=[FILTERED]" [WebSocket] for ************ at 2025-08-18 15:08:51 +0200
15:08:51 web.1  | Successfully upgraded to WebSocket (REQUEST_METHOD: GET, HTTP_CONNECTION: Upgrade, HTTP_UPGRADE: websocket)
15:08:51 web.1  | [JWT] Action Cable: Token found in query params
15:08:51 web.1  |   User Load (0.5ms)  SELECT "users".* FROM "users" WHERE "users"."id" = $1 LIMIT $2  [["id", 5], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/channels/application_cable/connection.rb:37:in `authenticate_from_jwt'
15:08:51 web.1  | [JWT] Action Cable: <NAME_EMAIL>
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG ✅ [Cable] Successfully connected to Action Cable
15:08:51 web.1  |   Company Load (1.4ms)  SELECT "companies".* FROM "companies" WHERE "companies"."id" = $1 LIMIT $2  [["id", 6], ["LIMIT", 1]]
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [Cable] No subscriptions to restore
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [Cable] Connection promise cleared
15:08:51 web.1  |   ↳ app/channels/application_cable/connection.rb:96:in `set_tenant_from_jwt'
15:08:51 web.1  |   Company Exists? (1.5ms)  SELECT 1 AS one FROM "companies" INNER JOIN "company_user_roles" ON "companies"."id" = "company_user_roles"."company_id" WHERE "company_user_roles"."active" = $1 AND "company_user_roles"."user_id" = $2 AND "companies"."id" = $3 LIMIT $4  [["active", true], ["user_id", 5], ["id", 6], ["LIMIT", 1]]
15:08:51 web.1  |   ↳ app/channels/application_cable/connection.rb:98:in `set_tenant_from_jwt'
15:08:51 web.1  | [JWT] Action Cable: Set tenant to Company #6
15:08:51 web.1  | [ActionCable] [<EMAIL>] [Company:6] Registered connection (Z2lkOi8vdHltYm94YXBwL0NvbXBhbnkvNg:Z2lkOi8vdHltYm94YXBwL1VzZXIvNQ)
15:08:51 web.1  | Started GET "/?locale=cs" for ************ at 2025-08-18 15:08:51 +0200
15:08:51 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - GET /?locale=cs
15:08:51 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/?locale=cs", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:51 web.1  | Started GET "/cs" for ************ at 2025-08-18 15:08:51 +0200
15:08:51 web.1  | [SECURITY] Rack::Attack blocked request: safelist - ************ - GET /cs
15:08:51 web.1  | [SECURITY] rate_limit_violation: {:ip=>"************", :path=>"/cs", :method=>"GET", :match_type=>:safelist, :matched_data=>"allow-local-network"}
15:08:51 web.1  | Processing by SpaController#index as JSON
15:08:51 web.1  |   Parameters: {"locale"=>"cs"}
15:08:51 web.1  | [AUTH DEBUG] SpaController: Format=application/json, Path=/cs, Skipping auth
15:08:51 web.1  | No JWT tenant context available
15:08:51 web.1  | Completed 401 Unauthorized in 1ms (Views: 0.2ms | ActiveRecord: 0.0ms | Allocations: 202)
15:08:51 web.1  |
15:08:51 web.1  |
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] Response interceptor: ERROR path triggered {
15:08:51 vite.1 |   "status": 401,
15:08:51 vite.1 |   "url": "/companies/2/edit.json",
15:08:51 vite.1 |   "errorMessage": "Request failed with status code 401"
15:08:51 vite.1 | }
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] hasJwtToken: Checking store availability
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] hasJwtToken: Token exists? true isAuthenticated: true
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] handleAuthFailureAndRedirect: Clearing auth state locally
15:08:51 vite.1 | 3:08:51 PM [Browser] LOG [DEBUG] clearAuth called - clearing all auth state
15:08:51 vite.1 | 3:08:51 PM [Browser] ERROR Error fetching company logo: {
15:08:51 vite.1 |   "message": "Request failed with status code 401",
15:08:51 vite.1 |   "name": "AxiosError",
15:08:51 vite.1 |   "stack": "AxiosError: Request failed with status code 401\n    at settle (http://************:5100/vite-dev/@fs/home/<USER>/Projects/attendifyapp/node_modules/.vite/deps/axios.js?v=ebaa61be:1253:12)\n    at XMLHttpRequest.onloadend (http://************:5100/vite-dev/@fs/home/<USER>/Projects/attendifyapp/node_modules/.vite/deps/axios.js?v=ebaa61be:1585:7)\n    at Axios.request (http://************:5100/vite-dev/@fs/home/<USER>/Projects/attendifyapp/node_modules/.vite/deps/axios.js?v=ebaa61be:2143:41)\n    at async Axios.request (http://************:5100/vite-dev/@fs/home/<USER>/Projects/attendifyapp/node_modules/.vite/deps/axios.js?v=ebaa61be:2139:14)\n    at Axios.request (http://************:5100/vite-dev/@fs/home/<USER>/Projects/attendifyapp/node_modules/.vite/deps/axios.js?v=ebaa61be:2143:41)",
15:08:51 vite.1 |   "config": {
15:08:51 vite.1 |     "transitional": {
15:08:51 vite.1 |       "silentJSONParsing": true,
15:08:51 vite.1 |       "forcedJSONParsing": true,
15:08:51 vite.1 |       "clarifyTimeoutError": false
15:08:51 vite.1 |     },
15:08:51 vite.1 |     "adapter": [
15:08:51 vite.1 |       "xhr",
15:08:51 vite.1 |       "http",
15:08:51 vite.1 |       "fetch"
15:08:51 vite.1 |     ],
15:08:51 vite.1 |     "transformRequest": [
15:08:51 vite.1 |       null
15:08:51 vite.1 |     ],
15:08:51 vite.1 |     "transformResponse": [
15:08:51 vite.1 |       null
15:08:51 vite.1 |     ],
15:08:51 vite.1 |     "timeout": 0,
15:08:51 vite.1 |     "xsrfCookieName": "XSRF-TOKEN",
15:08:51 vite.1 |     "xsrfHeaderName": "X-XSRF-TOKEN",
15:08:51 vite.1 |     "maxContentLength": -1,
15:08:51 vite.1 |     "maxBodyLength": -1,
15:08:51 vite.1 |     "env": {},
15:08:51 vite.1 |     "headers": {
15:08:51 vite.1 |       "Accept": "application/json",
15:08:51 vite.1 |       "Authorization": "Bearer eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************.QP0ahuYXvvMXXb00lF6oOqiSTBms84AWK_wB_t3qols"
15:08:51 vite.1 |     },
15:08:51 vite.1 |     "method": "get",
15:08:51 vite.1 |     "url": "/companies/2/edit.json",
15:08:51 vite.1 |     "allowAbsoluteUrls": true,
15:08:51 vite.1 |     "_retry": true
15:08:51 vite.1 |   },
15:08:51 vite.1 |   "code": "ERR_BAD_REQUEST",
15:08:51 vite.1 |   "status": 401
15:08:51 vite.1 | }
