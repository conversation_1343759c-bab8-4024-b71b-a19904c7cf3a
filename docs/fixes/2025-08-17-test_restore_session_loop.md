# restore_session Loop Analysis

## Current Flow:

1. **App starts** → `initializeApp()` (line 60)
2. **Checks if auth page** (lines 70-81)
   - If NOT auth page → calls `AuthService.ensureAuthenticated()` (line 90)
3. **ensureAuthenticated** checks for token
   - If no token → calls `restoreSessionToken()` (line 205)
4. **restoreSessionToken** makes request with `_skipRefreshInterceptor: true`
5. **If 401 response**:
   - Interceptor catches it (line 160)
   - Sees `_skipRefreshInterceptor` flag (line 185)
   - Enters special handling for restore_session (line 193)
   - Clears auth state locally
   - Redirects to login IF not already there

## Loop Prevention Mechanisms:

1. ✅ **`_skipRefreshInterceptor: true`** - Prevents token refresh attempt
2. ✅ **Special handling for restore_session URL** - Different path than normal 401
3. ✅ **Check for login page** - Won't redirect if already on login
4. ✅ **Server clears cookie** - auth_controller.rb:377 clears stale cookie

## Potential Loop Scenario:

Could a loop still happen? Let's trace:

1. restore_session fails → clears auth → redirects to login
2. User on login page (no more auth checks)
3. User logs in → gets new session → works fine

**NO LOOP** because:
- Once redirected to login, no more restore_session calls
- AUTH_BYPASS_PATHS prevents auth checks on login page

## VERDICT: NO LOOP RISK

The restore_session is properly protected against loops by:
1. Server-side cookie clearing
2. Client-side special handling
3. No auth checks on login pages