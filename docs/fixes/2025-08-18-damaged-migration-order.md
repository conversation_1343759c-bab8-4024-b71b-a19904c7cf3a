# Migration Order Damage and Recovery Analysis

**Date**: 2025-08-18  
**Issue**: Damaged Rails migration chain due to manual database changes and incorrect timestamp generation  
**Status**: RESOLVED - Safe to deploy  
**Criticality**: HIGH - Could have broken production deployment  

## Problem Summary

The Rails migration system was corrupted by:
1. Manual database schema modifications in development
2. Migration files created with incorrect timestamps (not using `rails generate migration`)
3. Chronologically impossible migration sequences
4. Missing migration files that were run in production

## Root Cause Analysis

### Primary Issues Discovered

1. **Temporal Paradox Migration**:
   - Migration `20240320000001_add_trial_fields_to_subscriptions.rb` (March 20, 2024)
   - Tried to modify `subscriptions` table 8 months before it was created
   - Subscriptions table created in `20241124123154_create_subscriptions.rb` (November 24, 2024)

2. **Phantom Migration**:
   - `20250817135339` existed in development `schema_migrations` table
   - No corresponding migration file existed
   - Likely added columns manually then lost the migration file

3. **Manual Schema Changes**:
   - Columns added directly to database without migrations:
     - `show_managers_in_team_status`
     - `team_status_visibility` 
     - `work_management_access`

4. **Incorrect Timestamp Generation**:
   - Migration files created manually instead of using `rails generate migration`
   - Led to chronologically impossible sequences

## Investigation Process

### Production Database State
Production migrations (83 total) as of 2025-08-18:
```
20240320000001  # Trial fields migration - WORKED IN PRODUCTION
20241113041127  # Devise create users
20241115103543  # Create companies
...
20250523230357  # Last production migration
```

### Development Database Issues
- Migration `20240320000001` failed because subscriptions table didn't exist yet
- Phantom migration `20250817135339` in schema_migrations table without file
- Manual database changes created schema drift

### Git History Analysis
```bash
# Migration creation history
git log --follow --oneline -- "*add_trial_fields_to_subscriptions*"
b6da242 Add trial subscription feature with activation and confirmation dialog

# Commit shows future date but migration has past timestamp
Date: Tue Apr 15 11:52:26 2025 +0200  # Future date!
Migration: 20240320000001               # Past timestamp!
```

## How Production Worked Despite Broken Chain

The only way production could have successfully run migration `20240320000001` is if:
1. Someone manually created the `subscriptions` table in production before the migration ran
2. OR there was manual intervention during deployment

**Evidence**: Production `schema_migrations` table shows `20240320000001` as successfully executed.

## Critical Discovery: Production vs Development Mismatch

### What Could Have Gone Wrong
If we had deployed the renamed migration (`20241124125800_add_trial_fields_to_subscriptions.rb`):
- Rails would see it as a "new" migration
- Would attempt to create columns that already exist in production
- Deployment would fail with `PG::DuplicateColumn` error
- **Production would be broken**

### Production Database Verification
```bash
# Command used to check production state
render@srv-cvirgc6uk2gs739q235g-745df57bdd-vlq8v:~/project/src$ rails runner "puts ActiveRecord::SchemaMigration.pluck(:version).sort"

# Result: 83 migrations from 20240320000001 to 20250523230357
```

## Resolution Steps Taken

### 1. Reverted Migration to Original Timestamp
```bash
# CRITICAL: Revert to match production expectations
mv db/migrate/20241124125800_add_trial_fields_to_subscriptions.rb \
   db/migrate/20240320000001_add_trial_fields_to_subscriptions.rb
```

### 2. Migration Comparison Analysis
- **Production**: 83 migrations (baseline)
- **Development**: 92 migrations (9 newer)
- **Missing in Dev**: 0 (✅ All production migrations present)
- **New in Dev**: 9 migrations ready for deployment

### 3. Deployment Safety Verification
**SAFE TO DEPLOY**: All production migrations exist in development with exact timestamp match.

## Current State (POST-RESOLUTION)

### Development Database
- All 83 production migrations present with correct timestamps
- 9 additional migrations ready for deployment
- Migration chain integrity restored

### Production Database  
- Remains unchanged and stable
- Ready to receive 9 new migrations on next deployment

### New Migrations to be Deployed
1. `20250630112338` - Add JWT fields to invitations
2. `20250717064038` - Create service contracts  
3. `20250717064137` - Add service contract to works
4. `20250721192722` - Backfill event status for legacy events
5. `20250808053345` - Add contracts count to companies
6. `20250808090000` - Create feedbacks
7. `20250816123239` - Add previous role to company user roles
8. `20250817145606` - Add feature visibility to company settings
9. `20250818082001` - Add missing visibility columns to company settings

## Lessons Learned and Prevention Measures

### Root Causes of the Problem
1. **Manual Database Modifications**: Direct schema changes bypassed Rails migration system
2. **Incorrect Migration Generation**: Not using `rails generate migration` command
3. **Timestamp Manipulation**: Manual timestamp creation instead of Rails automatic generation

### Prevention Rules Added to CLAUDE.md
```markdown
Always create rails migration by running `rails generate migration ...`
Never edit database directly.
Never edit rails `schema.rb` directly
```

### Best Practices Established
1. **Always use Rails generators**: `rails generate migration DescriptiveName`
2. **Never edit schema.rb directly**: It's auto-generated
3. **Never manually create migration timestamps**: Let Rails handle chronology
4. **Check migration status before deployment**: Compare dev/prod migration lists
5. **Document all manual database changes**: Create proper migrations after emergency fixes

## Production Deployment Impact

### Pre-Resolution Risk
- **CRITICAL**: Deployment would have failed
- Production database corruption possible
- Downtime during failed migration rollback

### Post-Resolution State
- **SAFE**: All migrations aligned with production
- No risk of duplicate column errors
- Clean deployment path established

## Technical Details

### Schema Drift Detection
```bash
# Check for columns without migrations
rails runner "puts CompanySetting.column_names - ['id', 'created_at', 'updated_at', ...]"

# Result showed unmigrated columns:
# - show_managers_in_team_status  
# - team_status_visibility
# - work_management_access
```

### Migration Chain Validation
```bash
# Production migration count
echo "Production: 83 migrations"

# Development migration count  
find db/migrate/ -name "*.rb" | wc -l
echo "Development: 92 migrations"

# Verify no missing migrations
comm -13 development_migrations.txt production_migrations.txt
# Result: No output (no missing migrations)
```

## Future Monitoring

### Early Warning Signs
1. **Migration status mismatch**: `rails db:migrate:status` shows inconsistencies
2. **Schema drift**: `rails db:schema:dump` shows unexpected changes
3. **Column existence errors**: Database columns exist without corresponding migrations
4. **Phantom migrations**: Entries in `schema_migrations` without files

### Recommended Checks Before Deployment
```bash
# 1. Compare migration lists
rails runner "puts ActiveRecord::SchemaMigration.pluck(:version).sort" # Production
find db/migrate/ -name "*.rb" | sort | sed 's/.*\///g' | sed 's/_.*//g' # Development

# 2. Check for pending migrations
rails db:migrate:status

# 3. Verify schema consistency
rails db:schema:dump && git diff db/schema.rb
```

## Related Issues

### Linear Issue Created
- **TYM-107**: Refactor Company Settings to Hybrid JSONB/Column Architecture
- Addresses the underlying messy settings architecture that contributed to manual changes

### CLAUDE.md Updates
Added mandatory rules to prevent future migration system corruption:
- Always use `rails generate migration`
- Never edit database directly  
- Never edit `schema.rb` directly

## Conclusion

This incident highlighted critical weaknesses in our migration management process. The resolution prevented a production-breaking deployment and established better safeguards. The migration system is now stable and aligned between development and production environments.

**Final Status**: ✅ **RESOLVED - SAFE TO DEPLOY**