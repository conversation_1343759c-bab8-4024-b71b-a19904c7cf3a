import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Check } from "lucide-react";

const CompanyOverview = () => {
  const [activeTab, setActiveTab] = React.useState("company1");

  return (
    <div className="p-6 space-y-6">
      <div className="flex gap-2">
        {/* Company Switch Buttons */}
        <Button variant={activeTab === "company1" ? "default" : "outline"} onClick={() => setActiveTab("company1")}>Company A</Button>
        <Button variant={activeTab === "company2" ? "default" : "outline"} onClick={() => setActiveTab("company2")}>Company B</Button>
      </div>

      {activeTab === "company1" && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-4">
            {/* Placeholder: Company Details */}
            <Card>
              <CardContent className="p-4">
                <h2 className="text-xl font-semibold mb-2">Company Profile</h2>
                <p className="text-muted-foreground">[Company profile placeholder]</p>
              </CardContent>
            </Card>

            {/* Placeholder: Company Settings */}
            <Card>
              <CardContent className="p-4">
                <h2 className="text-xl font-semibold mb-2">Company Settings</h2>
                <p className="text-muted-foreground">[Settings placeholder]</p>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-4">
            {/* Freemium + Upgrade Card */}
            <Card className="border-2 border-dashed">
              <CardContent className="p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold">Your Plan: Free</h2>
                  <Badge variant="secondary">Freemium</Badge>
                </div>
                <p className="text-muted-foreground text-sm">
                  You’re using the free version. Upgrade to Plus to unlock helpful features that save you time and give you peace of mind.
                </p>

                <ul className="text-sm list-disc list-inside space-y-1">
                  <li><Check className="inline w-4 h-4 mr-1 text-green-500" />Real-time who’s-on-site overview</li>
                  <li><Check className="inline w-4 h-4 mr-1 text-green-500" />Risk-aware smart bookings</li>
                  <li><Check className="inline w-4 h-4 mr-1 text-green-500" />Simple team planner tools</li>
                  <li className="italic text-muted-foreground">+ more added regularly</li>
                </ul>

                <Button className="w-full">Upgrade to Plus for €9/month</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {activeTab === "company2" && (
        <p>[Placeholder for Company B’s settings and plan]</p>
      )}
    </div>
  );
};

export default CompanyOverview;
