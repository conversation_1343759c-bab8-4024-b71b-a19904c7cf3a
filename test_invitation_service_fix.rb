# ABOUTME: Rails console script to test InvitationService with user reload fix
# ABOUTME: Simulates the complete invitation acceptance flow

# Run this in Rails console: rails runner test_invitation_service_fix.rb

puts "=== Testing InvitationService with User Reload Fix ==="
puts ""

# Simulate invitation data
test_email = "test_invite_#{Time.now.to_i}@example.com"
test_company_id = Company.first&.id || 1
test_sender_id = User.first&.id || 1
test_token = SecureRandom.urlsafe_base64(32)

puts "Test parameters:"
puts "  Email: #{test_email}"
puts "  Company ID: #{test_company_id}"
puts "  Sender ID: #{test_sender_id}"
puts ""

# Call the service method directly
service = InvitationService.new
result = service.complete_user_registration(
  email: test_email,
  company_id: test_company_id,
  sender_id: test_sender_id,
  invitation_token: test_token,
  password: 'Test123456!',
  password_confirmation: 'Test123456!'
)

if result[:success]
  puts "✅ Registration successful!"
  user = result[:user]
  
  puts "\nUser details:"
  puts "  ID: #{user.id}"
  puts "  Email: #{user.email}"
  puts "  Primary Company: #{user.primary_company&.name || 'NIL'}"
  puts "  Primary Company ID: #{user.primary_company&.id || 'NIL'}"
  
  # Check what would be in JWT response
  if user.primary_company
    puts "\n✅ SUCCESS: User has primary company!"
    puts "This would be included in JWT response:"
    puts "  company_id: #{user.primary_company.id}"
    
    # Simulate what handle_successful_jwt_login would do
    jwt_payload = user.jwt_payload
    puts "\nJWT payload:"
    puts "  user_id: #{jwt_payload[:user_id]}"
    puts "  email: #{jwt_payload[:email]}"
    
    # The JWT response would include
    jwt_response = {
      success: true,
      access_token: '<JWT_TOKEN>',
      user: {
        id: user.id,
        email: user.email,
        company_id: user.primary_company&.id
      }
    }
    
    puts "\nJWT login response would be:"
    puts jwt_response.to_json
    
    if jwt_response[:user][:company_id]
      puts "\n✅ Frontend would receive company_id: #{jwt_response[:user][:company_id]}"
      puts "This allows frontend to set company context properly!"
    else
      puts "\n❌ Frontend would NOT receive company_id"
      puts "This would cause 'Please provide X-Company-ID header' error!"
    end
  else
    puts "\n❌ FAILURE: User has no primary company!"
    puts "This is the bug that causes the frontend error."
  end
  
  # Clean up
  puts "\nCleaning up test data..."
  user.company_user_roles.destroy_all
  user.primary_company&.destroy if user.primary_company&.is_personal
  user.destroy
  puts "✓ Test user cleaned up"
else
  puts "❌ Registration failed:"
  puts "  Error: #{result[:error]}"
  puts "  Details: #{result[:details]}"
end

puts "\n=== Test Complete ===\n"