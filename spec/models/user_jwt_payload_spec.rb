# spec/models/user_jwt_payload_spec.rb
require 'rails_helper'

RSpec.describe User, type: :model do
  describe '#jwt_payload' do
    let(:user) { create(:user) }
    let(:company_a) { create(:company, name: 'Company A') }
    let(:company_b) { create(:company, name: 'Company B') }
    let(:owner_role) { create(:role, name: 'owner') }

    before do
      create(:company_user_role, user: user, company: company_a, role: owner_role, is_primary: true)
      create(:company_user_role, user: user, company: company_b, role: owner_role, is_primary: false)
    end

    context 'when user has a primary company' do
      it 'includes the primary company_id in the payload' do
        payload = user.jwt_payload

        expect(payload[:user_id]).to eq(user.id)
        expect(payload[:email]).to eq(user.email)
        expect(payload[:company_id]).to eq(company_a.id)
        expect(payload[:jti]).to be_present
      end
    end

    context 'when primary company is changed' do
      it 'reflects the new primary company in subsequent payload generation' do
        # Verify initial state
        initial_payload = user.jwt_payload
        expect(initial_payload[:company_id]).to eq(company_a.id)

        # Change primary company
        user.set_primary_company(company_b)

        # Verify payload reflects the change
        new_payload = user.jwt_payload
        expect(new_payload[:company_id]).to eq(company_b.id)
        expect(new_payload[:user_id]).to eq(user.id) # Other fields unchanged
        expect(new_payload[:email]).to eq(user.email)
      end

      it 'generates unique JTI for each payload generation' do
        payload1 = user.jwt_payload
        payload2 = user.jwt_payload

        expect(payload1[:jti]).not_to eq(payload2[:jti])
        expect(payload1[:jti]).to be_present
        expect(payload2[:jti]).to be_present
      end
    end

    context 'when user has no primary company' do
      let(:user_without_primary) { create(:user) }

      before do
        create(:company_user_role, user: user_without_primary, company: company_a, role: owner_role, is_primary: false)
      end

      it 'includes nil company_id in the payload' do
        payload = user_without_primary.jwt_payload

        expect(payload[:user_id]).to eq(user_without_primary.id)
        expect(payload[:email]).to eq(user_without_primary.email)
        expect(payload[:company_id]).to be_nil
        expect(payload[:jti]).to be_present
      end
    end

    context 'integration with JWT company switch flow' do
      it 'supports the complete company switch workflow' do
        # 1. Generate initial JWT with company A
        initial_payload = user.jwt_payload
        initial_token = JwtService.encode_access_token(initial_payload)
        decoded_initial = JwtService.decode(initial_token)
        
        expect(decoded_initial[:company_id]).to eq(company_a.id)

        # 2. Simulate company switch (as done in companies_controller.rb)
        user.set_primary_company(company_b)
        
        # 3. Generate new JWT with updated company context
        new_payload = user.jwt_payload
        new_token = JwtService.encode_access_token(new_payload)
        decoded_new = JwtService.decode(new_token)
        
        expect(decoded_new[:company_id]).to eq(company_b.id)
        expect(decoded_new[:user_id]).to eq(user.id)
        
        # 4. Verify tokens are different but user remains the same
        expect(initial_token).not_to eq(new_token)
        expect(decoded_initial[:user_id]).to eq(decoded_new[:user_id])
        expect(decoded_initial[:company_id]).not_to eq(decoded_new[:company_id])
      end
    end

    context 'payload structure validation' do
      it 'includes all required fields' do
        payload = user.jwt_payload

        expect(payload).to have_key(:user_id)
        expect(payload).to have_key(:email)
        expect(payload).to have_key(:company_id)
        expect(payload).to have_key(:jti)
        
        # Verify field types
        expect(payload[:user_id]).to be_an(Integer)
        expect(payload[:email]).to be_a(String)
        expect(payload[:company_id]).to be_an(Integer).or(be_nil)
        expect(payload[:jti]).to be_a(String)
      end

      it 'generates UUID format JTI' do
        payload = user.jwt_payload
        
        # UUID v4 format: 8-4-4-4-12 hexadecimal digits
        uuid_pattern = /\A[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\z/i
        expect(payload[:jti]).to match(uuid_pattern)
      end
    end
  end
end