require 'rails_helper'

RSpec.describe Feedback, type: :model do
  it { is_expected.to define_enum_for(:category).with_values(bug_report: 0, something_confusing: 1, missing_feature: 2, general_message: 3) }
  it { is_expected.to define_enum_for(:status).with_values(new_: 0, reviewed: 1, useful: 2, duplicate: 3, noise: 4) }

  it { is_expected.to validate_presence_of(:page_url) }
  it { is_expected.to validate_presence_of(:message) }
  it { is_expected.to validate_length_of(:message).is_at_most(300) }
  it { is_expected.to validate_presence_of(:user_email) }

  it 'validates email format' do
    fb = build(:feedback, user_email: 'invalid')
    expect(fb).not_to be_valid
    expect(fb.errors[:user_email]).to be_present
  end
end

