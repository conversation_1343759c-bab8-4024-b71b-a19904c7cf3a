# ABOUTME: Tests for contract data integrity to prevent accidental data loss
# ABOUTME: Ensures contracts with associated records cannot be deleted

require 'rails_helper'

RSpec.describe Contract, type: :model do
  let(:company) { create(:company) }
  let(:contract) { create(:contract, company: company) }

  describe 'data integrity protection' do
    context 'when contract has associated daily logs' do
      before do
        create(:daily_log, contract: contract)
      end

      it 'prevents contract deletion to preserve audit trail' do
        expect { contract.destroy }.not_to change(Contract, :count)
        expect(contract.errors[:base].first).to include('daily logs')
      end
    end

    context 'when contract has associated events' do
      before do
        create(:event, contract: contract)
      end

      it 'prevents contract deletion to preserve history' do
        expect { contract.destroy }.not_to change(Contract, :count)
        expect(contract.errors[:base].first).to include('events')
      end
    end

    context 'when contract has associated work assignments' do
      before do
        create(:work_assignment, contract: contract)
      end

      it 'prevents contract deletion to preserve work history' do
        expect { contract.destroy }.not_to change(Contract, :count)
        expect(contract.errors[:base].first).to include('work assignments')
      end
    end

    context 'when contract has no associated records' do
      it 'allows contract deletion' do
        expect { contract.destroy }.to change(Contract, :count).by(-1)
        expect(contract.destroyed?).to be true
      end
    end

    context 'when contract is terminated but has associated records' do
      before do
        create(:daily_log, contract: contract)
        contract.terminate!
      end

      it 'still prevents deletion of terminated contracts with data' do
        expect(contract.terminated?).to be true
        expect { contract.destroy }.not_to change(Contract, :count)
        expect(contract.errors[:base].first).to include('daily logs')
      end
    end
  end
end