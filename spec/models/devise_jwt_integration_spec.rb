# spec/models/devise_jwt_integration_spec.rb
require 'rails_helper'

RSpec.describe 'Devise JWT Integration', type: :model do
  describe 'JWT revocation strategy configuration' do
    it 'User model has jwt_authenticatable module' do
      expect(User.devise_modules).to include(:jwt_authenticatable)
    end
    
    it 'User model uses JwtRevocationStrategy for revocation' do
      # Access the JWT mapping configuration through Devise
      jwt_mapping = Devise.mappings[:user].modules.find { |mod| mod == :jwt_authenticatable }
      expect(jwt_mapping).not_to be_nil
      
      # Verify the revocation strategy is properly configured at model level
      user = User.new
      expect(user.class.jwt_revocation_strategy).to eq(JwtRevocationStrategy)
    end
    
    it 'JwtRevocationStrategy responds to required devise-jwt interface methods' do
      strategy = JwtRevocationStrategy.new
      
      # Check instance methods required by devise-jwt
      expect(strategy).to respond_to(:revoke_jwt)
      expect(strategy).to respond_to(:jwt_revoked?)
    end
    
    it 'Devise JWT is properly configured' do
      # Verify that JWT authentication works by testing the configuration indirectly
      # The fact that we can create JWTs and use the revocation strategy confirms proper setup
      
      # Check that JWT secret is available
      expect(Rails.application.credentials.jwt_secret).to be_present
      
      # Verify JWT can be created (this would fail if not properly configured)
      user = create(:user)
      payload = user.jwt_payload
      token = JwtService.encode_access_token(payload)
      expect(token).to be_present
      
      # Verify the token can be decoded
      decoded = JwtService.decode(token)
      expect(decoded['user_id']).to eq(user.id)
    end
  end
  
  describe 'JWT revocation functionality' do
    let(:user) { create(:user) }
    let(:strategy) { JwtRevocationStrategy.new }
    
    it 'can revoke a JWT token' do
      # Create a complete payload with all required fields
      payload = {
        'user_id' => user.id,
        'email' => user.email,
        'company_id' => user.primary_company&.id,
        'jti' => SecureRandom.uuid,
        'exp' => 1.hour.from_now.to_i
      }
      
      # First, confirm it's not revoked
      expect(strategy.jwt_revoked?(payload, user)).to be false
      
      # Revoke the token
      strategy.revoke_jwt(payload, user)
      
      # Now it should be revoked
      expect(strategy.jwt_revoked?(payload, user)).to be true
    end
  end
end