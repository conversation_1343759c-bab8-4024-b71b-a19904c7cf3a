# ABOUTME: Unit tests for contract validation changes
# ABOUTME: Ensures terminated contracts don't prevent re-invitation with same email

require 'rails_helper'

RSpec.describe Contract, type: :model do
  let(:company) { create(:company) }

  describe 'email uniqueness validation' do
    context 'when a contract is terminated' do
      let!(:terminated_contract) do
        create(:contract, 
          company: company, 
          email: '<EMAIL>',
          status: 'terminated'
        )
      end

      it 'allows creating a new active contract with the same email' do
        new_contract = build(:contract,
          company: company,
          email: '<EMAIL>',
          status: 'active'
        )
        
        expect(new_contract).to be_valid
        expect { new_contract.save! }.not_to raise_error
      end

      it 'allows multiple terminated contracts with the same email' do
        another_terminated = build(:contract,
          company: company,
          email: '<EMAIL>',
          status: 'terminated'
        )
        
        expect(another_terminated).to be_valid
        expect { another_terminated.save! }.not_to raise_error
      end
    end

    context 'with non-terminated contracts' do
      let!(:active_contract) do
        create(:contract, 
          company: company, 
          email: '<EMAIL>',
          status: 'active'
        )
      end

      it 'prevents duplicate active contracts with same email' do
        duplicate = build(:contract,
          company: company,
          email: '<EMAIL>',
          status: 'active'
        )
        
        expect(duplicate).not_to be_valid
        expect(duplicate.errors[:email]).to be_present
      end

      it 'prevents duplicate suspended contracts with same email' do
        suspended = create(:contract,
          company: company,
          email: '<EMAIL>',
          status: 'suspended'
        )

        duplicate = build(:contract,
          company: company,
          email: '<EMAIL>',
          status: 'active'
        )
        
        expect(duplicate).not_to be_valid
        expect(duplicate.errors[:email]).to be_present
      end
    end

    context 'across different companies' do
      let(:another_company) { create(:company) }

      it 'allows same email in different companies' do
        contract1 = create(:contract,
          company: company,
          email: '<EMAIL>',
          status: 'active'
        )

        contract2 = build(:contract,
          company: another_company,
          email: '<EMAIL>',
          status: 'active'
        )
        
        expect(contract2).to be_valid
        expect { contract2.save! }.not_to raise_error
      end
    end
  end
end