# ABOUTME: Tests for User#leave_company method to ensure it doesn't affect other users
# ABOUTME: Specifically tests the critical bug where leaving users were removing is_primary flag from all company users

require 'rails_helper'

RSpec.describe User, '#leave_company' do
  let(:company) { create(:company) }
  let(:owner) { create(:user) }
  let(:employee_user) { create(:user) }
  let(:owner_role) { Role.find_or_create_by(name: 'owner') }
  let(:employee_role) { Role.find_or_create_by(name: 'employee') }

  before do
    # Set up owner with primary company
    owner_company_role = CompanyUserRole.create!(
      user: owner,
      company: company,
      role: owner_role,
      active: true,
      is_primary: true
    )
    
    # Set up employee in same company
    employee_company_role = CompanyUserRole.create!(
      user: employee_user,
      company: company,
      role: employee_role,
      active: true,
      is_primary: true
    )
    
    # Create active contracts for both users
    create(:contract, user: owner, company: company, status: 'active')
    create(:contract, user: employee_user, company: company, status: 'active')
  end

  describe 'when an employee leaves the company' do
    it 'does not affect the owner\'s primary company setting' do
      # Verify initial state
      expect(owner.primary_company).to eq(company)
      expect(employee_user.primary_company).to eq(company)
      
      owner_role_before = CompanyUserRole.find_by(user: owner, company: company)
      expect(owner_role_before.is_primary).to be true
      expect(owner_role_before.active).to be true
      
      # Employee leaves the company
      result = employee_user.leave_company(company)
      expect(result).to be true
      
      # Reload owner's role to check current state
      owner_role_after = CompanyUserRole.find_by(user: owner, company: company)
      
      # CRITICAL: Owner's role should remain unchanged
      expect(owner_role_after.is_primary).to eq(true)
      expect(owner_role_after.active).to eq(true)
      
      # Owner should still have the company as primary
      owner.reload
      expect(owner.primary_company).to eq(company)
      
      # Employee's role should be deactivated (need to use with_inactive to find it)
      employee_role_after = CompanyUserRole.unscoped.find_by(user: employee_user, company: company)
      expect(employee_role_after).not_to be_nil
      expect(employee_role_after.is_primary).to be false
      expect(employee_role_after.active).to be false
    end
    
    it 'only deactivates the leaving user\'s roles' do
      # Get all users in the company before
      all_roles_before = CompanyUserRole.where(company: company).pluck(:user_id, :active, :is_primary)
      
      # Employee leaves
      employee_user.leave_company(company)
      
      # Check each user's role (use unscoped to see inactive roles too)
      CompanyUserRole.unscoped.where(company: company).each do |role|
        if role.user_id == employee_user.id
          expect(role.active).to eq(false)
          expect(role.is_primary).to eq(false)
        else
          expect(role.active).to eq(true)
          # is_primary might be true or false depending on user's setup, but shouldn't change
        end
      end
    end
    
    it 'does not affect other users\' ability to authenticate' do
      # Simulate the scenario from the logs
      expect(owner.company_user_roles.where(company: company, active: true).exists?).to be true
      
      # Employee leaves
      employee_user.leave_company(company)
      
      # Owner should still be able to access the company
      owner.reload
      expect(owner.company_user_roles.where(company: company, active: true).exists?).to be true
      expect(owner.primary_company).to eq(company)
    end
  end
end