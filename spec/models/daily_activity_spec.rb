# ABOUTME: Test file for DailyActivity model
# ABOUTME: Validates force_close_work_activities class method and daily activity functionality
require 'rails_helper'

RSpec.describe DailyActivity, type: :model do
  let(:company) { create(:company, name: "Test Company", subdomain: "test") }
  let(:user1) { create(:user, email: "<EMAIL>") }
  let(:user2) { create(:user, email: "<EMAIL>") }
  let(:contract1) do
    contract = build(:contract, company: company, first_name: "Test1", last_name: "User1")
    allow(contract).to receive(:send_invitation).and_return(true)
    contract.save!
    contract
  end
  let(:contract2) do
    contract = build(:contract, company: company, first_name: "Test2", last_name: "User2")
    allow(contract).to receive(:send_invitation).and_return(true)
    contract.save!
    contract
  end
  let(:work) { create(:work, title: "Test Work", company: company, status: "in_progress") }

  describe 'validations' do
    it 'is valid with required attributes' do
      daily_activity = DailyActivity.new(
        work: work,
        user: user1,
        company: company,
        contract: contract1,
        start_time: 1.hour.ago,
        end_time: nil,
        activity_type: "work_at_location"
      )
      expect(daily_activity).to be_valid
    end

    it 'requires activity_type to be valid' do
      daily_activity = DailyActivity.new(
        work: work,
        user: user1,
        company: company,
        contract: contract1,
        start_time: 1.hour.ago,
        end_time: nil,
        activity_type: "invalid_type"
      )
      expect(daily_activity).not_to be_valid
      expect(daily_activity.errors[:activity_type]).to be_present
    end
  end

  describe 'associations' do
    it 'belongs to work' do
      daily_activity = DailyActivity.create!(
        work: work,
        user: user1,
        company: company,
        contract: contract1,
        start_time: 1.hour.ago,
        end_time: nil,
        activity_type: "work_at_location"
      )
      expect(daily_activity.work).to eq(work)
    end

    it 'belongs to user' do
      daily_activity = DailyActivity.create!(
        work: work,
        user: user1,
        company: company,
        contract: contract1,
        start_time: 1.hour.ago,
        end_time: nil,
        activity_type: "work_at_location"
      )
      expect(daily_activity.user).to eq(user1)
    end

    it 'belongs to contract' do
      daily_activity = DailyActivity.create!(
        work: work,
        user: user1,
        company: company,
        contract: contract1,
        start_time: 1.hour.ago,
        end_time: nil,
        activity_type: "work_at_location"
      )
      expect(daily_activity.contract).to eq(contract1)
    end
  end

  describe '.force_close_work_activities' do
    before do
      # Create multiple active activities for the work
      @active_activity1 = DailyActivity.create!(
        work: work,
        user: user1,
        company: company,
        contract: contract1,
        start_time: 2.hours.ago,
        end_time: nil,
        activity_type: "work_at_location"
      )
      
      @active_activity2 = DailyActivity.create!(
        work: work,
        user: user2,
        company: company,
        contract: contract2,
        start_time: 1.hour.ago,
        end_time: nil,
        activity_type: "work_at_location"
      )

      # Create a finished activity that should not be affected
      @finished_activity = DailyActivity.create!(
        work: work,
        user: user1,
        company: company,
        contract: contract1,
        start_time: 3.hours.ago,
        end_time: 2.hours.ago,
        activity_type: "work_at_location"
      )

      # Create an active activity for a different work that should not be affected
      other_work = create(:work, title: "Other Work", company: company, status: "in_progress")
      @other_work_activity = DailyActivity.create!(
        work: other_work,
        user: user1,
        company: company,
        contract: contract1,
        start_time: 1.hour.ago,
        end_time: nil,
        activity_type: "work_at_location"
      )
    end

    it 'closes all active activities for the specified work' do
      reason = "Force closed by manager"
      count = DailyActivity.force_close_work_activities(work.id, reason)

      expect(count).to eq(2) # Should close 2 active activities

      # Reload and check that activities are closed
      @active_activity1.reload
      @active_activity2.reload
      
      expect(@active_activity1.end_time).not_to be_nil
      expect(@active_activity2.end_time).not_to be_nil
      expect(@active_activity1.description).to include("Admin force closed: #{reason}")
      expect(@active_activity2.description).to include("Admin force closed: #{reason}")
    end

    it 'does not affect finished activities' do
      original_end_time = @finished_activity.end_time
      original_description = @finished_activity.description

      DailyActivity.force_close_work_activities(work.id, "Test reason")

      @finished_activity.reload
      expect(@finished_activity.end_time).to eq(original_end_time)
      expect(@finished_activity.description).to eq(original_description)
    end

    it 'does not affect activities from other works' do
      DailyActivity.force_close_work_activities(work.id, "Test reason")

      @other_work_activity.reload
      expect(@other_work_activity.end_time).to be_nil
    end

    it 'returns 0 when no active activities exist for the work' do
      # First close all activities
      DailyActivity.force_close_work_activities(work.id, "First close")
      
      # Try to close again
      count = DailyActivity.force_close_work_activities(work.id, "Second close")
      expect(count).to eq(0)
    end

    it 'sets the end_time to current time' do
      before_time = Time.current
      DailyActivity.force_close_work_activities(work.id, "Test reason")
      after_time = Time.current

      @active_activity1.reload
      @active_activity2.reload
      
      expect(@active_activity1.end_time).to be_between(before_time, after_time)
      expect(@active_activity2.end_time).to be_between(before_time, after_time)
    end

    it 'includes the reason in the description' do
      custom_reason = "Emergency closure due to system maintenance"
      DailyActivity.force_close_work_activities(work.id, custom_reason)

      @active_activity1.reload
      @active_activity2.reload
      
      expect(@active_activity1.description).to include("Admin force closed: #{custom_reason}")
      expect(@active_activity2.description).to include("Admin force closed: #{custom_reason}")
    end
  end

  describe 'duration calculation' do
    it 'calculates duration when end_time is present' do
      start_time = 2.hours.ago
      end_time = 1.hour.ago
      
      daily_activity = DailyActivity.create!(
        work: work,
        user: user1,
        company: company,
        contract: contract1,
        start_time: start_time,
        end_time: end_time,
        activity_type: "work_at_location"
      )
      
      expected_duration = (end_time - start_time).to_i
      expect(daily_activity.duration).to eq(expected_duration)
    end

    it 'does not calculate duration when end_time is nil' do
      daily_activity = DailyActivity.create!(
        work: work,
        user: user1,
        company: company,
        contract: contract1,
        start_time: 1.hour.ago,
        end_time: nil,
        activity_type: "work_at_location"
      )
      
      expect(daily_activity.duration).to be_nil
    end
  end
end