# ABOUTME: Tests for Company model with focus on free tier role restrictions and available_roles method
require 'rails_helper'

RSpec.describe Company, type: :model do
  let!(:owner_role) { Role.find_or_create_by!(name: 'owner') }
  let!(:employee_role) { Role.find_or_create_by!(name: 'employee') }
  let!(:admin_role) { Role.find_or_create_by!(name: 'admin') }
  let!(:supervisor_role) { Role.find_or_create_by!(name: 'supervisor') }
  
  let(:company) { create(:company) }
  let(:owner) { create(:user) }
  let(:employee) { create(:user) }

  describe '#available_roles' do
    context 'when company has premium plan' do
      let!(:premium_plan) { create(:plan, :premium) }
      let!(:subscription) { create(:subscription, company: company, plan: premium_plan) }

      it 'returns all roles' do
        expect(company.available_roles.pluck(:name)).to match_array(['owner', 'employee', 'admin', 'supervisor'])
      end
    end

    context 'when company has plus plan' do
      let!(:plus_plan) { create(:plan, :plus) }
      let!(:subscription) { create(:subscription, company: company, plan: plus_plan) }

      it 'returns owner, employee, admin, and supervisor roles' do
        expect(company.available_roles.pluck(:name)).to match_array(['owner', 'employee', 'admin', 'supervisor'])
      end
    end

    context 'when company has no plan (free tier)' do
      context 'with no existing owners' do
        it 'returns owner and employee roles' do
          expect(company.available_roles.pluck(:name)).to match_array(['owner', 'employee'])
        end
      end

      context 'with existing owner' do
        before do
          ActsAsTenant.with_tenant(company) do
            create(:company_user_role, user: owner, company: company, role: owner_role)
          end
        end

        it 'returns only employee role' do
          expect(company.available_roles.pluck(:name)).to match_array(['employee'])
        end
      end

      context 'with multiple existing owners' do
        let(:second_owner) { create(:user) }
        
        before do
          ActsAsTenant.with_tenant(company) do
            create(:company_user_role, user: owner, company: company, role: owner_role)
            create(:company_user_role, user: second_owner, company: company, role: owner_role)
          end
        end

        it 'returns only employee role' do
          expect(company.available_roles.pluck(:name)).to match_array(['employee'])
        end
      end
    end
  end
end