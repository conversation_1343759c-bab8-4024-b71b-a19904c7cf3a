# spec/models/jwt_revocation_strategy_spec.rb
require 'rails_helper'

RSpec.describe JwtRevocationStrategy do
  let(:strategy) { described_class.new }
  let(:user) { create(:user) }
  let(:jti) { SecureRandom.uuid }
  let(:future_exp) { 1.hour.from_now.to_i }
  let(:past_exp) { 1.hour.ago.to_i }
  
  let(:valid_payload) do
    {
      'jti' => jti,
      'exp' => future_exp,
      'user_id' => user.id
    }
  end
  
  let(:expired_payload) do
    {
      'jti' => jti,
      'exp' => past_exp,
      'user_id' => user.id
    }
  end
  
  before do
    # Clear any existing revoked tokens using connection pool
    Redis.current.with(&:flushdb)
  end
  
  describe '#revoke_jwt' do
    context 'with valid payload' do
      it 'stores the JTI in Redis with proper expiration' do
        strategy.revoke_jwt(valid_payload, user)
        
        redis_key = described_class.revocation_key(jti)
        Redis.current.with do |conn|
          expect(conn.exists?(redis_key)).to be true
          expect(conn.get(redis_key)).to eq user.id.to_s
          
          # Check TTL is set correctly (within 5 seconds tolerance)
          ttl = conn.ttl(redis_key)
          expected_ttl = future_exp - Time.current.to_i
          expect(ttl).to be_within(5).of(expected_ttl)
        end
      end
      
      it 'logs the revocation' do
        expect(Rails.logger).to receive(:info).with("Revoked JWT with jti: #{jti} for user: #{user.id}")
        strategy.revoke_jwt(valid_payload, user)
      end
    end
    
    context 'with expired payload' do
      it 'does not store expired tokens' do
        strategy.revoke_jwt(expired_payload, user)
        
        redis_key = described_class.revocation_key(jti)
        Redis.current.with do |conn|
          expect(conn.exists?(redis_key)).to be false
        end
      end
    end
    
    context 'with missing jti' do
      it 'returns without error' do
        payload = valid_payload.except('jti')
        expect { strategy.revoke_jwt(payload, user) }.not_to raise_error
      end
    end
    
    context 'with missing exp' do
      it 'returns without error' do
        payload = valid_payload.except('exp')
        expect { strategy.revoke_jwt(payload, user) }.not_to raise_error
      end
    end
    
    context 'when Redis fails' do
      before do
        allow_any_instance_of(Redis).to receive(:setex).and_raise(Redis::ConnectionError)
      end
      
      it 'logs the error and re-raises' do
        expect(Rails.logger).to receive(:error).with(/Failed to revoke JWT/)
        expect { strategy.revoke_jwt(valid_payload, user) }.to raise_error(Redis::ConnectionError)
      end
    end
  end
  
  describe '#jwt_revoked?' do
    context 'when token is revoked' do
      before do
        strategy.revoke_jwt(valid_payload, user)
      end
      
      it 'returns true' do
        expect(strategy.jwt_revoked?(valid_payload, user)).to be true
      end
    end
    
    context 'when token is not revoked' do
      it 'returns false' do
        expect(strategy.jwt_revoked?(valid_payload, user)).to be false
      end
    end
    
    context 'with missing jti' do
      it 'returns false' do
        payload = valid_payload.except('jti')
        expect(strategy.jwt_revoked?(payload, user)).to be false
      end
    end
    
    context 'when Redis fails' do
      before do
        allow_any_instance_of(Redis).to receive(:exists?).and_raise(Redis::ConnectionError)
      end
      
      it 'logs the error and returns true (fail closed)' do
        expect(Rails.logger).to receive(:error).with(/Failed to check JWT revocation status/)
        expect(strategy.jwt_revoked?(valid_payload, user)).to be true
      end
    end
  end
  
  describe '.revocation_key' do
    it 'generates consistent keys' do
      key1 = described_class.revocation_key(jti)
      key2 = described_class.revocation_key(jti)
      
      expect(key1).to eq key2
      # Note: The actual key structure is handled by RedisKeyBuilder
      expect(key1).to be_a(String)
      expect(key1).to include(jti)
    end
  end
  
  describe '#revoke_all_for_user' do
    it 'logs a warning that the method is not implemented' do
      expect(Rails.logger).to receive(:warn).with("revoke_all_for_user not implemented in basic strategy")
      strategy.revoke_all_for_user(user)
    end
  end
  
  describe 'JTI expiration in Redis' do
    it 'automatically removes expired JTIs from Redis' do
      # Create a token that expires in 2 seconds
      short_exp_payload = {
        'jti' => jti,
        'exp' => 2.seconds.from_now.to_i,
        'user_id' => user.id
      }
      
      strategy.revoke_jwt(short_exp_payload, user)
      redis_key = described_class.revocation_key(jti)
      
      # Verify it exists
      Redis.current.with do |conn|
        expect(conn.exists?(redis_key)).to be true
      end
      
      # Wait for it to expire
      sleep 3
      
      # Verify it's gone
      Redis.current.with do |conn|
        expect(conn.exists?(redis_key)).to be false
      end
    end
  end
  
  describe 'concurrent revocations' do
    it 'handles multiple revocations safely' do
      threads = []
      jtis = 10.times.map { SecureRandom.uuid }
      
      jtis.each do |token_jti|
        threads << Thread.new do
          payload = valid_payload.merge('jti' => token_jti)
          strategy.revoke_jwt(payload, user)
        end
      end
      
      threads.each(&:join)
      
      # Verify all tokens were revoked
      jtis.each do |token_jti|
        payload = valid_payload.merge('jti' => token_jti)
        expect(strategy.jwt_revoked?(payload, user)).to be true
      end
    end
  end
  
  describe '.revoke_family' do
    let(:family_id) { SecureRandom.uuid }
    let!(:session_id_1) { SecureRandom.uuid }
    let!(:session_id_2) { SecureRandom.uuid }
    
    before do
      # Create multiple JWT sessions for the user to simulate multi-device login
      JwtSessionService.create_session(user, {
        access_token: 'token1',
        refresh_token: 'refresh1',
        company_id: user.primary_company&.id,
        ip_address: '***********',
        user_agent: 'Browser 1'
      })
      
      JwtSessionService.create_session(user, {
        access_token: 'token2', 
        refresh_token: 'refresh2',
        company_id: user.primary_company&.id,
        ip_address: '***********',
        user_agent: 'Browser 2'
      })
    end
    
    context 'with valid family_id and user' do
      it 'destroys all user sessions' do
        # Verify sessions exist before revocation
        expect(JwtSessionService.get_user_sessions_details(user.id).length).to eq 2
        
        described_class.revoke_family(family_id, user)
        
        # Verify all sessions are destroyed
        expect(JwtSessionService.get_user_sessions_details(user.id)).to be_empty
      end
      
      it 'logs security warnings' do
        expect(Rails.logger).to receive(:error).with("[SECURITY] Revoking token family: #{family_id} for User: #{user.id}")
        expect(Rails.logger).to receive(:info).with("[SECURITY] All sessions destroyed for User: #{user.id} due to token family breach")
        # JwtSessionService also logs session destructions - allow all related logging
        allow(Rails.logger).to receive(:info).with(/Destroyed/)
        
        described_class.revoke_family(family_id, user)
      end
      
      it 'handles session destruction gracefully' do
        expect { described_class.revoke_family(family_id, user) }.not_to raise_error
      end
    end
    
    context 'with nil family_id' do
      it 'returns early without taking action' do
        expect(JwtSessionService).not_to receive(:destroy_all_user_sessions)
        expect(Rails.logger).not_to receive(:error)
        
        described_class.revoke_family(nil, user)
      end
    end
    
    context 'with blank family_id' do
      it 'returns early without taking action' do
        expect(JwtSessionService).not_to receive(:destroy_all_user_sessions)
        expect(Rails.logger).not_to receive(:error)
        
        described_class.revoke_family('', user)
      end
    end
    
    context 'with nil user' do
      it 'returns early without taking action' do
        expect(JwtSessionService).not_to receive(:destroy_all_user_sessions)
        expect(Rails.logger).not_to receive(:error)
        
        described_class.revoke_family(family_id, nil)
      end
    end
    
    context 'when JwtSessionService raises an error' do
      before do
        allow(JwtSessionService).to receive(:destroy_all_user_sessions).and_raise(Redis::ConnectionError, 'Redis unavailable')
      end
      
      it 'logs the error and re-raises' do
        expect(Rails.logger).to receive(:error).with("[SECURITY] Revoking token family: #{family_id} for User: #{user.id}")
        expect(Rails.logger).to receive(:error).with('Failed to revoke token family: Redis unavailable')
        
        expect { described_class.revoke_family(family_id, user) }.to raise_error(Redis::ConnectionError, 'Redis unavailable')
      end
    end
    
    context 'security breach scenario simulation' do
      it 'completely isolates the user after family revocation' do
        # Setup: User has active sessions
        expect(JwtSessionService.get_user_sessions_details(user.id).length).to eq 2
        
        # Simulate breach detection and family revocation
        described_class.revoke_family(family_id, user)
        
        # Verify complete isolation: no sessions remain
        user_sessions = JwtSessionService.get_user_sessions_details(user.id)
        expect(user_sessions).to be_empty
        
        # Verify that new session lookup fails (sessions are gone)
        expect(JwtSessionService.find_session(user.id, session_id_1)).to be_nil
        expect(JwtSessionService.find_session(user.id, session_id_2)).to be_nil
      end
    end
    
    context 'family revocation with concurrent access' do
      it 'handles concurrent revocation attempts safely' do
        threads = []
        results = []
        
        # Simulate multiple concurrent revocation attempts
        3.times do
          threads << Thread.new do
            begin
              described_class.revoke_family(family_id, user)
              results << :success
            rescue => e
              results << e
            end
          end
        end
        
        threads.each(&:join)
        
        # All should succeed (or at least not crash)
        expect(results.all? { |r| r == :success || r.is_a?(Exception) }).to be true
        # Sessions should be destroyed regardless of concurrency
        expect(JwtSessionService.get_user_sessions_details(user.id)).to be_empty
      end
    end
  end
end