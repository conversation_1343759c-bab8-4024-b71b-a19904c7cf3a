require 'rails_helper'

RSpec.describe ApplicationCable::Connection, type: :channel do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let!(:company_user_role) { create(:company_user_role, user: user, company: company, role: create(:role, name: 'admin')) }
  let(:jwt_payload) { user.jwt_payload.merge('company_id' => company.id) }
  let(:valid_token) { JwtService.encode(jwt_payload) }
  let(:expired_token) { JwtService.encode(jwt_payload, 1.second.ago) }
  
  before do
    # Ensure Redis is clean for tests
    Redis.current.with { |conn| conn.flushdb }
  end

  describe '#connect' do
    context 'with valid JWT token in query params' do
      it 'successfully authenticates and sets current_user and current_tenant' do
        connect "/cable?token=#{valid_token}"
        
        expect(connection.current_user).to eq(user)
        expect(connection.current_tenant).to eq(company)
        expect(ActsAsTenant.current_tenant).to eq(company)
      end
    end

    context 'with valid JWT token in Authorization header' do
      it 'successfully authenticates' do
        connect "/cable", headers: { 'Authorization' => "Bearer #{valid_token}" }
        
        expect(connection.current_user).to eq(user)
        expect(connection.current_tenant).to eq(company)
      end
    end

    context 'with valid JWT token in X-Auth-Token header' do
      it 'successfully authenticates' do
        connect "/cable", headers: { 'X-Auth-Token' => valid_token }
        
        expect(connection.current_user).to eq(user)
        expect(connection.current_tenant).to eq(company)
      end
    end

    context 'with expired JWT token' do
      it 'rejects the connection' do
        expect {
          connect "/cable?token=#{expired_token}"
        }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
      end
    end

    context 'with revoked JWT token' do
      it 'rejects the connection' do
        # Create a token and get its payload to revoke
        token_to_revoke = JwtService.encode(jwt_payload)
        decoded_payload = JwtService.decode(token_to_revoke)
        
        # Revoke the token using the actual payload with JTI
        JwtRevocationStrategy.new.revoke_jwt(decoded_payload, user)
        
        expect {
          connect "/cable?token=#{token_to_revoke}"
        }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
      end
    end

    context 'with invalid JWT token' do
      it 'rejects the connection' do
        expect {
          connect "/cable?token=invalid_token"
        }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
      end
    end

    context 'without JWT token' do
      it 'rejects the connection' do
        expect {
          connect "/cable"
        }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
      end
    end

    context 'with JWT token for non-existent user' do
      it 'rejects the connection' do
        invalid_payload = jwt_payload.merge('user_id' => 999999)
        invalid_token = JwtService.encode(invalid_payload)
        
        expect {
          connect "/cable?token=#{invalid_token}"
        }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
      end
    end

    context 'with JWT token containing invalid company_id' do
      it 'authenticates user but does not set tenant' do
        invalid_company_payload = jwt_payload.merge('company_id' => 999999)
        token_with_invalid_company = JwtService.encode(invalid_company_payload)
        
        connect "/cable?token=#{token_with_invalid_company}"
        
        expect(connection.current_user).to eq(user)
        expect(connection.current_tenant).to be_nil
        expect(ActsAsTenant.current_tenant).to be_nil
      end
    end

    context 'with JWT token for company user does not belong to' do
      let(:other_company) { create(:company) }
      
      it 'authenticates user but does not set tenant' do
        unauthorized_company_payload = jwt_payload.merge('company_id' => other_company.id)
        token_with_unauthorized_company = JwtService.encode(unauthorized_company_payload)
        
        connect "/cable?token=#{token_with_unauthorized_company}"
        
        expect(connection.current_user).to eq(user)
        expect(connection.current_tenant).to be_nil
        expect(ActsAsTenant.current_tenant).to be_nil
      end
    end
  end

  describe 'logging' do
    it 'adds appropriate tags for authenticated connections' do
      connect "/cable?token=#{valid_token}"
      
      # Check that connection was successful and user/tenant are set correctly
      expect(connection.current_user).to eq(user)
      expect(connection.current_tenant).to eq(company)
    end
  end
end