// ABOUTME: Tests for MobileWorkCard component used in mobile scheduling interface
// ABOUTME: Tests work display, selection states, and mobile-friendly interactions

import { mount, createLocalVue } from '@vue/test-utils'
import MobileWorkCard from '@/components/calendar/MobileWorkCard.vue'

const localVue = createLocalVue()

// Mock Lucide icons
jest.mock('lucide-vue-next', () => ({
  MapPin: { name: 'MapPin', template: '<div>MapPin</div>' },
  Users: { name: 'Users', template: '<div>Users</div>' },
  Clock: { name: 'Clock', template: '<div>Clock</div>' },
  CheckCircle: { name: 'CheckCircle', template: '<div>CheckCircle</div>' }
}))

describe('MobileWorkCard', () => {
  let wrapper
  let mockWork

  beforeEach(() => {
    mockWork = {
      id: 1,
      title: 'Test Work Title',
      description: 'This is a test work description that should be displayed properly on the mobile card interface.',
      priority: 'high',
      location: 'Main Office',
      assigned_users_count: 3,
      estimated_duration: 120
    }

    const $t = (key, fallback) => fallback || key

    wrapper = mount(MobileWorkCard, {
      localVue,
      propsData: {
        work: mockWork,
        isSelected: false
      },
      mocks: {
        $t
      }
    })
  })

  afterEach(() => {
    wrapper.destroy()
  })

  describe('Component Rendering', () => {
    it('renders the component correctly', () => {
      expect(wrapper.find('[data-vue-component="mobile-work-card"]').exists()).toBe(true)
    })

    it('displays work title', () => {
      const title = wrapper.find('.work-title')
      expect(title.text()).toBe('Test Work Title')
    })

    it('displays work description when present', () => {
      const description = wrapper.find('.work-description')
      expect(description.text()).toBe(mockWork.description)
    })

    it('does not display description section when description is null', async () => {
      await wrapper.setProps({
        work: { ...mockWork, description: null }
      })
      
      expect(wrapper.find('.work-description').exists()).toBe(false)
    })

    it('applies correct CSS classes based on selection state', async () => {
      expect(wrapper.classes()).not.toContain('selected')
      
      await wrapper.setProps({ isSelected: true })
      expect(wrapper.classes()).toContain('selected')
    })
  })

  describe('Priority Display', () => {
    it('displays priority badge when priority is set', () => {
      const priority = wrapper.find('.work-priority')
      expect(priority.exists()).toBe(true)
      expect(priority.classes()).toContain('priority-high')
    })

    it('does not display priority badge when priority is not set', async () => {
      await wrapper.setProps({
        work: { ...mockWork, priority: null }
      })
      
      expect(wrapper.find('.work-priority').exists()).toBe(false)
    })

    it('formats priority text correctly', () => {
      const priority = wrapper.find('.work-priority')
      expect(priority.text()).toBe('Vysoká') // Czech translation for 'high'
    })

    it('applies correct CSS classes for different priority levels', async () => {
      const priorities = ['low', 'medium', 'high', 'urgent']
      
      for (const priorityLevel of priorities) {
        await wrapper.setProps({
          work: { ...mockWork, priority: priorityLevel }
        })
        
        const priorityElement = wrapper.find('.work-priority')
        expect(priorityElement.classes()).toContain(`priority-${priorityLevel}`)
      }
    })
  })

  describe('Work Details Display', () => {
    it('displays location when present', () => {
      const location = wrapper.find('.work-location')
      expect(location.exists()).toBe(true)
      expect(location.text()).toContain('Main Office')
    })

    it('displays team size when present', () => {
      const team = wrapper.find('.work-team')
      expect(team.exists()).toBe(true)
      expect(team.text()).toContain('3 lidé') // Czech for 'people'
    })

    it('displays duration when present', () => {
      const duration = wrapper.find('.work-duration')
      expect(duration.exists()).toBe(true)
      expect(duration.text()).toContain('2h') // 120 minutes = 2 hours
    })

    it('hides location when not present', async () => {
      await wrapper.setProps({
        work: { ...mockWork, location: null }
      })
      
      expect(wrapper.find('.work-location').exists()).toBe(false)
    })

    it('hides team count when not present', async () => {
      await wrapper.setProps({
        work: { ...mockWork, assigned_users_count: null }
      })
      
      expect(wrapper.find('.work-team').exists()).toBe(false)
    })

    it('hides duration when not present', async () => {
      await wrapper.setProps({
        work: { ...mockWork, estimated_duration: null }
      })
      
      expect(wrapper.find('.work-duration').exists()).toBe(false)
    })
  })

  describe('Duration Formatting', () => {
    it('formats minutes correctly when less than 60', async () => {
      await wrapper.setProps({
        work: { ...mockWork, estimated_duration: 45 }
      })
      
      const duration = wrapper.find('.work-duration')
      expect(duration.text()).toContain('45min')
    })

    it('formats hours correctly when no remaining minutes', async () => {
      await wrapper.setProps({
        work: { ...mockWork, estimated_duration: 120 }
      })
      
      const duration = wrapper.find('.work-duration')
      expect(duration.text()).toContain('2h')
    })

    it('formats hours and minutes correctly', async () => {
      await wrapper.setProps({
        work: { ...mockWork, estimated_duration: 150 }
      })
      
      const duration = wrapper.find('.work-duration')
      expect(duration.text()).toContain('2h 30min')
    })
  })

  describe('Description Truncation', () => {
    it('truncates long descriptions', async () => {
      const longDescription = 'This is a very long description that should be truncated because it exceeds the maximum length allowed for display in the mobile work card interface.'
      
      await wrapper.setProps({
        work: { ...mockWork, description: longDescription }
      })
      
      const description = wrapper.find('.work-description')
      expect(description.text()).toContain('...')
      expect(description.text().length).toBeLessThan(longDescription.length)
    })

    it('does not truncate short descriptions', async () => {
      const shortDescription = 'Short description'
      
      await wrapper.setProps({
        work: { ...mockWork, description: shortDescription }
      })
      
      const description = wrapper.find('.work-description')
      expect(description.text()).toBe(shortDescription)
      expect(description.text()).not.toContain('...')
    })
  })

  describe('Selection Indicator', () => {
    it('shows selection indicator when selected', async () => {
      await wrapper.setProps({ isSelected: true })
      
      const indicator = wrapper.find('.selection-indicator')
      expect(indicator.exists()).toBe(true)
      expect(indicator.text()).toContain('Vybráno') // Czech for 'Selected'
    })

    it('hides selection indicator when not selected', () => {
      const indicator = wrapper.find('.selection-indicator')
      expect(indicator.exists()).toBe(false)
    })
  })

  describe('User Interactions', () => {
    it('emits click event when card is clicked', async () => {
      await wrapper.trigger('click')
      
      expect(wrapper.emitted('click')).toBeTruthy()
      expect(wrapper.emitted('click')).toHaveLength(1)
    })

    it('has correct cursor pointer styling for interaction', () => {
      const cardStyle = getComputedStyle(wrapper.element)
      expect(wrapper.element.style.cursor).toBe('pointer')
    })

    it('applies touch-friendly interaction styling', () => {
      expect(wrapper.attributes('style')).toContain('touch-action: manipulation')
    })
  })

  describe('Accessibility', () => {
    it('provides proper data attributes for testing', () => {
      expect(wrapper.find('[data-vue-component="mobile-work-card"]').exists()).toBe(true)
    })

    it('has proper semantic structure', () => {
      expect(wrapper.find('.work-header').exists()).toBe(true)
      expect(wrapper.find('.work-details').exists()).toBe(true)
      expect(wrapper.find('.work-title').element.tagName).toBe('H4')
    })
  })

  describe('Responsive Design', () => {
    it('applies mobile-optimized classes', () => {
      expect(wrapper.classes()).toContain('mobile-work-card')
    })

    it('includes proper spacing for touch interactions', () => {
      // Check that the component has adequate padding for touch targets
      const cardElement = wrapper.find('.mobile-work-card')
      expect(cardElement.exists()).toBe(true)
    })
  })

  describe('Priority Method Testing', () => {
    it('formats all priority levels correctly', () => {
      const component = wrapper.vm
      
      expect(component.formatPriority('low')).toBe('Nízká')
      expect(component.formatPriority('medium')).toBe('Střední')
      expect(component.formatPriority('high')).toBe('Vysoká')
      expect(component.formatPriority('urgent')).toBe('Urgentní')
    })

    it('returns original value for unknown priority', () => {
      const component = wrapper.vm
      expect(component.formatPriority('unknown')).toBe('unknown')
    })
  })

  describe('Edge Cases', () => {
    it('handles work with minimal data', async () => {
      const minimalWork = {
        id: 2,
        title: 'Minimal Work'
      }
      
      await wrapper.setProps({
        work: minimalWork,
        isSelected: false
      })
      
      expect(wrapper.find('.work-title').text()).toBe('Minimal Work')
      expect(wrapper.find('.work-location').exists()).toBe(false)
      expect(wrapper.find('.work-team').exists()).toBe(false)
      expect(wrapper.find('.work-duration').exists()).toBe(false)
      expect(wrapper.find('.work-priority').exists()).toBe(false)
    })

    it('handles empty or undefined description gracefully', async () => {
      await wrapper.setProps({
        work: { ...mockWork, description: '' }
      })
      
      expect(wrapper.find('.work-description').exists()).toBe(false)
    })

    it('handles zero duration correctly', async () => {
      await wrapper.setProps({
        work: { ...mockWork, estimated_duration: 0 }
      })
      
      expect(wrapper.find('.work-duration').exists()).toBe(false)
    })
  })
})