// ABOUTME: Comprehensive tests for MobileUnscheduledPanel component
// ABOUTME: Tests mobile scheduling interface, work selection, and auto-scheduling functionality

import { mount, createLocalVue } from '@vue/test-utils'
import Vuex from 'vuex'
import MobileUnscheduledPanel from '@/components/calendar/MobileUnscheduledPanel.vue'
import MobileWorkCard from '@/components/calendar/MobileWorkCard.vue'

const localVue = createLocalVue()
localVue.use(Vuex)

// Mock Lucide icons
jest.mock('lucide-vue-next', () => ({
  LandPlot: { name: 'LandPlot', template: '<div>LandPlot</div>' },
  ChevronUp: { name: 'ChevronUp', template: '<div>ChevronUp</div>' },
  ChevronDown: { name: 'ChevronDown', template: '<div>ChevronDown</div>' }
}))

describe('MobileUnscheduledPanel', () => {
  let wrapper
  let store
  let actions
  let getters
  let mockWorks

  beforeEach(() => {
    // Mock unscheduled works data
    mockWorks = [
      {
        id: 1,
        title: 'Test Work 1',
        description: 'First test work',
        priority: 'high',
        location: 'Office',
        assigned_users_count: 3,
        estimated_duration: 120
      },
      {
        id: 2,
        title: 'Test Work 2',
        description: 'Second test work',
        priority: 'medium',
        location: 'Remote',
        assigned_users_count: 2,
        estimated_duration: 60
      }
    ]

    // Mock Vuex store
    getters = {
      unscheduledWorks: () => mockWorks
    }

    actions = {
      updateWorkDate: jest.fn().mockResolvedValue(),
      findFirstFreeSlot: jest.fn().mockResolvedValue(new Date('2024-01-15'))
    }

    store = new Vuex.Store({
      modules: {
        calendarStore: {
          namespaced: true,
          getters,
          actions
        }
      }
    })

    // Mock $t function for i18n
    const $t = (key, fallback) => fallback || key

    wrapper = mount(MobileUnscheduledPanel, {
      localVue,
      store,
      mocks: {
        $t
      },
      stubs: {
        MobileWorkCard: true
      }
    })
  })

  afterEach(() => {
    wrapper.destroy()
  })

  describe('Component Rendering', () => {
    it('renders the component correctly', () => {
      expect(wrapper.find('[data-vue-component="mobile-unscheduled-panel"]').exists()).toBe(true)
    })

    it('displays the correct header title and work count', () => {
      const headerTitle = wrapper.find('.header-title')
      expect(headerTitle.text()).toContain('Unscheduled Works')
      expect(headerTitle.text()).toContain(`(${mockWorks.length})`)
    })

    it('shows chevron down icon when panel is collapsed', () => {
      expect(wrapper.find('ChevronDown').exists()).toBe(true)
      expect(wrapper.find('ChevronUp').exists()).toBe(false)
    })

    it('shows chevron up icon when panel is expanded', async () => {
      await wrapper.setData({ isExpanded: true })
      expect(wrapper.find('ChevronUp').exists()).toBe(true)
      expect(wrapper.find('ChevronDown').exists()).toBe(false)
    })

    it('renders panel content when expanded', async () => {
      await wrapper.setData({ isExpanded: true })
      expect(wrapper.find('.panel-content').exists()).toBe(true)
    })

    it('hides panel content when collapsed', () => {
      expect(wrapper.find('.panel-content').exists()).toBe(false)
    })
  })

  describe('Panel Expansion/Collapse', () => {
    it('toggles panel expansion when header is clicked', async () => {
      const header = wrapper.find('[data-action="toggle-panel"]')
      
      expect(wrapper.vm.isExpanded).toBe(false)
      
      await header.trigger('click')
      expect(wrapper.vm.isExpanded).toBe(true)
      
      await header.trigger('click')
      expect(wrapper.vm.isExpanded).toBe(false)
    })

    it('auto-expands when work is selected', async () => {
      await wrapper.setData({ 
        selectedWork: mockWorks[0],
        isExpanded: false 
      })
      
      const header = wrapper.find('[data-action="toggle-panel"]')
      await header.trigger('click')
      
      expect(wrapper.vm.isExpanded).toBe(true)
    })

    it('applies correct CSS classes based on expansion state', async () => {
      const header = wrapper.find('.panel-header')
      
      expect(header.classes()).not.toContain('has-selection')
      
      await wrapper.setData({ selectedWork: mockWorks[0] })
      expect(header.classes()).toContain('has-selection')
    })
  })

  describe('Work Selection', () => {
    beforeEach(async () => {
      await wrapper.setData({ isExpanded: true })
    })

    it('displays all unscheduled works when no selection', () => {
      const workCards = wrapper.findAllComponents({ name: 'MobileWorkCard' })
      expect(workCards).toHaveLength(mockWorks.length)
    })

    it('updates header when work is selected', async () => {
      await wrapper.vm.selectWork(mockWorks[0])
      
      const selectedTitle = wrapper.find('.header-title.selected')
      expect(selectedTitle.text()).toContain('Selected: Test Work 1')
    })

    it('shows selection hint when work is selected', async () => {
      await wrapper.vm.selectWork(mockWorks[0])
      
      const selectionHint = wrapper.find('.selection-hint')
      expect(selectionHint.text()).toContain('Tap a day to schedule')
    })

    it('emits work-selected event when work is selected', async () => {
      await wrapper.vm.selectWork(mockWorks[0])
      
      expect(wrapper.emitted('work-selected')).toBeTruthy()
      expect(wrapper.emitted('work-selected')[0][0]).toEqual(mockWorks[0])
    })

    it('clears selection and emits work-deselected event', async () => {
      await wrapper.setData({ selectedWork: mockWorks[0] })
      
      await wrapper.vm.clearSelection()
      
      expect(wrapper.vm.selectedWork).toBe(null)
      expect(wrapper.emitted('work-deselected')).toBeTruthy()
    })

    it('shows only selected work card in selection state', async () => {
      await wrapper.setData({ selectedWork: mockWorks[0] })
      
      const worksList = wrapper.find('.works-list')
      const selectionState = wrapper.find('.selection-state')
      
      expect(worksList.exists()).toBe(false)
      expect(selectionState.exists()).toBe(true)
    })
  })

  describe('Auto-Scheduling', () => {
    beforeEach(async () => {
      await wrapper.setData({ 
        isExpanded: true,
        selectedWork: mockWorks[0]
      })
    })

    it('shows auto-schedule button when work is selected', () => {
      const autoScheduleBtn = wrapper.find('[data-action="auto-schedule"]')
      expect(autoScheduleBtn.exists()).toBe(true)
    })

    it('disables auto-schedule button during scheduling', async () => {
      await wrapper.setData({ isScheduling: true })
      
      const autoScheduleBtn = wrapper.find('[data-action="auto-schedule"]')
      expect(autoScheduleBtn.attributes('disabled')).toBeTruthy()
    })

    it('changes button text during scheduling', async () => {
      const autoScheduleBtn = wrapper.find('[data-action="auto-schedule"]')
      expect(autoScheduleBtn.text()).toContain('Auto Schedule')
      
      await wrapper.setData({ isScheduling: true })
      expect(autoScheduleBtn.text()).toContain('Scheduling...')
    })

    it('calls findFirstFreeSlot and schedules work successfully', async () => {
      const optimalDate = new Date('2024-01-15')
      actions.findFirstFreeSlot.mockResolvedValue(optimalDate)
      
      await wrapper.vm.autoSchedule()
      
      expect(actions.findFirstFreeSlot).toHaveBeenCalledWith(
        expect.any(Object),
        mockWorks[0]
      )
      
      expect(actions.updateWorkDate).toHaveBeenCalledWith(
        expect.any(Object),
        {
          workId: mockWorks[0].id,
          newDate: '2024-01-15'
        }
      )
    })

    it('emits work-scheduled event on successful scheduling', async () => {
      const optimalDate = new Date('2024-01-15')
      actions.findFirstFreeSlot.mockResolvedValue(optimalDate)
      
      await wrapper.vm.autoSchedule()
      
      expect(wrapper.emitted('work-scheduled')).toBeTruthy()
      expect(wrapper.emitted('work-scheduled')[0][0]).toEqual({
        work: mockWorks[0],
        date: optimalDate
      })
    })

    it('emits no-free-slots event when no slots available', async () => {
      actions.findFirstFreeSlot.mockResolvedValue(null)
      
      await wrapper.vm.autoSchedule()
      
      expect(wrapper.emitted('no-free-slots')).toBeTruthy()
      expect(wrapper.emitted('no-free-slots')[0][0]).toEqual(mockWorks[0])
    })

    it('emits scheduling-error event on failure', async () => {
      const error = new Error('Scheduling failed')
      actions.findFirstFreeSlot.mockRejectedValue(error)
      
      await wrapper.vm.autoSchedule()
      
      expect(wrapper.emitted('scheduling-error')).toBeTruthy()
      expect(wrapper.emitted('scheduling-error')[0][0]).toEqual(error)
    })

    it('resets scheduling state after completion', async () => {
      const optimalDate = new Date('2024-01-15')
      actions.findFirstFreeSlot.mockResolvedValue(optimalDate)
      
      await wrapper.vm.autoSchedule()
      
      expect(wrapper.vm.isScheduling).toBe(false)
      expect(wrapper.vm.selectedWork).toBe(null)
    })
  })

  describe('Empty State', () => {
    beforeEach(() => {
      // Create store with no unscheduled works
      getters.unscheduledWorks = () => []
      
      store = new Vuex.Store({
        modules: {
          calendarStore: {
            namespaced: true,
            getters,
            actions
          }
        }
      })

      wrapper = mount(MobileUnscheduledPanel, {
        localVue,
        store,
        mocks: {
          $t: (key, fallback) => fallback || key
        },
        stubs: {
          MobileWorkCard: true
        }
      })
    })

    it('shows empty state when no unscheduled works', async () => {
      await wrapper.setData({ isExpanded: true })
      
      const emptyState = wrapper.find('.empty-state')
      expect(emptyState.exists()).toBe(true)
      expect(emptyState.text()).toContain('No unscheduled works')
    })

    it('shows zero count in header', () => {
      const headerTitle = wrapper.find('.header-title')
      expect(headerTitle.text()).toContain('(0)')
    })
  })

  describe('User Interactions', () => {
    beforeEach(async () => {
      await wrapper.setData({ isExpanded: true })
    })

    it('handles cancel button click in selection state', async () => {
      await wrapper.setData({ selectedWork: mockWorks[0] })
      
      const cancelBtn = wrapper.find('[data-action="cancel-selection"]')
      await cancelBtn.trigger('click')
      
      expect(wrapper.vm.selectedWork).toBe(null)
    })

    it('provides correct data attributes for testing', () => {
      expect(wrapper.find('[data-vue-component="mobile-unscheduled-panel"]').exists()).toBe(true)
      expect(wrapper.find('[data-action="toggle-panel"]').exists()).toBe(true)
    })

    it('handles work card selection through events', async () => {
      const selectWorkSpy = jest.spyOn(wrapper.vm, 'selectWork')
      
      // Simulate work card click event
      await wrapper.vm.selectWork(mockWorks[0])
      
      expect(selectWorkSpy).toHaveBeenCalledWith(mockWorks[0])
      expect(wrapper.vm.selectedWork).toEqual(mockWorks[0])
      expect(wrapper.vm.isExpanded).toBe(true)
    })
  })

  describe('Component Lifecycle', () => {
    it('adds click event listener on mount', () => {
      const addEventListenerSpy = jest.spyOn(document, 'addEventListener')
      
      mount(MobileUnscheduledPanel, {
        localVue,
        store,
        mocks: {
          $t: (key, fallback) => fallback || key
        }
      })
      
      expect(addEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function))
    })

    it('removes click event listener on unmount', () => {
      const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener')
      
      const testWrapper = mount(MobileUnscheduledPanel, {
        localVue,
        store,
        mocks: {
          $t: (key, fallback) => fallback || key
        }
      })
      
      testWrapper.destroy()
      
      expect(removeEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function))
    })
  })

  describe('Integration with MobileWorkCard', () => {
    it('passes correct props to MobileWorkCard components', async () => {
      await wrapper.setData({ isExpanded: true })
      
      // Use actual MobileWorkCard component instead of stub
      wrapper = mount(MobileUnscheduledPanel, {
        localVue,
        store,
        mocks: {
          $t: (key, fallback) => fallback || key
        },
        components: {
          MobileWorkCard
        }
      })
      
      await wrapper.setData({ isExpanded: true })
      
      const workCards = wrapper.findAllComponents(MobileWorkCard)
      expect(workCards).toHaveLength(mockWorks.length)
      
      const firstCard = workCards.at(0)
      expect(firstCard.props('work')).toEqual(mockWorks[0])
      expect(firstCard.props('isSelected')).toBe(false)
    })
  })
})