require 'rails_helper'

RSpec.describe "API Dual Authentication System Tests", type: :request do
  let(:user) { create(:user, email: '<EMAIL>') }
  let(:company) { create(:company, name: 'Test Company') }
  let(:role) { create(:role, name: 'employee') }
  let!(:company_user_role) { create(:company_user_role, user: user, company: company, role: role, is_primary: true) }
  let!(:contract) { create(:contract, :skip_invitation, user: user, company: company, first_name: 'Test', last_name: 'User') }
  
  # JWT setup
  let(:valid_payload) { { user_id: user.id, email: user.email, company_id: company.id } }
  let(:valid_jwt) { JwtService.encode(valid_payload) }
  let(:expired_jwt) { JwtService.encode(valid_payload, 1.second.ago) }
  let(:invalid_jwt) { "invalid.jwt.token" }
  let(:jwt_headers) { { 'Authorization' => "Bearer #{valid_jwt}" } }
  let(:expired_jwt_headers) { { 'Authorization' => "Bearer #{expired_jwt}" } }
  let(:invalid_jwt_headers) { { 'Authorization' => "Bearer #{invalid_jwt}" } }
  
  # Another user and company for cross-tenant testing
  let(:other_user) { create(:user, email: '<EMAIL>') }
  let(:other_company) { create(:company, name: 'Other Company') }
  let(:other_role) { create(:role, name: 'employee') }
  let!(:other_company_user_role) { create(:company_user_role, user: other_user, company: other_company, role: other_role, is_primary: true) }
  let!(:other_contract) { create(:contract, :skip_invitation, user: other_user, company: other_company) }
  
  # JWT for unauthorized company access
  let(:unauthorized_company_payload) { { user_id: user.id, email: user.email, company_id: other_company.id } }
  let(:unauthorized_company_jwt) { JwtService.encode(unauthorized_company_payload) }
  let(:unauthorized_company_headers) { { 'Authorization' => "Bearer #{unauthorized_company_jwt}" } }
  
  before do
    # Mock User#jwt_payload to include company_id
    allow_any_instance_of(User).to receive(:jwt_payload).and_return(valid_payload)
    
    # Clear any Redis data
    Redis.current.flushdb
  end
  
  describe "DailyLogsController endpoints" do
    let(:daily_log) { create(:daily_log, contract: contract, company: company, start_time: Time.current.beginning_of_day + 9.hours, user: user) }
    let(:daily_activity) { create(:daily_activity, daily_log: daily_log, description: "Working on task") }
    
    describe "GET /api/v1/daily_logs" do
      context "with JWT authentication" do
        it "returns daily logs for the authenticated user" do
          daily_log # Create the daily log
          get "/api/v1/daily_logs", headers: jwt_headers
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json).to be_an(Array)
          expect(json.first['id']).to eq(daily_log.id)
        end
        
        it "tracks JWT request in metrics" do
          daily_log # Create the daily log
          expect(AuthHealthCheck).to receive(:increment_jwt_request_count)
          get "/api/v1/daily_logs", headers: jwt_headers
        end
      end
      
      context "with session authentication" do
        before { sign_in user }
        
        it "returns daily logs for the session user" do
          daily_log # Create the daily log
          get "/api/v1/daily_logs"
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json).to be_an(Array)
          expect(json.first['id']).to eq(daily_log.id)
        end
        
        it "tracks session request in metrics" do
          daily_log # Create the daily log
          expect(AuthHealthCheck).to receive(:increment_session_request_count)
          get "/api/v1/daily_logs"
        end
      end
      
      context "with expired JWT" do
        it "returns unauthorized" do
          daily_log # Create the daily log
          get "/api/v1/daily_logs", headers: expired_jwt_headers
          
          expect(response).to have_http_status(:unauthorized)
          json = JSON.parse(response.body)
          expect(json['error']).to eq('Authentication required')
        end
      end
      
      context "with invalid JWT" do
        it "returns unauthorized" do
          daily_log # Create the daily log
          get "/api/v1/daily_logs", headers: invalid_jwt_headers
          
          expect(response).to have_http_status(:unauthorized)
          json = JSON.parse(response.body)
          expect(json['error']).to eq('Authentication required')
        end
      end
      
      context "with no authentication" do
        it "returns unauthorized" do
          daily_log # Create the daily log
          get "/api/v1/daily_logs"
          
          expect(response).to have_http_status(:unauthorized)
          json = JSON.parse(response.body)
          expect(json['error']).to eq('Authentication required')
        end
      end
    end
    
    describe "GET /api/v1/daily_logs/fetch" do
      let(:date_param) { Date.current.to_s }
      
      context "with JWT authentication" do
        it "returns daily logs for the specified date" do
          daily_log # Create the daily log
          get "/api/v1/daily_logs/fetch", params: { date: date_param }, headers: jwt_headers
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json['daily_logs']).to be_an(Array)
          expect(json['daily_logs'].first['id']).to eq(daily_log.id)
        end
      end
      
      context "with session authentication" do
        before { sign_in user }
        
        it "returns daily logs for the specified date" do
          daily_log # Create the daily log
          get "/api/v1/daily_logs/fetch", params: { date: date_param }
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json['daily_logs']).to be_an(Array)
        end
      end
    end
    
    describe "POST /api/v1/daily_logs" do
      context "with JWT authentication" do
        it "creates a new daily log" do
          # Don't create any existing logs for today
          initial_count = DailyLog.count
          
          # Ensure user is reloaded with all associations
          user.reload
          
          post "/api/v1/daily_logs", headers: jwt_headers
          
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json['daily_log']).not_to be_nil
          expect(DailyLog.count).to eq(initial_count + 1)
        end
        
        it "sets the correct user and contract from JWT context" do
          # Don't create any existing logs to ensure we create a new one
          post "/api/v1/daily_logs", headers: jwt_headers
          
          json = JSON.parse(response.body)
          created_log = DailyLog.find(json['daily_log']['id'])
          expect(created_log.user_id).to eq(user.id)
          expect(created_log.contract_id).to eq(contract.id)
        end
      end
      
      context "with session authentication" do
        before { sign_in user }
        
        it "creates a new daily log" do
          # Don't create any existing logs for today
          expect {
            post "/api/v1/daily_logs"
          }.to change(DailyLog, :count).by(1)
          
          expect(response).to have_http_status(:success)
        end
      end
    end
    
    describe "GET /api/v1/daily_logs/team_summary" do
      let!(:supervisor_role) { create(:role, name: 'supervisor') }
      let!(:supervisor_company_role) { create(:company_user_role, user: user, company: company, role: supervisor_role, is_primary: false) }
      
      context "with JWT authentication and supervisor role" do
        it "returns team summary" do
          get "/api/v1/daily_logs/team_summary", headers: jwt_headers
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json['team_logs']).to be_an(Array)
        end
      end
      
      context "with JWT authentication but no supervisor role" do
        before { supervisor_company_role.destroy }
        
        it "returns forbidden" do
          get "/api/v1/daily_logs/team_summary", headers: jwt_headers
          
          expect(response).to have_http_status(:forbidden)
        end
      end
    end
  end
  
  describe "EventsController endpoints" do
    let!(:event) do
      # Ensure tenant is set for event creation
      ActsAsTenant.with_tenant(company) do
        create(:event, contract: contract, user: user, title: "Test Event", event_type: "vacation", start_time: 1.week.from_now, end_time: 2.weeks.from_now)
      end
    end
    
    describe "GET /api/v1/events" do
      context "with JWT authentication" do
        it "returns events for the authenticated user's company" do
          get "/api/v1/events", headers: jwt_headers
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json).to be_an(Array)
          expect(json.first['id']).to eq(event.id)
          expect(json.first['name']).to eq("#{contract.first_name} #{contract.last_name}")
        end
      end
      
      context "with session authentication" do
        before { sign_in user }
        
        it "returns events for the session user's company" do
          get "/api/v1/events"
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json).to be_an(Array)
          expect(json.first['id']).to eq(event.id)
        end
      end
      
      context "with status parameter" do
        let!(:approved_event) do
          ActsAsTenant.with_tenant(company) do
            create(:event, contract: contract, user: user, status: 'approved', start_time: 2.weeks.from_now, end_time: 3.weeks.from_now)
          end
        end
        
        it "filters events by status with JWT auth" do
          get "/api/v1/events", params: { status: 'approved' }, headers: jwt_headers
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json.length).to eq(1)
          expect(json.first['id']).to eq(approved_event.id)
        end
      end
    end
    
    describe "GET /api/v1/events/fetch" do
      let(:date_param) { Date.current.to_s }
      let!(:work) { create(:work, company: company, scheduled_start_date: Date.current) }
      let!(:meeting) { create(:meeting, company: company, created_by: user, confirmed_date: Date.current) }
      
      context "with JWT authentication" do
        it "returns events, works, meetings and holidays for the month" do
          get "/api/v1/events/fetch", params: { date: date_param }, headers: jwt_headers
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json).to have_key('events')
          expect(json).to have_key('works')
          expect(json).to have_key('meetings')
          expect(json).to have_key('holidays')
          expect(json['events']).to be_an(Array)
          expect(json['works']).to be_an(Array)
          expect(json['meetings']).to be_an(Array)
        end
      end
      
      context "with session authentication" do
        before { sign_in user }
        
        it "returns all data for the month" do
          get "/api/v1/events/fetch", params: { date: date_param }
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json).to have_key('events')
          expect(json).to have_key('works')
          expect(json).to have_key('meetings')
          expect(json).to have_key('holidays')
        end
      end
    end
    
    describe "POST /api/v1/events" do
      let(:event_params) do
        {
          event: {
            event_type: 'vacation',
            title: 'Summer Holiday',
            start_time: 1.week.from_now.to_s,
            end_time: 2.weeks.from_now.to_s,
            description: 'Annual vacation'
          }
        }
      end
      
      context "with JWT authentication" do
        it "creates a new event" do
          expect {
            post "/api/v1/events", params: event_params, headers: jwt_headers
          }.to change(Event, :count).by(1)
          
          expect(response).to have_http_status(:created)
          json = JSON.parse(response.body)
          expect(json['success']).to be true
          expect(json['event']['title']).to eq('Summer Holiday')
        end
        
        it "associates the event with the correct contract and user" do
          post "/api/v1/events", params: event_params, headers: jwt_headers
          
          json = JSON.parse(response.body)
          created_event = Event.find(json['event']['id'])
          expect(created_event.contract_id).to eq(contract.id)
          expect(created_event.user_id).to eq(user.id)
        end
      end
      
      context "with session authentication" do
        before { sign_in user }
        
        it "creates a new event" do
          expect {
            post "/api/v1/events", params: event_params
          }.to change(Event, :count).by(1)
          
          expect(response).to have_http_status(:created)
        end
      end
    end
    
    describe "DELETE /api/v1/events/:id" do
      context "with JWT authentication" do
        it "deletes the event" do
          expect {
            delete "/api/v1/events/#{event.id}", headers: jwt_headers
          }.to change(Event, :count).by(-1)
          
          expect(response).to have_http_status(:success)
          json = JSON.parse(response.body)
          expect(json['success']).to be true
        end
      end
      
      context "with session authentication" do
        before { sign_in user }
        
        it "deletes the event" do
          expect {
            delete "/api/v1/events/#{event.id}"
          }.to change(Event, :count).by(-1)
          
          expect(response).to have_http_status(:success)
        end
      end
      
      context "trying to delete another user's event" do
        let!(:other_event) { create(:event, contract: other_contract, user: other_user) }
        
        it "returns not found with JWT auth" do
          delete "/api/v1/events/#{other_event.id}", headers: jwt_headers
          
          expect(response).to have_http_status(:not_found)
        end
      end
    end
  end
  
  describe "JWT Revocation" do
    let(:revoked_jwt) do
      token = JwtService.encode(valid_payload.merge(jti: 'revoked-jti'))
      decoded = JwtService.decode(token)
      JwtRevocationStrategy.new.revoke_jwt(decoded, user)
      token
    end
    let(:revoked_jwt_headers) { { 'Authorization' => "Bearer #{revoked_jwt}" } }
    
    it "rejects revoked JWT tokens on DailyLogsController" do
      get "/api/v1/daily_logs", headers: revoked_jwt_headers
      
      expect(response).to have_http_status(:unauthorized)
      json = JSON.parse(response.body)
      expect(json['error']).to eq('Authentication required')
    end
    
    it "rejects revoked JWT tokens on EventsController" do
      get "/api/v1/events", headers: revoked_jwt_headers
      
      expect(response).to have_http_status(:unauthorized)
      json = JSON.parse(response.body)
      expect(json['error']).to eq('Authentication required')
    end
  end
  
  describe "Tenant Context and Isolation" do
    let!(:other_company_event) do
      ActsAsTenant.with_tenant(other_company) do
        create(:event, contract: other_contract, user: other_user, start_time: 1.week.from_now, end_time: 2.weeks.from_now)
      end
    end
    let!(:other_company_daily_log) do
      ActsAsTenant.with_tenant(other_company) do
        create(:daily_log, contract: other_contract, company: other_company, user: other_user, start_time: Time.current.beginning_of_day + 9.hours)
      end
    end
    
    # Also need to create our own company's data for comparison
    let!(:daily_log) do
      ActsAsTenant.with_tenant(company) do
        create(:daily_log, contract: contract, company: company, start_time: Time.current.beginning_of_day + 9.hours, user: user)
      end
    end
    
    context "with JWT containing valid company_id" do
      it "sets tenant context correctly for DailyLogsController" do
        get "/api/v1/daily_logs", headers: jwt_headers
        
        expect(response).to have_http_status(:success)
        json = JSON.parse(response.body)
        
        # Should only see logs from the JWT's company
        log_ids = json.map { |log| log['id'] }
        expect(log_ids).to include(daily_log.id)
        expect(log_ids).not_to include(other_company_daily_log.id)
      end
      
      it "sets tenant context correctly for EventsController" do
        # Create an event in this test's context
        tenant_event = ActsAsTenant.with_tenant(company) do
          create(:event, contract: contract, user: user, title: "Tenant Test Event", start_time: 1.week.from_now, end_time: 2.weeks.from_now)
        end
        
        get "/api/v1/events", headers: jwt_headers
        
        expect(response).to have_http_status(:success)
        json = JSON.parse(response.body)
        
        # Should only see events from the JWT's company
        event_ids = json.map { |e| e['id'] }
        expect(event_ids).to include(tenant_event.id)
        expect(event_ids).not_to include(other_company_event.id)
      end
    end
    
    context "with JWT containing unauthorized company_id" do
      it "handles unauthorized company access gracefully" do
        get "/api/v1/daily_logs", headers: unauthorized_company_headers
        
        # User is authenticated but doesn't have access to the company in the JWT
        # This results in 401 because the JWT authentication fails due to invalid tenant context
        expect(response).to have_http_status(:unauthorized)
        json = JSON.parse(response.body)
        expect(json['error']).to eq('Authentication required')
      end
    end
    
    context "with session authentication preserving tenant context" do
      before do
        sign_in user
        # Manually set the tenant in the session by setting ActsAsTenant
        # In real app, this would be set by some controller action
        allow_any_instance_of(Api::V1::ApiController).to receive(:session).and_return({ tenant_id: company.id })
      end
      
      it "maintains tenant context across requests" do
        # First request
        get "/api/v1/daily_logs"
        expect(response).to have_http_status(:success)
        first_json = JSON.parse(response.body)
        
        # Second request
        get "/api/v1/events"
        expect(response).to have_http_status(:success)
        second_json = JSON.parse(response.body)
        
        # Both requests should succeed (even if no data exists)
        expect(first_json).to be_an(Array)
        expect(second_json).to be_an(Array)
      end
    end
  end
  
  describe "Authentication Method Precedence" do
    before { sign_in user }
    
    it "prefers JWT over session when both are present" do
      # Create a JWT for other_user but keep session for user
      other_user_payload = { user_id: other_user.id, email: other_user.email, company_id: other_company.id }
      other_user_jwt = JwtService.encode(other_user_payload)
      other_user_headers = { 'Authorization' => "Bearer #{other_user_jwt}" }
      
      # Mock jwt_payload for other_user
      allow_any_instance_of(User).to receive(:jwt_payload) do |instance|
        if instance.id == other_user.id
          other_user_payload
        else
          valid_payload
        end
      end
      
      # Make request with JWT for other_user while session has user
      get "/api/v1/events", headers: other_user_headers
      
      
      expect(response).to have_http_status(:success)
      json = JSON.parse(response.body)
      
      # Should see other_user's events, not session user's events
      event_ids = json.map { |e| e['id'] }
      expect(event_ids).to include(other_company_event.id)
      expect(event_ids).not_to include(event.id)
    end
  end
  
  describe "Metrics Tracking" do
    it "accurately tracks JWT vs session usage" do
      # Get baseline metrics
      health_check = AuthHealthCheck.new
      initial_jwt_count = health_check.jwt_request_count
      initial_session_count = health_check.session_request_count
      
      # Make 3 JWT requests
      3.times do
        get "/api/v1/daily_logs", headers: jwt_headers
        expect(response).to have_http_status(:success)
      end
      
      # Make 2 session requests
      sign_in user
      2.times do
        get "/api/v1/events"
        expect(response).to have_http_status(:success)
      end
      
      # Check metrics (adjusted for initial counts)
      health_check = AuthHealthCheck.new
      expect(health_check.jwt_request_count - initial_jwt_count).to eq(3)
      expect(health_check.session_request_count - initial_session_count).to eq(2)
    end
  end
  
  describe "Error Scenarios" do
    context "malformed authorization header" do
      it "returns unauthorized with missing Bearer prefix" do
        get "/api/v1/daily_logs", headers: { 'Authorization' => valid_jwt }
        
        expect(response).to have_http_status(:unauthorized)
      end
      
      it "returns unauthorized with malformed JWT" do
        get "/api/v1/daily_logs", headers: { 'Authorization' => 'Bearer malformed.jwt' }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end
    
    context "JWT with missing required claims" do
      it "handles JWT without user_id" do
        bad_payload = { email: user.email, company_id: company.id }
        bad_jwt = JwtService.encode(bad_payload)
        
        get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{bad_jwt}" }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end
    
    context "non-existent user in JWT" do
      it "returns unauthorized" do
        bad_payload = { user_id: 999999, email: '<EMAIL>', company_id: company.id }
        bad_jwt = JwtService.encode(bad_payload)
        
        get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{bad_jwt}" }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end