# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Session Authentication Critical Paths', type: :system, js: true do
  let(:user) { create(:user, email: '<EMAIL>', password: 'password123') }
  let(:company) { create(:company, name: 'Test Company') }
  let(:other_company) { create(:company, name: 'Other Company') }
  let(:role) { create(:role, name: 'employee') }
  
  before do
    # Setup user with companies
    create(:company_user_role, user: user, company: company, role: role, is_primary: true)
    create(:company_user_role, user: user, company: other_company, role: role)
  end

  describe 'Login Flow' do
    it 'successfully logs in with valid credentials' do
      visit new_user_session_path
      
      fill_in 'Email', with: user.email
      fill_in 'Password', with: 'password123'
      click_button 'Log in'
      
      expect(page).to have_current_path(root_path)
      expect(page).to have_content('Signed in successfully')
    end

    it 'fails login with invalid credentials' do
      visit new_user_session_path
      
      fill_in 'Email', with: user.email
      fill_in 'Password', with: 'wrongpassword'
      click_button 'Log in'
      
      expect(page).to have_content('Invalid Email or password')
      expect(page).to have_current_path(new_user_session_path)
    end

    it 'redirects to requested page after login' do
      visit companies_path
      
      expect(page).to have_current_path(new_user_session_path)
      
      fill_in 'Email', with: user.email
      fill_in 'Password', with: 'password123'
      click_button 'Log in'
      
      expect(page).to have_current_path(companies_path)
    end

    it 'remembers user with remember me option' do
      visit new_user_session_path
      
      fill_in 'Email', with: user.email
      fill_in 'Password', with: 'password123'
      check 'Remember me'
      click_button 'Log in'
      
      # Check that remember token is set
      expect(page.driver.browser.manage.cookie_named('remember_user_token')).not_to be_nil
    end
  end

  describe 'Logout Flow' do
    before do
      login_as(user, scope: :user)
    end

    it 'successfully logs out user' do
      visit root_path
      
      within('nav') do
        click_link 'Logout'
      end
      
      expect(page).to have_content('Signed out successfully')
      expect(page).to have_current_path(new_user_session_path)
    end

    it 'clears session data on logout' do
      visit root_path
      expect(page).to have_content(user.email)
      
      within('nav') do
        click_link 'Logout'
      end
      
      # Try to access protected page
      visit companies_path
      expect(page).to have_current_path(new_user_session_path)
    end
  end

  describe 'Session Persistence' do
    before do
      login_as(user, scope: :user)
    end

    it 'maintains session across page navigations' do
      visit root_path
      expect(page).to have_content(user.email)
      
      visit companies_path
      expect(page).to have_content(user.email)
      
      visit contracts_path
      expect(page).to have_content(user.email)
    end

    it 'maintains session after page refresh' do
      visit root_path
      expect(page).to have_content(user.email)
      
      page.driver.browser.navigate.refresh
      
      expect(page).to have_content(user.email)
    end

    it 'maintains tenant context in session' do
      visit companies_path
      
      # Switch to specific company
      within("#company_#{company.id}") do
        click_link 'Select'
      end
      
      # Verify tenant is set
      visit daily_logs_path
      expect(page).to have_content(company.name)
      
      # Navigate to another page
      visit events_path
      expect(page).to have_content(company.name)
    end
  end

  describe 'Tenant Switching' do
    before do
      login_as(user, scope: :user)
    end

    it 'switches between companies successfully' do
      visit companies_path
      
      # Select first company
      within("#company_#{company.id}") do
        click_link 'Select'
      end
      
      expect(page).to have_content("Switched to #{company.name}")
      
      # Data should be scoped to first company
      visit daily_logs_path
      expect(page).to have_content(company.name)
      
      # Switch to second company
      visit companies_path
      within("#company_#{other_company.id}") do
        click_link 'Select'
      end
      
      expect(page).to have_content("Switched to #{other_company.name}")
      
      # Data should now be scoped to second company
      visit daily_logs_path
      expect(page).to have_content(other_company.name)
    end

    it 'persists tenant selection across sessions' do
      visit companies_path
      
      within("#company_#{company.id}") do
        click_link 'Select'
      end
      
      # Logout
      within('nav') do
        click_link 'Logout'
      end
      
      # Login again
      visit new_user_session_path
      fill_in 'Email', with: user.email
      fill_in 'Password', with: 'password123'
      click_button 'Log in'
      
      # Should still be in the same company context
      visit daily_logs_path
      expect(page).to have_content(company.name)
    end
  end

  describe 'Protected Routes Access' do
    it 'redirects unauthenticated users to login' do
      visit companies_path
      expect(page).to have_current_path(new_user_session_path)
      
      visit daily_logs_path
      expect(page).to have_current_path(new_user_session_path)
      
      visit contracts_path
      expect(page).to have_current_path(new_user_session_path)
    end

    it 'allows authenticated users to access protected routes' do
      login_as(user, scope: :user)
      
      visit companies_path
      expect(page).to have_current_path(companies_path)
      
      visit contracts_path
      expect(page).to have_current_path(contracts_path)
    end
  end

  describe 'Session Security' do
    before do
      login_as(user, scope: :user)
    end

    it 'invalidates session on password change' do
      visit edit_user_registration_path
      
      fill_in 'Email', with: '<EMAIL>'
      fill_in 'Password', with: 'newpassword123'
      fill_in 'Password confirmation', with: 'newpassword123'
      fill_in 'Current password', with: 'password123'
      click_button 'Update'
      
      # Should require re-authentication
      visit companies_path
      expect(page).to have_current_path(new_user_session_path)
    end

    it 'prevents session fixation attacks' do
      # Get initial session ID
      visit root_path
      initial_session = page.driver.browser.manage.cookie_named('_attendifyapp_session')
      
      # Logout and login again
      within('nav') do
        click_link 'Logout'
      end
      
      visit new_user_session_path
      fill_in 'Email', with: user.email
      fill_in 'Password', with: 'password123'
      click_button 'Log in'
      
      # Session ID should be different
      new_session = page.driver.browser.manage.cookie_named('_attendifyapp_session')
      expect(new_session[:value]).not_to eq(initial_session[:value])
    end
  end

  describe 'Concurrent Sessions' do
    it 'allows multiple browser sessions for same user' do
      # First browser session
      visit new_user_session_path
      fill_in 'Email', with: user.email
      fill_in 'Password', with: 'password123'
      click_button 'Log in'
      expect(page).to have_content(user.email)
      
      # Simulate second browser session
      in_browser(:second) do
        visit new_user_session_path
        fill_in 'Email', with: user.email
        fill_in 'Password', with: 'password123'
        click_button 'Log in'
        expect(page).to have_content(user.email)
      end
      
      # First session should still be valid
      visit companies_path
      expect(page).to have_content(user.email)
    end
  end

  describe 'CSRF Protection' do
    before do
      login_as(user, scope: :user)
    end

    it 'includes CSRF token in forms' do
      visit new_contract_path
      
      csrf_token = find('input[name="authenticity_token"]', visible: false).value
      expect(csrf_token).not_to be_empty
    end

    it 'rejects requests without valid CSRF token' do
      # This would typically be tested at request spec level
      # System specs automatically handle CSRF tokens
      visit new_contract_path
      
      # Fill form and submit - CSRF should be handled automatically
      fill_in 'Title', with: 'Test Contract'
      click_button 'Create Contract'
      
      # Should not see CSRF error
      expect(page).not_to have_content('ActionController::InvalidAuthenticityToken')
    end
  end
end