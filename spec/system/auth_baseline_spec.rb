require 'rails_helper'

RSpec.describe 'Authentication Baseline Tests', type: :system do
  # These tests capture the current authentication behavior
  # Run these before and after JWT implementation to ensure compatibility
  
  before do
    driven_by(:rack_test)
  end

  describe 'Session-based authentication flow' do
    let(:user) { create(:user, email: '<EMAIL>', password: 'password123') }
    let(:company) { create(:company) }
    let!(:role) { create(:role, name: 'employee') }
    let!(:company_user_role) { create(:company_user_role, user: user, company: company, role: role) }

    context 'successful login' do
      it 'logs in via login page and redirects to dashboard' do
        visit new_user_session_path(locale: :en)
        
        fill_in 'Email', with: user.email
        fill_in 'Password', with: 'password123'
        click_button 'Log in'
        
        expect(page).to have_current_path(root_path)
        expect(page).to have_content('Signed in successfully')
      end

      it 'maintains session across page visits' do
        sign_in user
        
        visit root_path
        expect(page).not_to have_content('Sign in')
        
        visit companies_path
        expect(page).not_to have_content('Sign in')
      end

      it 'allows logout' do
        sign_in user
        visit root_path
        
        click_link 'Sign out'
        
        expect(page).to have_current_path(new_user_session_path)
        expect(page).to have_content('Sign in')
      end
    end

    context 'failed login' do
      it 'shows error for invalid credentials' do
        visit new_user_session_path(locale: :en)
        
        fill_in 'Email', with: user.email
        fill_in 'Password', with: 'wrongpassword'
        click_button 'Log in'
        
        expect(page).to have_content('Invalid Email or password')
        expect(page).to have_current_path(new_user_session_path)
      end

      it 'shows error for non-existent user' do
        visit new_user_session_path(locale: :en)
        
        fill_in 'Email', with: '<EMAIL>'
        fill_in 'Password', with: 'password123'
        click_button 'Log in'
        
        expect(page).to have_content('Invalid Email or password')
      end
    end

    context 'protected routes' do
      it 'redirects to login when not authenticated' do
        visit companies_path
        
        expect(page).to have_current_path(new_user_session_path)
        expect(page).to have_content('You need to sign in')
      end

      it 'allows access when authenticated' do
        sign_in user
        
        visit companies_path
        
        expect(page).to have_current_path(companies_path)
        expect(page).not_to have_content('You need to sign in')
      end
    end

    context 'remember me functionality' do
      it 'remembers user when checkbox is checked' do
        visit new_user_session_path(locale: :en)
        
        fill_in 'Email', with: user.email
        fill_in 'Password', with: 'password123'
        check 'Remember me'
        click_button 'Log in'
        
        expect(page).to have_current_path(root_path)
        
        # Simulate browser restart by clearing session but keeping cookies
        Capybara.reset_sessions!
        
        visit root_path
        # Should still be logged in due to remember token
        expect(page).not_to have_content('Sign in')
      end
    end

    context 'multi-tenancy' do
      let(:company2) { create(:company) }
      let!(:role2) { create(:role, name: 'admin', company: company2) }
      let!(:company_user_role2) { create(:company_user_role, user: user, company: company2, role: role2) }

      it 'sets current tenant on login' do
        sign_in user
        
        visit root_path
        
        # Should have a current company set
        expect(Company.current).to eq(company)
      end

      it 'allows switching between companies' do
        sign_in user
        
        visit root_path
        expect(Company.current).to eq(company)
        
        # Switch to second company
        visit company_path(company2)
        expect(Company.current).to eq(company2)
      end
    end

    context 'API authentication' do
      it 'accepts session authentication for API endpoints' do
        sign_in user
        
        # Make API request with session cookie
        page.driver.header('Accept', 'application/json')
        visit api_v1_user_path
        
        json = JSON.parse(page.body)
        expect(json['email']).to eq(user.email)
      end

      it 'rejects unauthenticated API requests' do
        page.driver.header('Accept', 'application/json')
        visit api_v1_user_path
        
        expect(page.status_code).to eq(401)
      end
    end

    context 'CSRF protection' do
      it 'includes CSRF token in forms' do
        sign_in user
        visit new_contract_path
        
        expect(page).to have_css("input[name='authenticity_token']", visible: false)
      end
    end
  end

  describe 'Devise features' do
    context 'password reset' do
      let(:user) { create(:user) }

      it 'sends password reset instructions' do
        visit new_user_password_path(locale: :en)
        
        fill_in 'Email', with: user.email
        
        expect {
          click_button 'Send me reset password instructions'
        }.to change { ActionMailer::Base.deliveries.count }.by(1)
        
        expect(page).to have_content('You will receive an email with instructions')
      end
    end

    context 'account confirmation' do
      let(:user) { create(:user, confirmed_at: nil) }

      it 'requires email confirmation when enabled' do
        # This depends on Devise confirmable being enabled
        # Adjust based on your configuration
      end
    end
  end

  describe 'Performance benchmarks' do
    let(:user) { create(:user) }

    it 'completes login within acceptable time' do
      start_time = Time.current
      
      visit new_user_session_path(locale: :en)
      fill_in 'Email', with: user.email
      fill_in 'Password', with: 'password123'
      click_button 'Log in'
      
      duration = Time.current - start_time
      
      expect(duration).to be < 2.seconds
    end
  end
end