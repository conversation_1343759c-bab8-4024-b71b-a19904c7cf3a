# ABOUTME: Simple test to ensure admin controller show_company query works
# ABOUTME: Tests that the database column fix prevents PG::UndefinedColumn errors
require 'rails_helper'

RSpec.describe AdminController, type: :controller do
  describe '#show_company' do
    let(:company) { create(:company) }

    it 'does not raise PG::UndefinedColumn when ordering contracts by first_name and last_name' do
      # Create a contract with first_name and last_name in contracts table
      create(:contract, company: company, first_name: '<PERSON>', last_name: '<PERSON><PERSON>')
      
      # This should not raise a database error
      expect {
        company.contracts.includes(:user).order('contracts.first_name, contracts.last_name').to_a
      }.not_to raise_error

      # The old broken query should fail
      expect {
        company.contracts.includes(:user).order('users.first_name, users.last_name').to_a
      }.to raise_error(ActiveRecord::StatementInvalid, /column users\.first_name does not exist/)
    end
  end
end