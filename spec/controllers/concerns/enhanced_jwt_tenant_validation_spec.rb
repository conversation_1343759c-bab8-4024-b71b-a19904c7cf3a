# spec/controllers/concerns/enhanced_jwt_tenant_validation_spec.rb
# Test enhanced JWT tenant validation implemented in Chunk 29

require 'rails_helper'

RSpec.describe 'Enhanced JWT Tenant Validation (Chunk 29)', type: :controller do
  controller(ApplicationController) do
    include JwtAuthenticatable

    def test_action
      render json: { success: true }
    end
  end

  let(:user) { create(:user) }
  let(:company1) { create(:company) }
  let(:company2) { create(:company) }
  let(:company_user_role1) { create(:company_user_role, user: user, company: company1, active: true) }
  let(:company_user_role2) { create(:company_user_role, user: user, company: company2, active: true) }

  before do
    routes.draw { get 'test_action' => 'anonymous#test_action' }
    
    # Ensure user has active roles in both companies
    company_user_role1
    company_user_role2
  end

  describe 'Enhanced JWT tenant validation' do
    context 'with valid JWT and correct tenant' do
      let(:jwt_payload) { { user_id: user.id, company_id: company1.id, exp: 1.hour.from_now.to_i } }
      let(:jwt_token) { JwtService.encode(jwt_payload) }

      before do
        request.headers['Authorization'] = "Bearer #{jwt_token}"
      end

      it 'allows access when JWT company matches user access' do
        get :test_action
        expect(response).to have_http_status(:success)
        expect(ActsAsTenant.current_tenant).to eq(company1)
      end

      it 'logs successful tenant context setting' do
        expect(AuthHealthCheck).to receive(:log_security_event).with(
          'tenant_context_set',
          hash_including(
            severity: 'low',
            user_id: user.id,
            company_id: company1.id
          )
        )
        
        get :test_action
      end
    end

    context 'with JWT containing invalid company_id' do
      let(:invalid_company_id) { 99999 }
      let(:jwt_payload) { { user_id: user.id, company_id: invalid_company_id, exp: 1.hour.from_now.to_i } }
      let(:jwt_token) { JwtService.encode(jwt_payload) }

      before do
        request.headers['Authorization'] = "Bearer #{jwt_token}"
      end

      it 'denies access for non-existent company' do
        get :test_action
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)['error']).to include('Invalid tenant context')
      end

      it 'logs security event for invalid company' do
        expect(AuthHealthCheck).to receive(:log_security_event).with(
          'invalid_company_in_jwt',
          hash_including(
            severity: 'high',
            user_id: user.id,
            company_id: invalid_company_id,
            reason: 'company_not_found'
          )
        )
        
        get :test_action
      end
    end

    context 'with JWT containing company user has no access to' do
      let(:unauthorized_company) { create(:company) }
      let(:jwt_payload) { { user_id: user.id, company_id: unauthorized_company.id, exp: 1.hour.from_now.to_i } }
      let(:jwt_token) { JwtService.encode(jwt_payload) }

      before do
        request.headers['Authorization'] = "Bearer #{jwt_token}"
      end

      it 'denies access for unauthorized company' do
        get :test_action
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)['error']).to include('Invalid tenant context')
      end

      it 'logs security event for unauthorized company access' do
        expect(AuthHealthCheck).to receive(:log_security_event).with(
          'unauthorized_company_access',
          hash_including(
            severity: 'high',
            user_id: user.id,
            company_id: unauthorized_company.id,
            reason: 'no_active_role'
          )
        )
        
        get :test_action
      end
    end

    context 'with JWT containing company where user has inactive role' do
      let(:jwt_payload) { { user_id: user.id, company_id: company1.id, exp: 1.hour.from_now.to_i } }
      let(:jwt_token) { JwtService.encode(jwt_payload) }

      before do
        # Make the user's role inactive
        company_user_role1.update!(active: false)
        request.headers['Authorization'] = "Bearer #{jwt_token}"
      end

      it 'denies access for inactive role' do
        get :test_action
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)['error']).to include('Invalid tenant context')
      end

      it 'logs security event for inactive role' do
        expect(AuthHealthCheck).to receive(:log_security_event).with(
          'unauthorized_company_access',
          hash_including(
            severity: 'high',
            user_id: user.id,
            company_id: company1.id,
            reason: 'no_active_role'
          )
        )
        
        get :test_action
      end
    end
  end

  describe 'Security logging integration' do
    let(:jwt_payload) { { user_id: user.id, company_id: company1.id, exp: 1.hour.from_now.to_i } }
    let(:jwt_token) { JwtService.encode(jwt_token) }

    before do
      # Mock the AuthHealthCheck to verify security events are logged
      allow(AuthHealthCheck).to receive(:log_security_event)
      request.headers['Authorization'] = "Bearer #{jwt_token}"
    end

    it 'logs authentication events to AuthHealthCheck' do
      expect(AuthHealthCheck).to receive(:log_auth_event)
        .with('jwt_authentication', success: true)
      
      get :test_action
    end
  end
end