# spec/services/jwt_session_validation_spec.rb
require 'rails_helper'

RSpec.describe JwtSessionService, 'Session Validation' do
  let(:user) { create(:user) }
  let(:jwt_payload) do
    {
      access_token: 'test.access.token',
      refresh_token: 'test.refresh.token',
      company_id: 1,
      ip_address: '*************',
      user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
  end
  
  before do
    Redis.current.flushdb
  end
  
  describe 'context validation' do
    let(:session_id) { described_class.create_session(user, jwt_payload) }
    
    context 'with matching context' do
      it 'allows access with same IP and User-Agent' do
        context = {
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        session = described_class.find_session(user.id, session_id, validate_context: context)
        expect(session).to be_present
        expect(session[:access_token]).to eq('test.access.token')
      end
      
      it 'allows access with compatible User-Agent (minor version difference)' do
        context = {
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.125 Safari/537.36'
        }
        
        session = described_class.find_session(user.id, session_id, validate_context: context)
        expect(session).to be_present
      end
    end
    
    context 'with mismatched context' do
      it 'denies access with different IP address' do
        context = {
          ip_address: '********',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        session = described_class.find_session(user.id, session_id, validate_context: context)
        expect(session).to be_nil
        
        # Verify session was destroyed
        session_without_validation = described_class.find_session(user.id, session_id)
        expect(session_without_validation).to be_nil
      end
      
      it 'denies access with different browser' do
        context = {
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        session = described_class.find_session(user.id, session_id, validate_context: context)
        expect(session).to be_nil
      end
      
      it 'denies access with completely different User-Agent' do
        context = {
          ip_address: '*************',
          user_agent: 'PostmanRuntime/7.28.0'
        }
        
        session = described_class.find_session(user.id, session_id, validate_context: context)
        expect(session).to be_nil
      end
    end
    
    context 'without validation context' do
      it 'allows access without validation (backward compatibility)' do
        session = described_class.find_session(user.id, session_id)
        expect(session).to be_present
        expect(session[:access_token]).to eq('test.access.token')
      end
    end
  end
  
  describe 'browser signature extraction' do
    it 'correctly identifies Chrome browsers' do
      service = described_class
      chrome_ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      signature = service.send(:extract_browser_signature, chrome_ua)
      expect(signature).to eq('chrome-9')
    end
    
    it 'correctly identifies Firefox browsers' do
      service = described_class
      firefox_ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
      signature = service.send(:extract_browser_signature, firefox_ua)
      expect(signature).to eq('firefox-8')
    end
    
    it 'handles unknown User-Agents' do
      service = described_class
      unknown_ua = 'CustomBot/1.0'
      signature = service.send(:extract_browser_signature, unknown_ua)
      expect(signature).to eq('CustomBot/1.0')
    end
  end
end