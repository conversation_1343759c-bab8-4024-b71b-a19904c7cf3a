require 'rails_helper'

RSpec.describe AuthHealthCheck do
  let(:service) { described_class.new }
  let(:metrics_file) { Rails.root.join('tmp', 'auth_metrics.json') }
  
  before do
    # Clean up any existing metrics file
    FileUtils.rm_f(metrics_file)
  end
  
  after do
    # Clean up after tests
    FileUtils.rm_f(metrics_file)
  end
  
  describe '.check_health' do
    it 'returns health status' do
      result = described_class.check_health
      
      expect(result).to include(
        :timestamp,
        :healthy,
        :checks,
        :alerts
      )
      
      expect(result[:checks]).to include(
        :login_success_rate,
        :api_error_rate,
        :avg_response_time_ms,
        :session_validity
      )
    end
    
    context 'when metrics indicate problems' do
      before do
        # Log some failed login attempts
        10.times do
          described_class.log_auth_event('login', success: false, error: 'test')
        end
      end
      
      it 'sets healthy to false' do
        result = described_class.check_health
        expect(result[:healthy]).to be false
      end
      
      it 'includes alerts' do
        result = described_class.check_health
        expect(result[:alerts]).not_to be_empty
      end
    end
  end
  
  describe '.capture_baseline' do
    it 'captures current metrics as baseline' do
      # Log some events first
      5.times do
        described_class.log_auth_event('login', success: true, duration_ms: 100)
      end
      
      3.times do
        described_class.log_auth_event('api_request', success: true, duration_ms: 50)
      end
      
      baseline = described_class.capture_baseline
      
      expect(baseline).to include(
        :captured_at,
        :period,
        :metrics
      )
      
      expect(baseline[:metrics]).to include(
        :login_success_rate,
        :api_error_rate,
        :avg_response_time_ms,
        :active_sessions,
        :total_users
      )
      
      expect(baseline[:metrics][:login_success_rate]).to eq(100.0)
      expect(baseline[:metrics][:api_error_rate]).to eq(0.0)
    end
  end
  
  describe '.log_auth_event' do
    it 'logs successful login event' do
      described_class.log_auth_event('login', success: true, duration_ms: 150)
      
      metrics = JSON.parse(File.read(metrics_file))
      expect(metrics['events']).to include(
        hash_including(
          'type' => 'login',
          'success' => true,
          'duration_ms' => 150
        )
      )
    end
    
    it 'logs failed login event with error' do
      described_class.log_auth_event('login', success: false, error: 'Invalid password')
      
      metrics = JSON.parse(File.read(metrics_file))
      expect(metrics['events']).to include(
        hash_including(
          'type' => 'login',
          'success' => false,
          'error' => 'Invalid password'
        )
      )
    end
    
    it 'logs API request event' do
      described_class.log_auth_event('api_request', success: true, duration_ms: 75)
      
      metrics = JSON.parse(File.read(metrics_file))
      expect(metrics['events']).to include(
        hash_including(
          'type' => 'api_request',
          'success' => true,
          'duration_ms' => 75
        )
      )
    end
    
    it 'removes events older than 7 days' do
      # Log an old event
      old_event = {
        'type' => 'login',
        'timestamp' => 8.days.ago.iso8601,
        'success' => true
      }
      
      File.write(metrics_file, { 'events' => [old_event] }.to_json)
      
      # Log a new event
      described_class.log_auth_event('login', success: true)
      
      metrics = JSON.parse(File.read(metrics_file))
      
      # Old event should be removed
      expect(metrics['events'].size).to eq(1)
      expect(metrics['events'].first['timestamp']).not_to eq(old_event['timestamp'])
    end
  end
  
  describe '.generate_report' do
    before do
      # Create baseline
      5.times do
        described_class.log_auth_event('login', success: true, duration_ms: 100)
      end
      described_class.capture_baseline
      
      # Log some new events
      4.times do
        described_class.log_auth_event('login', success: true, duration_ms: 120)
      end
      described_class.log_auth_event('login', success: false, duration_ms: 150)
    end
    
    it 'generates comprehensive report' do
      report = described_class.generate_report
      
      expect(report).to include(
        :generated_at,
        :baseline,
        :current_metrics,
        :comparison
      )
    end
    
    it 'calculates metric differences' do
      report = described_class.generate_report
      
      expect(report[:comparison]).to include(
        :login_success_rate_diff,
        :api_error_rate_diff,
        :response_time_diff
      )
      
      # Login success rate should have decreased (100% -> 80%)
      expect(report[:comparison][:login_success_rate_diff]).to be < 0
    end
  end
  
  describe 'threshold alerts' do
    it 'alerts when login success rate drops below threshold' do
      # Log mostly failed logins
      20.times do
        described_class.log_auth_event('login', success: false)
      end
      described_class.log_auth_event('login', success: true)
      
      result = described_class.check_health
      
      expect(result[:healthy]).to be false
      expect(result[:alerts]).to include(/Login success rate.*below threshold/)
    end
    
    it 'alerts when API error rate exceeds threshold' do
      # Log mostly failed API calls
      10.times do
        described_class.log_auth_event('api_request', success: false)
      end
      
      result = described_class.check_health
      
      expect(result[:healthy]).to be false
      expect(result[:alerts]).to include(/API error rate.*exceeds threshold/)
    end
    
    it 'alerts when response time exceeds threshold' do
      # Log slow requests
      5.times do
        described_class.log_auth_event('api_request', success: true, duration_ms: 2000)
      end
      
      result = described_class.check_health
      
      expect(result[:healthy]).to be false
      expect(result[:alerts]).to include(/Average response time.*exceeds threshold/)
    end
  end
end