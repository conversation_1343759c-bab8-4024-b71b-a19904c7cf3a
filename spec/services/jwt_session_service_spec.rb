# spec/services/jwt_session_service_spec.rb
require 'rails_helper'

RSpec.describe JwtSessionService do
  let(:user) { create(:user) }
  let(:jwt_payload) do
    {
      access_token: 'test.access.token',
      refresh_token: 'test.refresh.token',
      company_id: 1,
      ip_address: '127.0.0.1',
      user_agent: 'RSpec Test'
    }
  end
  
  before do
    # Clear any existing sessions in Redis
    Redis.current.with(&:flushdb)
  end
  
  describe '.create_session' do
    it 'creates a new session in Redis' do
      session_id = described_class.create_session(user, jwt_payload)
      
      expect(session_id).to be_present
      expect(session_id).to match(/^[a-f0-9\-]{36}$/) # UUID format
      
      # Verify session is stored in Redis using proper namespaced key
      key = RedisKeyBuilder.jwt_session_key(user.id, session_id)
      Redis.current.with do |conn|
        expect(conn.exists?(key)).to be true
      end
    end
    
    it 'stores all session data correctly' do
      session_id = described_class.create_session(user, jwt_payload)
      session = described_class.find_session(user.id, session_id)
      
      expect(session[:user_id]).to eq(user.id)
      expect(session[:access_token]).to eq('test.access.token')
      expect(session[:refresh_token]).to eq('test.refresh.token')
      expect(session[:company_id]).to eq(1)
      expect(session[:ip_address]).to eq('127.0.0.1')
      expect(session[:user_agent]).to eq('RSpec Test')
      expect(session[:created_at]).to be_present
      expect(session[:last_activity]).to be_present
    end
    
    it 'sets TTL on the session' do
      session_id = described_class.create_session(user, jwt_payload)
      key = RedisKeyBuilder.jwt_session_key(user.id, session_id)
      
      Redis.current.with do |conn|
        ttl = conn.ttl(key)
        expect(ttl).to be > 0
        expect(ttl).to be <= 90.days.to_i  # Updated to match forever login duration
      end
    end
    
    it 'adds session to user sessions list' do
      session_id = described_class.create_session(user, jwt_payload)
      user_sessions_key = RedisKeyBuilder.jwt_user_sessions_key(user.id)
      
      Redis.current.with do |conn|
        sessions = conn.smembers(user_sessions_key)
        expect(sessions).to include(session_id)
      end
    end
  end
  
  describe '.find_session' do
    let(:session_id) { described_class.create_session(user, jwt_payload) }
    
    it 'returns session data when found' do
      session = described_class.find_session(user.id, session_id)
      
      expect(session).to be_present
      expect(session[:access_token]).to eq('test.access.token')
    end
    
    it 'returns nil when session not found' do
      session = described_class.find_session(user.id, 'non-existent-id')
      expect(session).to be_nil
    end
    
    it 'returns nil when user_id is nil' do
      session = described_class.find_session(nil, session_id)
      expect(session).to be_nil
    end
    
    it 'returns nil when session_id is nil' do
      session = described_class.find_session(user.id, nil)
      expect(session).to be_nil
    end
    
    it 'updates last activity timestamp' do
      original_key = RedisKeyBuilder.jwt_session_key(user.id, session_id)
      
      original_ttl = Redis.current.with { |conn| conn.ttl(original_key) }
      
      sleep 2 # Ensure time difference
      described_class.find_session(user.id, session_id)
      
      new_ttl = Redis.current.with { |conn| conn.ttl(original_key) }
      expect(new_ttl).to be >= original_ttl
    end
  end
  
  describe '.update_session_token' do
    let(:session_id) { described_class.create_session(user, jwt_payload) }
    
    it 'updates the access token' do
      new_token = 'new.access.token'
      result = described_class.update_session_token(user.id, session_id, new_token)
      
      expect(result).to be true
      
      session = described_class.find_session(user.id, session_id)
      expect(session[:access_token]).to eq(new_token)
    end
    
    it 'preserves other session data' do
      new_token = 'new.access.token'
      described_class.update_session_token(user.id, session_id, new_token)
      
      session = described_class.find_session(user.id, session_id)
      expect(session[:refresh_token]).to eq('test.refresh.token')
      expect(session[:company_id]).to eq(1)
    end
    
    it 'returns false when session not found' do
      result = described_class.update_session_token(user.id, 'non-existent', 'new.token')
      expect(result).to be false
    end
  end
  
  describe '.destroy_session' do
    let(:session_id) { described_class.create_session(user, jwt_payload) }
    
    it 'removes session from Redis' do
      described_class.destroy_session(user.id, session_id)
      
      key = RedisKeyBuilder.jwt_session_key(user.id, session_id)
      Redis.current.with do |conn|
        expect(conn.exists?(key)).to be false
      end
    end
    
    it 'removes session from user sessions list' do
      described_class.destroy_session(user.id, session_id)
      
      user_sessions_key = RedisKeyBuilder.jwt_user_sessions_key(user.id)
      Redis.current.with do |conn|
        sessions = conn.smembers(user_sessions_key)
        expect(sessions).not_to include(session_id)
      end
    end
    
    it 'returns true on success' do
      result = described_class.destroy_session(user.id, session_id)
      expect(result).to be true
    end
    
    it 'handles non-existent sessions gracefully' do
      result = described_class.destroy_session(user.id, 'non-existent')
      expect(result).to be true # Should not fail
    end
  end
  
  describe '.destroy_all_user_sessions' do
    it 'destroys all sessions for a user' do
      # Create multiple sessions
      session1 = described_class.create_session(user, jwt_payload)
      session2 = described_class.create_session(user, jwt_payload)
      session3 = described_class.create_session(user, jwt_payload)
      
      described_class.destroy_all_user_sessions(user.id)
      
      # Verify all sessions are gone
      expect(described_class.find_session(user.id, session1)).to be_nil
      expect(described_class.find_session(user.id, session2)).to be_nil
      expect(described_class.find_session(user.id, session3)).to be_nil
      
      # Verify user sessions list is empty
      user_sessions_key = RedisKeyBuilder.jwt_user_sessions_key(user.id)
      Redis.current.with do |conn|
        expect(conn.exists?(user_sessions_key)).to be false
      end
    end
  end
  
  describe '.get_user_sessions_details' do
    it 'returns all active sessions with details' do
      # Create multiple sessions
      session1 = described_class.create_session(user, jwt_payload)
      session2 = described_class.create_session(user, jwt_payload.merge(ip_address: '***********'))
      
      sessions = described_class.get_user_sessions_details(user.id)
      
      expect(sessions.length).to eq(2)
      expect(sessions.map { |s| s[:session_id] }).to contain_exactly(session1, session2)
      expect(sessions.map { |s| s[:ip_address] }).to contain_exactly('127.0.0.1', '***********')
    end
    
    it 'returns empty array when no sessions' do
      sessions = described_class.get_user_sessions_details(user.id)
      expect(sessions).to eq([])
    end
  end
end