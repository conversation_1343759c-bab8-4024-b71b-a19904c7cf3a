require 'rails_helper'

RSpec.describe JwtService, type: :service do
  let(:payload) { { user_id: 1, data: 'test' } }
  let(:secret) { 'test_secret_from_spec' } # Use a distinct secret for tests
  let(:custom_exp_time) { 1.hour.from_now }

  before do
    # Stub the SECRET_KEY constant for all tests in this describe block
    # This is important because the constant is set at class load time.
    stub_const("JwtService::SECRET_KEY", secret)
  end

  describe '.encode' do
    context 'with valid parameters' do
      it 'encodes the payload with jti, iat, and default expiration time (duration)' do
        travel_to Time.current do |now|
          token = described_class.encode(payload)
          decoded_token_array = JWT.decode(token, secret, true, { algorithm: 'HS256' })
          decoded_payload = decoded_token_array[0]

          expect(decoded_payload['user_id']).to eq(payload[:user_id])
          expect(decoded_payload['data']).to eq(payload[:data])
          expect(decoded_payload['jti']).to match(/\A[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}\z/i)
          expect(decoded_payload['iat']).to eq(now.to_i)

          expected_exp = (now + JwtService::DEFAULT_EXPIRATION_DURATION).to_i
          expect(decoded_payload['exp']).to eq(expected_exp)
        end
      end

      it 'encodes the payload with a custom absolute expiration time (Time object)' do
        fixed_custom_exp_time = 1.hour.from_now # Evaluate once
        travel_to Time.current do |now| # To control 'iat'
          token = described_class.encode(payload, fixed_custom_exp_time)
          decoded_payload = JWT.decode(token, secret, true, { algorithm: 'HS256' })[0]

          expect(decoded_payload['exp']).to eq(fixed_custom_exp_time.to_i)
          expect(decoded_payload['iat']).to eq(now.to_i)
          expect(decoded_payload['jti']).to be_present
        end
      end

      it 'encodes the payload with a custom absolute expiration time (Integer epoch)' do
        custom_exp_epoch = 2.hours.from_now.to_i
        travel_to Time.current do |now| # To control 'iat'
          token = described_class.encode(payload, custom_exp_epoch)
          decoded_payload = JWT.decode(token, secret, true, { algorithm: 'HS256' })[0]

          expect(decoded_payload['exp']).to eq(custom_exp_epoch)
          expect(decoded_payload['iat']).to eq(now.to_i)
          expect(decoded_payload['jti']).to be_present
        end
      end

      it 'does not modify the original payload' do
        original_payload_to_test = { user_id: 100, data: 'original' }
        described_class.encode(original_payload_to_test.dup) # Pass a dup to be safe in test setup
        expect(original_payload_to_test.keys).to contain_exactly(:user_id, :data)
        expect(original_payload_to_test).not_to include(:exp, :iat, :jti)
      end
    end

    context 'with invalid parameters' do
      it 'raises ArgumentError if payload is not a Hash' do
        expect { described_class.encode("not a hash") }.to raise_error(ArgumentError, "Payload must be a Hash")
      end

      context 'when jwt_secret is not configured (nil)' do
        before do
          stub_const("JwtService::SECRET_KEY", nil)
        end
        it 'raises an ArgumentError' do
          expect { described_class.encode(payload) }.to raise_error(ArgumentError, "JWT secret not configured")
        end
      end

      context 'when jwt_secret is not configured (empty string)' do
        before do
          stub_const("JwtService::SECRET_KEY", "")
        end
        it 'raises an ArgumentError' do
          expect { described_class.encode(payload) }.to raise_error(ArgumentError, "JWT secret not configured")
        end
      end

      it 'raises ArgumentError for invalid expiration type' do
        expect { described_class.encode(payload, "invalid_exp_type") }.to raise_error(ArgumentError, /Invalid expiration type: String/)
      end
    end
  end

  describe '.decode' do
    let(:token_payload_for_decode) { { user_id: 2, data: 'decode_test' } }
    let(:valid_token) { travel_to(Time.current) { described_class.encode(token_payload_for_decode) } }
    let(:expired_token) do
      travel_to(Time.current) { described_class.encode(token_payload_for_decode, -1.hour) } # Expired 1 hour ago
    end
    let(:base_payload_for_manual_encode) { token_payload_for_decode.merge(exp: 1.hour.from_now.to_i, iat: Time.current.to_i, jti: SecureRandom.uuid) }
    let(:invalid_signature_token) { JWT.encode(base_payload_for_manual_encode, 'wrong_secret', 'HS256') }
    let(:tampered_token) { valid_token.reverse }

    it 'decodes a valid token and returns the payload as HashWithIndifferentAccess with jti and iat' do
      decoded_payload = described_class.decode(valid_token)
      expect(decoded_payload).to be_a(HashWithIndifferentAccess)
      expect(decoded_payload[:user_id]).to eq(token_payload_for_decode[:user_id])
      expect(decoded_payload['data']).to eq(token_payload_for_decode[:data])
      expect(decoded_payload[:jti]).to match(/\A[0-9a-f]{8}-([0-9a-f]{4}-){3}[0-9a-f]{12}\z/i)
      expect(decoded_payload[:iat]).to be_a(Integer)
      expect(decoded_payload[:iat]).to be <= Time.current.to_i
    end

    it 'returns nil for an expired token' do
      expect(Rails.logger).to receive(:warn).with("JWT expired for token: #{expired_token}")
      expect(described_class.decode(expired_token)).to be_nil
    end

    it 'returns nil for a token with an invalid signature (JWT::VerificationError)' do
      expect(Rails.logger).to receive(:warn).with("JWT error: Signature verification failed for token: #{invalid_signature_token}")
      expect(described_class.decode(invalid_signature_token)).to be_nil
    end

    it 'returns nil for a malformed token (JWT::DecodeError)' do
      malformed_token = 'invalid.token.format'
      expect(Rails.logger).to receive(:warn).with("JWT error: Not enough or too many segments for token: #{malformed_token}")
      expect(described_class.decode(malformed_token)).to be_nil
    end

    it 'returns nil for a tampered token (JWT::DecodeError)' do
      # The specific error message can vary based on how the tampering affects the token structure
      expect(Rails.logger).to receive(:warn).with(/JWT error: .* for token: #{Regexp.escape(tampered_token)}/)
      expect(described_class.decode(tampered_token)).to be_nil
    end

    context 'with a different algorithm' do
      let(:token_with_different_algo) { JWT.encode(base_payload_for_manual_encode, secret, 'HS512') }

      it 'returns nil if token was encoded with a different algorithm' do
        # JWT.decode will try to verify using HS256 (as configured in JwtService)
        # but the token was signed with HS512, so signature verification will fail.
        expect(Rails.logger).to receive(:warn).with("JWT error: Signature verification failed for token: #{token_with_different_algo}")
        expect(described_class.decode(token_with_different_algo)).to be_nil
      end
    end
  end

  describe 'integration between encode and decode' do
    it 'successfully decodes a token it encoded' do
      integration_payload = { user_id: 123, role: 'admin' }
      token = described_class.encode(integration_payload)
      decoded_payload = described_class.decode(token)

      expect(decoded_payload).not_to be_nil
      expect(decoded_payload[:user_id]).to eq(integration_payload[:user_id])
      expect(decoded_payload[:role]).to eq(integration_payload[:role])
      expect(decoded_payload[:jti]).to be_present
      expect(decoded_payload[:iat]).to be_present
      expect(decoded_payload[:exp]).to be_present
    end

    it 'returns nil if trying to decode an expired self-encoded token' do
      integration_payload = { user_id: 456 }
      # Encode with an expiration time that is definitely in the past (e.g., -1 hour duration)
      token = described_class.encode(integration_payload, -1.hour)

      expect(Rails.logger).to receive(:warn).with("JWT expired for token: #{token}")
      decoded_payload = described_class.decode(token)
      expect(decoded_payload).to be_nil
    end
  end

  describe '.encode_access_token' do
    it 'encodes a payload as an access token with short expiry, jti, and iat' do
      travel_to Time.current do |now|
        token = described_class.encode_access_token(payload)
        decoded_payload = JWT.decode(token, secret, true, { algorithm: 'HS256' })[0]

        expect(decoded_payload['user_id']).to eq(payload[:user_id])
        expect(decoded_payload['jti']).to be_present
        expect(decoded_payload['iat']).to eq(now.to_i)
        expected_exp = (now + JwtService::ACCESS_TOKEN_EXPIRATION_DURATION).to_i
        expect(decoded_payload['exp']).to eq(expected_exp)
      end
    end
  end

  describe '.encode_refresh_token' do
    it 'encodes a payload as a refresh token with long expiry, type claim, jti, and iat' do
      travel_to Time.current do |now|
        token = described_class.encode_refresh_token(payload)
        decoded_payload = JWT.decode(token, secret, true, { algorithm: 'HS256' })[0]

        expect(decoded_payload['user_id']).to eq(payload[:user_id])
        expect(decoded_payload['token_type']).to eq('refresh')
        expect(decoded_payload['jti']).to be_present
        expect(decoded_payload['iat']).to eq(now.to_i)
        expected_exp = (now + JwtService::REFRESH_TOKEN_EXPIRATION_DURATION).to_i
        expect(decoded_payload['exp']).to eq(expected_exp)
      end
    end
  end
end 