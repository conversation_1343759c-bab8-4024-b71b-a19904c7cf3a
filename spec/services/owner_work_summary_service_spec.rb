# ABOUTME: Tests for OwnerWorkSummaryService - company-wide monthly work summaries
# ABOUTME: Validates summary calculation including weekend and holiday days worked

require 'rails_helper'

RSpec.describe OwnerWorkSummaryService, type: :service do
  let(:company) { create(:company) }
  let(:contract) { create(:contract, company: company, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>') }
  
  describe '.generate_summary' do
    context 'with no data' do
      it 'returns empty array when company has no contracts' do
        result = OwnerWorkSummaryService.generate_summary(company, 2025, 1)
        expect(result).to eq([])
      end
      
      it 'returns employee with zero stats when no daily logs exist' do
        contract # Create the contract
        result = OwnerWorkSummaryService.generate_summary(company, 2025, 1)
        
        expect(result.length).to eq(1)
        employee = result.first
        expect(employee[:employee_name]).to eq('<PERSON>')
        expect(employee[:employee_email]).to eq('<EMAIL>')
        expect(employee[:total_hours]).to eq(0)
        expect(employee[:days_worked]).to eq(0)
        expect(employee[:weekend_days_worked]).to eq(0)
        expect(employee[:holiday_days_worked]).to eq(0)
      end
    end
    
    context 'with work data' do
      let(:start_date) { Date.new(2025, 1, 1) } # Wednesday
      let(:weekend_date) { Date.new(2025, 1, 4) } # Saturday
      let(:holiday_date) { Date.new(2025, 1, 1) } # New Year's Day (assuming it's a holiday)
      
      before do
        # Create a weekday log
        create(:daily_log, 
          contract: contract,
          start_time: start_date.in_time_zone.change(hour: 9),
          end_time: start_date.in_time_zone.change(hour: 17),
          duration: 8.hours.to_i
        )
        
        # Create a weekend log
        create(:daily_log, 
          contract: contract,
          start_time: weekend_date.in_time_zone.change(hour: 10),
          end_time: weekend_date.in_time_zone.change(hour: 14),
          duration: 4.hours.to_i
        )
      end
      
      it 'calculates weekend days worked correctly' do
        result = OwnerWorkSummaryService.generate_summary(company, 2025, 1)
        
        employee = result.first
        expect(employee[:days_worked]).to eq(2)
        expect(employee[:weekend_days_worked]).to eq(1)
        expect(employee[:total_hours]).to eq(12.hours.to_i) # 8 + 4 hours in seconds
      end
    end
    
    context 'with holiday work' do
      let(:holiday_date) { Date.new(2025, 1, 1) } # Assuming New Year's is a holiday
      
      before do
        # Mock holiday fetcher to return our test holiday
        allow(OwnerWorkSummaryService).to receive(:fetch_holidays).with(2025, 1).and_return(['2025-01-01'])
        
        # Create work log on holiday
        create(:daily_log,
          contract: contract,
          start_time: holiday_date.in_time_zone.change(hour: 10),
          end_time: holiday_date.in_time_zone.change(hour: 16),
          duration: 6.hours.to_i
        )
      end
      
      it 'calculates holiday days worked correctly' do
        result = OwnerWorkSummaryService.generate_summary(company, 2025, 1)
        
        employee = result.first
        expect(employee[:days_worked]).to eq(1)
        expect(employee[:holiday_days_worked]).to eq(1)
        expect(employee[:weekend_days_worked]).to eq(0) # Jan 1, 2025 is a Wednesday
      end
      
      it 'does not count holiday as worked when no log exists' do
        # Remove the daily log
        contract.daily_logs.destroy_all
        
        result = OwnerWorkSummaryService.generate_summary(company, 2025, 1)
        
        employee = result.first
        expect(employee[:days_worked]).to eq(0)
        expect(employee[:holiday_days_worked]).to eq(0)
      end
    end
    
    context 'with events (vacation, illness, etc.)' do
      before do
        # Create vacation event
        create(:event,
          contract: contract,
          event_type: 'vacation',
          start_time: Date.new(2025, 1, 2).beginning_of_day,
          end_time: Date.new(2025, 1, 3).end_of_day
        )
        
        # Create illness event
        create(:event,
          contract: contract,
          event_type: 'illness', 
          start_time: Date.new(2025, 1, 5).beginning_of_day,
          end_time: Date.new(2025, 1, 5).end_of_day
        )
        
        # Create day_care event
        create(:event,
          contract: contract,
          event_type: 'day_care',
          start_time: Date.new(2025, 1, 7).beginning_of_day,
          end_time: Date.new(2025, 1, 7).end_of_day
        )
      end
      
      it 'calculates event counts correctly' do
        result = OwnerWorkSummaryService.generate_summary(company, 2025, 1)
        
        employee = result.first
        expect(employee[:event_counts]['vacation']).to eq(2) # 2 working days (Thu-Fri)
        expect(employee[:event_counts]['illness']).to eq(0) # 0 working days (Sun counts as 0 since not weekday)
        expect(employee[:event_counts]['day_care']).to eq(1) # 1 working day  
        expect(employee[:event_counts]['family_sick']).to eq(0)
        expect(employee[:event_counts]['other']).to eq(0)
      end
    end
    
    context 'with interaction between work logs and events' do
      let(:conflicted_date) { Date.new(2025, 1, 6) } # Monday
      
      before do
        # Mock holidays (none for this test)
        allow(OwnerWorkSummaryService).to receive(:fetch_holidays).with(2025, 1).and_return([])
        
        # Create both a work log AND an event on the same day
        create(:daily_log,
          contract: contract,
          start_time: conflicted_date.in_time_zone.change(hour: 9),
          end_time: conflicted_date.in_time_zone.change(hour: 17), 
          duration: 8.hours.to_i
        )
        
        create(:event,
          contract: contract,
          event_type: 'illness',
          start_time: conflicted_date.beginning_of_day,
          end_time: conflicted_date.end_of_day
        )
      end
      
      it 'does not count event days when employee actually worked' do
        result = OwnerWorkSummaryService.generate_summary(company, 2025, 1)
        
        employee = result.first
        expect(employee[:days_worked]).to eq(1) # Employee worked
        expect(employee[:total_hours]).to eq(8.hours.to_i)
        expect(employee[:event_counts]['illness']).to eq(0) # Should not count since they worked
      end
    end
    
    context 'with multiple employees' do
      let(:contract2) { create(:contract, company: company, first_name: 'Jane', last_name: 'Smith', email: '<EMAIL>') }
      
      before do
        # Employee 1 (John) - works on weekdays
        create(:daily_log,
          contract: contract,
          start_time: Date.new(2025, 1, 2).in_time_zone.change(hour: 9),
          end_time: Date.new(2025, 1, 2).in_time_zone.change(hour: 17),
          duration: 8.hours.to_i
        )
        
        # Employee 2 (Jane) - works on weekend
        create(:daily_log,
          contract: contract2,
          start_time: Date.new(2025, 1, 4).in_time_zone.change(hour: 10), # Saturday
          end_time: Date.new(2025, 1, 4).in_time_zone.change(hour: 14),
          duration: 4.hours.to_i  
        )
        
        # Employee 2 also has vacation
        create(:event,
          contract: contract2,
          event_type: 'vacation',
          start_time: Date.new(2025, 1, 6).beginning_of_day,
          end_time: Date.new(2025, 1, 7).end_of_day
        )
      end
      
      it 'returns correct summary for each employee' do
        result = OwnerWorkSummaryService.generate_summary(company, 2025, 1)
        
        expect(result.length).to eq(2)
        
        # Find John's data
        john = result.find { |emp| emp[:employee_name] == 'John Doe' }
        expect(john[:days_worked]).to eq(1)
        expect(john[:weekend_days_worked]).to eq(0)
        expect(john[:total_hours]).to eq(8.hours.to_i)
        
        # Find Jane's data  
        jane = result.find { |emp| emp[:employee_name] == 'Jane Smith' }
        expect(jane[:days_worked]).to eq(1)
        expect(jane[:weekend_days_worked]).to eq(1) # Worked Saturday
        expect(jane[:total_hours]).to eq(4.hours.to_i)
        expect(jane[:event_counts]['vacation']).to eq(2) # Mon-Tue vacation days
      end
    end
    
    context 'with date boundaries' do
      it 'ignores logs outside the month' do
        # Create log in previous month
        create(:daily_log,
          contract: contract,
          start_time: Date.new(2024, 12, 31).in_time_zone.change(hour: 9),
          end_time: Date.new(2024, 12, 31).in_time_zone.change(hour: 17),
          duration: 8.hours.to_i
        )
        
        # Create log in next month
        create(:daily_log,
          contract: contract,
          start_time: Date.new(2025, 2, 1).in_time_zone.change(hour: 9),
          end_time: Date.new(2025, 2, 1).in_time_zone.change(hour: 17),
          duration: 8.hours.to_i
        )
        
        result = OwnerWorkSummaryService.generate_summary(company, 2025, 1)
        
        employee = result.first
        expect(employee[:days_worked]).to eq(0)
        expect(employee[:total_hours]).to eq(0)
      end
    end
  end
end