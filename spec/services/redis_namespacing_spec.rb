# spec/services/redis_namespacing_spec.rb
require 'rails_helper'

RSpec.describe 'Redis Namespacing' do
  before do
    # Clear test Redis to ensure clean state
    Redis.current.flushdb
  end
  
  describe RedisNamespacing do
    describe '.all_namespaces' do
      it 'returns all defined namespaces' do
        namespaces = RedisNamespacing.all_namespaces
        expect(namespaces).to include(:jwt_sessions, :jwt_revoked, :rate_limit, :cache)
      end
    end
    
    describe '.namespace_exists?' do
      it 'returns true for existing namespaces' do
        expect(RedisNamespacing.namespace_exists?(:jwt_sessions)).to be true
        expect(RedisNamespacing.namespace_exists?('jwt_sessions')).to be true
      end
      
      it 'returns false for non-existing namespaces' do
        expect(RedisNamespacing.namespace_exists?(:nonexistent)).to be false
      end
    end
    
    describe '.namespace_prefix' do
      it 'returns correct prefix for namespace' do
        prefix = RedisNamespacing.namespace_prefix(:jwt_sessions)
        expect(prefix).to include('attendifyapp')
        expect(prefix).to include('jwt:sessions')
      end
    end
  end
  
  describe RedisKeyBuilder do
    describe '.key_for' do
      it 'generates correctly namespaced keys' do
        key = RedisKeyBuilder.key_for(:jwt_sessions, 'user_123', 'session_456')
        expect(key).to include('attendifyapp')
        expect(key).to include('jwt:sessions')
        expect(key).to include('user_123:session_456')
      end
      
      it 'raises error for unknown namespace' do
        expect {
          RedisKeyBuilder.key_for(:unknown_namespace, 'test')
        }.to raise_error(ArgumentError, /Unknown namespace/)
      end
    end
    
    describe 'specific key builders' do
      it 'generates JWT session keys correctly' do
        key = RedisKeyBuilder.jwt_session_key(123, 'session_456')
        expect(key).to include('jwt:sessions')
        expect(key).to include('123:session_456')
      end
      
      it 'generates JWT user sessions keys correctly' do
        key = RedisKeyBuilder.jwt_user_sessions_key(123)
        expect(key).to include('jwt:sessions')
        expect(key).to include('user:123:list')
      end
      
      it 'generates JWT revocation keys correctly' do
        key = RedisKeyBuilder.jwt_revocation_key('jti_123')
        expect(key).to include('jwt:revoked')
        expect(key).to include('jti_123')
      end
      
      it 'generates cache keys correctly' do
        key = RedisKeyBuilder.cache_key('user', 123, 'profile')
        expect(key).to include('cache')
        expect(key).to include('user:123:profile')
      end
    end
  end
  
  describe RedisService do
    describe 'basic operations' do
      it 'sets and gets values with proper namespacing' do
        result = RedisService.set(:cache, ['test', 'key'], 'test_value')
        expect(result).to be true
        
        value = RedisService.get(:cache, ['test', 'key'])
        expect(value).to eq('test_value')
      end
      
      it 'sets and gets JSON values' do
        data = { user_id: 123, name: 'Test User' }
        RedisService.set(:cache, ['user', 123], data)
        
        retrieved = RedisService.get(:cache, ['user', 123])
        expect(retrieved['user_id']).to eq(123)
        expect(retrieved['name']).to eq('Test User')
      end
      
      it 'handles TTL correctly' do
        RedisService.set(:cache, ['expiring', 'key'], 'value', ttl: 1)
        
        value = RedisService.get(:cache, ['expiring', 'key'])
        expect(value).to eq('value')
        
        ttl = RedisService.ttl(:cache, ['expiring', 'key'])
        expect(ttl).to be > 0
        expect(ttl).to be <= 1
      end
      
      it 'handles set operations' do
        RedisService.sadd(:cache, ['test', 'set'], 'member1')
        RedisService.sadd(:cache, ['test', 'set'], 'member2')
        
        members = RedisService.smembers(:cache, ['test', 'set'])
        expect(members).to contain_exactly('member1', 'member2')
        
        RedisService.srem(:cache, ['test', 'set'], 'member1')
        members = RedisService.smembers(:cache, ['test', 'set'])
        expect(members).to contain_exactly('member2')
      end
    end
    
    describe 'error handling' do
      it 'returns false on Redis errors for set operations' do
        # Simulate Redis error
        allow(Redis.current).to receive(:setex).and_raise(Redis::ConnectionError.new("Connection failed"))
        
        result = RedisService.set(:cache, ['test'], 'value', ttl: 60)
        expect(result).to be false
      end
      
      it 'returns nil on Redis errors for get operations' do
        allow(Redis.current).to receive(:get).and_raise(Redis::ConnectionError)
        
        result = RedisService.get(:cache, ['test'])
        expect(result).to be_nil
      end
    end
    
    describe 'namespace isolation' do
      it 'isolates different namespaces' do
        # Set same key in different namespaces
        RedisService.set(:cache, ['test'], 'cache_value')
        RedisService.set(:metrics, ['test'], 'metrics_value')
        
        cache_value = RedisService.get(:cache, ['test'])
        metrics_value = RedisService.get(:metrics, ['test'])
        
        expect(cache_value).to eq('cache_value')
        expect(metrics_value).to eq('metrics_value')
      end
      
      it 'prevents cross-namespace key collisions' do
        # Create keys that would collide without namespacing
        RedisService.set(:jwt_sessions, ['123', 'abc'], 'session_data')
        RedisService.set(:cache, ['123', 'abc'], 'cache_data')
        
        session_data = RedisService.get(:jwt_sessions, ['123', 'abc'])
        cache_data = RedisService.get(:cache, ['123', 'abc'])
        
        expect(session_data).to eq('session_data')
        expect(cache_data).to eq('cache_data')
      end
    end
  end
  
  describe 'Integration with existing services' do
    let(:user) { create(:user) }
    
    it 'JWT sessions use proper namespacing' do
      jwt_payload = {
        access_token: 'test.token',
        refresh_token: 'test.refresh',
        company_id: 1,
        ip_address: '127.0.0.1',
        user_agent: 'RSpec'
      }
      
      session_id = JwtSessionService.create_session(user, jwt_payload)
      
      # Verify the key exists in Redis with proper namespace
      key = RedisKeyBuilder.jwt_session_key(user.id, session_id)
      expect(Redis.current.exists?(key)).to be true
      
      # Verify namespace isolation - key shouldn't exist in other namespaces
      cache_key = RedisKeyBuilder.cache_key(user.id, session_id)
      expect(Redis.current.exists?(cache_key)).to be false
    end
    
    it 'JWT revocation uses proper namespacing' do
      strategy = JwtRevocationStrategy.new
      payload = { 'jti' => 'test_jti', 'exp' => 1.hour.from_now.to_i }
      
      strategy.revoke_jwt(payload, user)
      
      # Verify revocation key exists with proper namespace
      revocation_key = RedisKeyBuilder.jwt_revocation_key('test_jti')
      expect(Redis.current.exists?(revocation_key)).to be true
      
      # Verify it's isolated from other namespaces
      cache_key = RedisKeyBuilder.cache_key('test_jti')
      expect(Redis.current.exists?(cache_key)).to be false
    end
  end
end