# spec/services/jwt_token_rotation_spec.rb
require 'rails_helper'

RSpec.describe 'JWT Token Rotation', type: :request do
  let(:user) { create(:user) }
  let(:company) { user.primary_company }
  
  before do
    ActsAsTenant.current_tenant = company
  end

  describe 'Token Rotation Lifecycle' do
    context 'successful refresh token rotation' do
      it 'rotates refresh tokens properly' do
        # Initial login to get tokens
        post '/api/v1/auth/jwt_login', params: {
          email: user.email,
          password: 'password'
        }
        
        expect(response).to have_http_status(:ok)
        initial_response = JSON.parse(response.body)
        initial_access_token = initial_response['access_token']
        
        # Refresh token should be in HttpOnly cookie
        expect(response.cookies['refresh_token']).to be_present
        
        # Wait to ensure token timestamps differ
        sleep(1)
        
        # Refresh the token
        post '/api/v1/auth/refresh_token'
        
        expect(response).to have_http_status(:ok)
        refresh_response = JSON.parse(response.body)
        new_access_token = refresh_response['access_token']
        
        # New access token should be different
        expect(new_access_token).not_to eq(initial_access_token)
        
        # Verify token family tracking
        initial_payload = JwtService.decode(initial_access_token)
        new_payload = JwtService.decode(new_access_token)
        
        expect(new_payload['user_id']).to eq(initial_payload['user_id'])
        expect(new_payload['company_id']).to eq(initial_payload['company_id'])
      end
      
      it 'tracks rotation count in refresh tokens' do
        # Login to get initial refresh token
        post '/api/v1/auth/jwt_login', params: {
          email: user.email,
          password: 'password'
        }
        
        # Extract refresh token from cookie for testing
        refresh_token = response.cookies['refresh_token']
        initial_payload = JwtService.decode(refresh_token)
        
        # Initial rotation count should be 0
        expect(initial_payload['rotation_count']).to eq(0)
        expect(initial_payload['refresh_family_id']).to be_present
        family_id = initial_payload['refresh_family_id']
        
        # Perform refresh
        post '/api/v1/auth/refresh_token'
        expect(response).to have_http_status(:ok)
        
        # New refresh token should have incremented rotation count
        new_refresh_token = response.cookies['refresh_token']
        new_payload = JwtService.decode(new_refresh_token)
        
        expect(new_payload['rotation_count']).to eq(1)
        expect(new_payload['refresh_family_id']).to eq(family_id)
      end
    end

    context 'refresh token reuse detection' do
      it 'detects and blocks reused refresh tokens' do
        # Initial login
        post '/api/v1/auth/jwt_login', params: {
          email: user.email,
          password: 'password'
        }
        
        old_refresh_token = response.cookies['refresh_token']
        
        # First refresh - should succeed
        post '/api/v1/auth/refresh_token'
        expect(response).to have_http_status(:ok)
        
        # Manually set the old (now revoked) refresh token
        cookies['refresh_token'] = old_refresh_token
        
        # Attempt to use old refresh token - should fail
        post '/api/v1/auth/refresh_token'
        expect(response).to have_http_status(:unauthorized)
        
        error_response = JSON.parse(response.body)
        expect(error_response['error']).to eq('Refresh token has been revoked')
      end
      
      it 'logs security events for token reuse' do
        # Initial login
        post '/api/v1/auth/jwt_login', params: {
          email: user.email,
          password: 'password'
        }
        
        old_refresh_token = response.cookies['refresh_token']
        
        # First refresh
        post '/api/v1/auth/refresh_token'
        
        # Manually set old token
        cookies['refresh_token'] = old_refresh_token
        
        # Expect security logging for reuse attempt
        expect(Rails.logger).to receive(:error).with(
          include('[SECURITY] Refresh token reuse detected')
        )
        
        post '/api/v1/auth/refresh_token'
      end
    end

    context 'excessive rotation count protection' do
      it 'blocks tokens with suspicious rotation counts' do
        # Create a token with high rotation count
        payload = user.jwt_payload.merge(
          token_type: 'refresh',
          refresh_family_id: SecureRandom.uuid,
          rotation_count: 1001 # Above threshold
        )
        
        suspicious_token = JwtService.encode(payload, 7.days)
        cookies['refresh_token'] = suspicious_token
        
        # Should be blocked
        post '/api/v1/auth/refresh_token'
        expect(response).to have_http_status(:unauthorized)
        
        error_response = JSON.parse(response.body)
        expect(error_response['error']).to eq('Invalid refresh token format')
      end
    end

    context 'malformed refresh tokens' do
      it 'handles invalid token format' do
        cookies['refresh_token'] = 'invalid.token.format'
        
        post '/api/v1/auth/refresh_token'
        expect(response).to have_http_status(:unauthorized)
        
        error_response = JSON.parse(response.body)
        expect(error_response['error']).to eq('Invalid or expired refresh token')
      end
      
      it 'handles missing token type' do
        # Create token without token_type
        payload = user.jwt_payload # Missing token_type: 'refresh'
        invalid_token = JwtService.encode(payload, 7.days)
        cookies['refresh_token'] = invalid_token
        
        post '/api/v1/auth/refresh_token'
        expect(response).to have_http_status(:unauthorized)
        
        error_response = JSON.parse(response.body)
        expect(error_response['error']).to eq('Invalid refresh token format')
      end
    end

    context 'JWT-only mode restrictions' do
      before do
        allow(FeatureFlags).to receive(:jwt_only_mode_enabled?).and_return(true)
      end
      
      it 'only accepts refresh tokens from HttpOnly cookies in JWT-only mode' do
        # Login to establish session
        post '/api/v1/auth/jwt_login', params: {
          email: user.email,
          password: 'password'
        }
        
        refresh_token = response.cookies['refresh_token']
        
        # Clear the cookie and try to send via body (should fail in JWT-only mode)
        cookies.delete('refresh_token')
        
        post '/api/v1/auth/refresh_token', params: {
          refresh_token: refresh_token
        }
        
        expect(response).to have_http_status(:unauthorized)
        error_response = JSON.parse(response.body)
        expect(error_response['error']).to eq('Refresh token required in secure cookie')
      end
    end

    context 'password reset integration' do
      it 'creates properly formatted tokens after password reset' do
        # Generate a password reset token for the user
        raw_token, encrypted_token = Devise.token_generator.generate(User, :reset_password_token)
        user.update!(
          reset_password_token: encrypted_token,
          reset_password_sent_at: Time.current
        )
        
        # Reset password via API
        post '/api/v1/auth/password_reset', params: {
          reset_password_token: raw_token,
          password: 'newpassword',
          password_confirmation: 'newpassword'
        }
        
        expect(response).to have_http_status(:ok)
        reset_response = JSON.parse(response.body)
        access_token = reset_response['access_token']
        
        # Verify access token is properly formatted
        access_payload = JwtService.decode(access_token)
        expect(access_payload['user_id']).to eq(user.id)
        
        # Verify refresh token in cookie is properly formatted
        refresh_token = response.cookies['refresh_token']
        expect(refresh_token).to be_present
        
        refresh_payload = JwtService.decode(refresh_token)
        expect(refresh_payload['token_type']).to eq('refresh')
        expect(refresh_payload['refresh_family_id']).to be_present
        expect(refresh_payload['rotation_count']).to eq(0)
        
        # Test that token refresh works correctly after password reset
        sleep(1) # Ensure different timestamps
        
        post '/api/v1/auth/refresh_token'
        expect(response).to have_http_status(:ok)
        
        refresh_response = JSON.parse(response.body)
        new_access_token = refresh_response['access_token']
        
        # Verify new token is different and properly formatted
        expect(new_access_token).not_to eq(access_token)
        new_payload = JwtService.decode(new_access_token)
        expect(new_payload['user_id']).to eq(user.id)
      end
    end

    context 'Redis session integration' do
      it 'updates Redis session on token refresh' do
        # Login
        post '/api/v1/auth/jwt_login', params: {
          email: user.email,
          password: 'password'
        }
        
        initial_access_token = JSON.parse(response.body)['access_token']
        
        # Wait to ensure different expiry times
        sleep(2)
        
        # Refresh token
        post '/api/v1/auth/refresh_token'
        
        new_access_token = JSON.parse(response.body)['access_token']
        
        # Verify Redis session was updated 
        # Note: In tests, signed cookies may not be accessible the same way
        # The important part is that new access token is different, indicating refresh worked
        expect(new_access_token).not_to eq(initial_access_token)
        
        # Verify tokens have different expiry times
        initial_payload = JwtService.decode(initial_access_token)
        new_payload = JwtService.decode(new_access_token)
        expect(new_payload['exp']).to be > initial_payload['exp']
      end
    end
  end
end