# ABOUTME: Integration test for OtherActivitiesSection component in Mainbox using Capybara+Cuprite
# ABOUTME: Verifies Phase 2 bug fix - Other Activities visible in Mainbox after TodayWorkSection refactor

require 'rails_helper'

RSpec.describe 'Mainbox Other Activities Integration', type: :feature, js: true do
  before do
    # Sign in using test credentials
    sign_in_test_user
  end

  describe 'OtherActivitiesSection component visibility' do
    it 'displays the Other Activities section in the Mainbox' do
      visit root_path
      
      # Wait for Vue components to load
      wait_for_vite_assets
      
      # Check that the Other Activities section is present
      expect(page).to have_css('[data-vue-component="other-activities-section"]', wait: 10)
      
      # Check that the section header is visible
      expect(page).to have_text('Ostatní aktivity')
      
      # Take a screenshot for debugging
      screenshot_for_claude('other_activities_section_visible')
    end

    it 'shows both TodayWorkSection and OtherActivitiesSection in the same column' do
      visit root_path
      wait_for_vite_assets
      
      # Verify both sections are present in the Mainbox
      expect(page).to have_css('[data-vue-component="today-work-section"]')
      expect(page).to have_css('[data-vue-component="other-activities-section"]')
      
      # Check that they're in the same column (first column)
      within('.grid-cols-3 > div:first-child') do
        expect(page).to have_css('[data-vue-component="today-work-section"]')
        expect(page).to have_css('[data-vue-component="other-activities-section"]')
      end
    end
  end

  private

  def wait_for_vite_assets
    # Wait for Vue components to load
    sleep(2)
  end
end