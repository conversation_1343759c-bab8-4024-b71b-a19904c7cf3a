# ABOUTME: Tests for service worker update handling fix (TYM-77)
# ABOUTME: Verifies that service worker updates are handled with user control instead of aggressive automatic updates

require 'rails_helper'

RSpec.feature 'Service Worker Update Handling', type: :feature, js: true do
  describe 'Service Worker Registration' do
    scenario 'service worker file contains controlled update behavior' do
      visit '/sw.js'
      
      expect(page.status_code).to eq(200)
      expect(page.response_headers['Content-Type']).to include('javascript')
      
      # Verify skipWaiting was removed from install event
      expect(page.body).not_to include('return self.skipWaiting()')
      expect(page.body).to include('Skip waiting removed - let new SW wait for all clients to close')
      
      # Verify message handler still exists for user-controlled updates
      expect(page.body).to include("event.data.type === 'SKIP_WAITING'")
      expect(page.body).to include('self.skipWaiting()')
    end

    scenario 'application.js contains controlled update registration logic' do
      visit '/cs/dashboard'
      
      # Check that controlled update logic is present in the page
      page_source = page.html
      
      # Verify controllerchange handler is present
      expect(page_source).to include('controllerchange')
      expect(page_source).to include('refreshing')
      expect(page_source).to include('window.location.reload()')
      
      # Verify user prompt function exists
      expect(page_source).to include('showUpdatePrompt')
      expect(page_source).to include('confirm(')
      expect(page_source).to include('A new version of the app is available')
    end
  end

  describe 'Update Strategy' do
    scenario 'service worker update process is user-controlled' do
      visit '/cs/dashboard'
      
      page_source = page.html
      
      # Verify automatic update was replaced with user prompt
      expect(page_source).not_to include('newWorker.postMessage({ type: \'SKIP_WAITING\' });')
      expect(page_source).to include('showUpdatePrompt(newWorker)')
      
      # Verify user can decline updates
      expect(page_source).to include('User declined update')
      expect(page_source).to include('will update on next page load')
    end
  end
end