# ABOUTME: Capybara test for TYM-115 login-logout loop fix
# ABOUTME: Verifies company context is set properly and prevents logout loop

require 'rails_helper'

RSpec.describe 'TYM-115 Login Fix', type: :feature, js: true do
  let(:owner_user) { User.find_by(email: '<EMAIL>') }
  let(:password) { '123456' }
  
  before do
    # Ensure test user has a primary company
    if owner_user
      role = owner_user.company_user_roles.first
      role&.update!(is_primary: true)
    end
  end
  
  it 'successfully logs in and stays logged in with company context' do
    # Navigate to login page
    visit '/cs/users/sign_in'
    
    # Perform login
    puts "Logging in as #{owner_user.email}..."
    fill_in 'user_email', with: owner_user.email
    fill_in 'user_password', with: password
    click_button 'Přihlásit se'
    
    # Wait for navigation to dashboard
    expect(page).to have_current_path(/daily_logs/, wait: 10)
    puts "✓ Successfully navigated to dashboard"
    
    # Verify we're still on the dashboard (not redirected to login)
    sleep 2 # Give time for any potential logout to occur
    expect(current_path).not_to include('/sign_in')
    puts "✓ Still logged in after 2 seconds"
    
    # Test navigation to different pages to ensure API calls work
    # Navigate to employees page
    visit '/cs/employees'
    
    # Check we're still logged in
    expect(current_path).not_to include('/sign_in')
    puts "✓ Successfully accessed employees page"
    
    # Navigate to contracts page  
    visit '/cs/contracts'
    
    # Check we're still logged in
    expect(current_path).not_to include('/sign_in')
    puts "✓ Successfully accessed contracts page"
    
    # Test page refresh to verify session restoration works
    puts "Testing page refresh..."
    page.driver.browser.navigate.refresh
    sleep 1
    
    # Verify still logged in after refresh
    expect(current_path).not_to include('/sign_in')
    puts "✓ Still logged in after page refresh"
    
    # Check for any error messages
    expect(page).not_to have_css('.flash-message.error')
    puts "✓ No error messages displayed"
    
    puts "\n✅ TYM-115 Fix Verified: Login-logout loop is resolved!"
  end
  
  it 'handles missing primary company gracefully' do
    # Temporarily remove primary company flag
    owner_user.company_user_roles.update_all(is_primary: false)
    
    # Navigate to login page
    visit '/cs/users/sign_in'
    
    # Perform login
    fill_in 'user_email', with: owner_user.email
    fill_in 'user_password', with: password
    click_button 'Přihlásit se'
    
    # Should still login successfully with fallback to first company
    expect(page).to have_current_path(/daily_logs/, wait: 10)
    puts "✓ Login successful even without primary company"
    
    # Verify API calls work (fallback should select first company)
    sleep 2
    expect(current_path).not_to include('/sign_in')
    puts "✓ Fallback company selection working"
    
    # Restore primary company for other tests
    owner_user.company_user_roles.first&.update!(is_primary: true)
  end
end