# ABOUTME: Feature test for calendar day click functionality and current day styling
# ABOUTME: Tests the NewMonthlyCalendar component's new day click and today highlighting features

require 'rails_helper'

RSpec.describe 'Calendar Day Click Functionality', type: :feature, js: true do
  before do
    sign_in_test_user
    navigate_to('events')
  end

  describe 'Calendar rendering' do
    it 'renders the calendar without errors' do
      expect(page).to have_css('[data-vue-component="new-monthly-calendar"]', wait: 5)
      expect(page).to have_css('.calendar-grid')
      expect(page).to have_css('.calendar-day-cell')
    end

    it 'highlights today with special styling' do
      today = Date.current.day
      today_cell = page.find("[data-testid='calendar-day-#{today}']", wait: 5)
      expect(today_cell[:class]).to include('today')
    end
  end

  describe 'Day click functionality' do
    it 'shows create buttons when clicking on a day' do
      # Find the first available day cell
      first_day = page.first("[data-testid^='calendar-day-']", wait: 5)
      first_day.click
      
      # Should show create buttons
      expect(page).to have_css('.day-create-buttons', wait: 2)
      expect(page).to have_css('.create-event-btn')
      expect(page).to have_css('.create-work-btn')
    end

    it 'opens event form when clicking create event button' do
      first_day = page.first("[data-testid^='calendar-day-']", wait: 5)
      first_day.click
      
      # Click the create event button
      page.find('.create-event-btn', wait: 2).click
      
      # Should open event form modal
      expect(page).to have_css('.modal-overlay', wait: 3)
      expect(page).to have_text('Nová událost')
    end

    it 'opens work form when clicking create work button' do
      first_day = page.first("[data-testid^='calendar-day-']", wait: 5)
      first_day.click
      
      # Click the create work button
      page.find('.create-work-btn', wait: 2).click
      
      # Should open work form modal
      expect(page).to have_css('.modal-overlay', wait: 3)
      expect(page).to have_text('Nová zakázka')
    end
  end

  describe 'Hover functionality' do
    it 'shows create buttons on hover' do
      first_day = page.first("[data-testid^='calendar-day-']", wait: 5)
      first_day.hover
      
      # Should show create buttons on hover
      expect(page).to have_css('.day-create-buttons', wait: 2)
    end
  end
end