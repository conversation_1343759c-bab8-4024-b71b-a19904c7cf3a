# ABOUTME: Test for inline company editing feature (TYM-117)
# ABOUTME: Verifies company index displays with inline forms and subscription cards

require 'rails_helper'

RSpec.feature "Company Inline Editing", type: :feature, js: true do
  let(:owner_email) { "<EMAIL>" }
  let(:owner_password) { "123456" }
  
  scenario "displays company with inline editing and subscription card" do
    # Login with Playwright fixture
    visit "/login"
    fill_in "user_email", with: owner_email
    fill_in "user_password", with: owner_password
    click_button "Přihlásit se"
    
    # Navigate to companies page
    visit "/companies"
    
    # Wait for page to load
    expect(page).to have_content("Pracovní prostory", wait: 10)
    
    # Check that company switcher buttons are visible if multiple companies
    # (May not be visible if only one company)
    
    # Check for inline company information form
    expect(page).to have_css(".card", minimum: 1)
    expect(page).to have_content("Informace o pracovním prostoru")
    
    # Check for form fields
    expect(page).to have_field("company_name")
    expect(page).to have_field("company_address")
    expect(page).to have_field("company_phone")
    expect(page).to have_field("company_web")
    expect(page).to have_field("company_description")
    
    # Check for settings form
    expect(page).to have_content("Nastavení")
    expect(page).to have_field("break_duration")
    expect(page).to have_field("approve_vacations")
    
    # Check for subscription card
    expect(page).to have_content("Váš plán")
    expect(page).to have_css(".border-dashed")
    
    # Check for plan features
    expect(page).to have_content("Přehled kdo je na místě")
    
    # Test that form can be submitted (if user has permission)
    if page.has_button?("Uložit informace")
      # Fill and submit form
      fill_in "company_name", with: "Test Company Updated"
      click_button "Uložit informace"
      
      # Check for success message
      expect(page).to have_content("úspěšně")
    end
  end
end