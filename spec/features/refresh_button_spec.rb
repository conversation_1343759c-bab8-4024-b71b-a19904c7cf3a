# ABOUTME: Test spec for PWA refresh button functionality  
# ABOUTME: Verifies that refresh buttons are present and functional across target views

require 'rails_helper'

RSpec.feature 'Refresh Button', type: :feature, js: true do
  let(:company) { create(:company, name: "Test Company") }
  let(:owner) { create(:user, email: '<EMAIL>', password: '123456') }
  
  before do
    # Setup owner role
    owner_role = Role.find_or_create_by(name: 'owner')
    
    # Create owner's company relationship
    CompanyUserRole.create!(
      user: owner,
      company: company,
      role: owner_role,
      active: true,
      is_primary: true
    )
    
    # Login as owner
    visit '/users/sign_in'
    fill_in 'user_email', with: owner.email
    fill_in 'user_password', with: owner.password
    click_button 'Přihlásit se'
  end

  describe 'Refresh button presence' do
    it 'shows refresh button on contracts page' do
      visit '/contracts'
      expect(page).to have_css('[data-testid="refresh-contracts"]', wait: 5)
    end

    it 'shows refresh button on mainbox page' do
      visit '/'
      expect(page).to have_css('[data-testid="refresh-mainbox"]', wait: 5)
    end

    it 'shows refresh button on events page' do
      visit '/events'
      expect(page).to have_css('[data-testid="refresh-events"]', wait: 5)
    end

    it 'shows refresh button on reports page' do
      visit '/reports/works_summary'
      expect(page).to have_css('[data-testid="refresh-reports"]', wait: 5)
    end
  end

  describe 'Refresh button functionality' do
    it 'triggers page reload when clicked on contracts page' do
      visit '/contracts'
      
      # Store current time
      initial_load_time = page.evaluate_script('Date.now()')
      
      # Click refresh button
      find('[data-testid="refresh-contracts"]').click
      
      # Wait for reload to complete
      sleep 1
      
      # Check if page was reloaded by comparing timestamps
      new_load_time = page.evaluate_script('Date.now()')
      expect(new_load_time).to be > initial_load_time
    end
  end
end