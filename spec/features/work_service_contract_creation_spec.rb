# ABOUTME: Request tests verifying service contract creation via API endpoint fix
# ABOUTME: Tests that WorkForm.vue correctly calls /api/v1/works instead of /works for service contract creation
require 'rails_helper'
require 'support/jwt_helpers'

RSpec.describe 'Work Service Contract Creation via API', type: :request do
  include JwtHelpers
  let(:company) { create(:company, name: "Service Contract Test Company", subdomain: "servicetest") }
  let(:owner) { create(:user, email: "<EMAIL>") }
  let(:owner_role) { create(:role, name: "owner") }
  let(:user_role) { create(:company_user_role, company: company, user: owner, role: owner_role, is_primary: true) }
  let(:contract) { create(:contract, :with_user, company: company, user: owner) }

  before do
    # Set up company context
    ActsAsTenant.current_tenant = company
    
    # Create user role for authentication
    user_role
    
    # Create contract for assignments
    contract
  end

  def jwt_auth_headers(user = owner)
    # Create JWT token with proper company context
    payload = {
      user_id: user.id,
      email: user.email,
      company_id: company.id
    }
    
    token = JwtService.encode(payload, 1.hour.from_now)
    auth_headers(token)
  end

  context 'when creating a work with a new service contract' do
    it 'successfully creates both work and service contract via /api/v1/works endpoint' do
      work_params = {
        title: 'Test Work with Service Contract',
        description: 'Work requiring service contract',
        status: 'scheduled'
      }

      service_contract_params = {
        service_contract_id: 'new',
        service_contract_title: 'Test Service Contract',
        service_contract_description: 'Service contract for testing'
      }

      assignment_params = {
        assigned_contracts: [contract.id]
      }

      payload = work_params.merge(service_contract_params).merge(assignment_params)

      expect {
        post '/api/v1/works', params: payload, headers: jwt_auth_headers.merge({
          'Accept' => 'application/json'
        })
      }.to change(Work, :count).by(1)
        .and change(ServiceContract, :count).by(1)

      expect(response).to have_http_status(:created)
      response_data = JSON.parse(response.body)

      # Verify the work was created with service contract
      created_work = Work.find(response_data['id'])
      expect(created_work.title).to eq('Test Work with Service Contract')
      expect(created_work.service_contract).to be_present
      expect(created_work.service_contract.title).to eq('Test Service Contract')
      expect(created_work.service_contract.description).to eq('Service contract for testing')
      expect(created_work.service_contract.status).to eq('scheduled')
      
      # Verify response includes service contract info
      expect(response_data['service_contract']).to be_present
      expect(response_data['service_contract']['title']).to eq('Test Service Contract')
    end

    it 'successfully creates work and service contract even without title (uses generated title)' do
      work_params = {
        title: 'Test Work',
        description: 'Work requiring service contract',
        status: 'scheduled'
      }

      service_contract_params = {
        service_contract_id: 'new',
        # No service_contract_title provided - should use generated title
        service_contract_description: 'Service contract for testing'
      }

      assignment_params = {
        assigned_contracts: [contract.id]
      }

      payload = work_params.merge(service_contract_params).merge(assignment_params)

      expect {
        post '/api/v1/works', params: payload, headers: jwt_auth_headers.merge({
          'Accept' => 'application/json'
        })
      }.to change(Work, :count).by(1)
        .and change(ServiceContract, :count).by(1)

      expect(response).to have_http_status(:created)
      
      response_data = JSON.parse(response.body)
      created_work = Work.find(response_data['id'])
      expect(created_work.service_contract).to be_present
      expect(created_work.service_contract.title).to be_present  # Should have generated title
    end
  end

  context 'when creating a work with an existing service contract' do
    let!(:existing_service_contract) do
      create(:service_contract, 
        company: company,
        title: 'Existing Service Contract',
        status: 'scheduled'
      )
    end

    it 'successfully assigns existing service contract to work' do
      work_params = {
        title: 'Test Work with Existing Contract',
        description: 'Work using existing service contract',
        status: 'scheduled'
      }

      service_contract_params = {
        service_contract_id: existing_service_contract.id
      }

      assignment_params = {
        assigned_contracts: [contract.id]
      }

      payload = work_params.merge(service_contract_params).merge(assignment_params)

      expect {
        post '/api/v1/works', params: payload, headers: jwt_auth_headers.merge({
          'Accept' => 'application/json'
        })
      }.to change(Work, :count).by(1)
        .and change(ServiceContract, :count).by(0) # No new service contract created

      expect(response).to have_http_status(:created)
      
      created_work = Work.last
      expect(created_work.title).to eq('Test Work with Existing Contract')
      expect(created_work.service_contract).to eq(existing_service_contract)
    end
  end

  context 'comparing with main controller (regression test)' do
    it 'main /works endpoint does NOT create service contracts' do
      work_params = {
        work: {
          title: 'Test Work via Main Controller',
          description: 'Work that should NOT create service contract',
          status: 'scheduled'
        }
      }

      service_contract_params = {
        service_contract_id: 'new',
        service_contract_title: 'Should Not Be Created',
        service_contract_description: 'This should be ignored'
      }

      assignment_params = {
        assigned_contracts: [contract.id]
      }

      payload = work_params.merge(service_contract_params).merge(assignment_params)

      expect {
        post '/works', params: payload, headers: jwt_auth_headers.merge({
          'Accept' => 'application/json'
        })
      }.to change(Work, :count).by(1)
        .and change(ServiceContract, :count).by(0) # No service contract created

      # Work created but without service contract
      created_work = Work.last
      expect(created_work.title).to eq('Test Work via Main Controller')
      expect(created_work.service_contract).to be_nil
    end
  end
end