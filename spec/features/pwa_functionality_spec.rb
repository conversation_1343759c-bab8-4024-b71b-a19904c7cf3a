# ABOUTME: PWA functionality tests covering Phase 1 & 2 implementation
# ABOUTME: Tests manifest.json, service worker registration, and basic offline capabilities

require 'rails_helper'

RSpec.feature 'PWA Functionality', type: :feature, js: true do
  let(:owner) { create(:user, :with_owner_role) }
  let(:company) { owner.companies.first }

  before do
    sign_in_as_owner(owner, company)
  end

  describe 'Phase 1: PWA Foundation' do
    context 'when PWA is enabled' do
      before do
        allow(FeatureFlags).to receive(:pwa_enabled?).and_return(true)
      end

      scenario 'manifest.json is accessible and valid' do
        visit '/manifest.json'
        
        expect(page.status_code).to eq(200)
        expect(page.response_headers['Content-Type']).to include('application/json')
        
        manifest = JSON.parse(page.body)
        expect(manifest['name']).to eq('Týmbox - Docházkový systém')
        expect(manifest['short_name']).to eq('Týmbox')
        expect(manifest['start_url']).to eq('/')
        expect(manifest['display']).to eq('standalone')
        expect(manifest['icons']).to be_present
        expect(manifest['icons'].length).to be >= 2
      end

      scenario 'PWA meta tags are present in SPA layout' do
        visit '/cs/dashboard'
        
        expect(page).to have_css('link[rel="manifest"][href="/manifest.json"]', visible: false)
        expect(page).to have_css('meta[name="application-name"][content="Týmbox"]', visible: false)
        expect(page).to have_css('meta[name="apple-mobile-web-app-capable"][content="yes"]', visible: false)
        expect(page).to have_css('link[rel="apple-touch-icon"]', visible: false)
      end

      scenario 'PWA icons are accessible' do
        visit '/icons/icon-192x192.png'
        expect(page.status_code).to eq(200)
        expect(page.response_headers['Content-Type']).to include('image/png')

        visit '/icons/icon-512x512.png'
        expect(page.status_code).to eq(200)
        expect(page.response_headers['Content-Type']).to include('image/png')
      end

      scenario 'service worker is registrable' do
        visit '/sw.js'
        expect(page.status_code).to eq(200)
        expect(page.response_headers['Content-Type']).to include('javascript')
        expect(page.body).to include('Service Worker')
      end

      scenario 'offline page is accessible' do
        visit '/offline.html'
        expect(page.status_code).to eq(200)
        expect(page).to have_content('Jste offline')
        expect(page).to have_content('Týmbox funguje i offline')
        expect(page).to have_button('Zkusit znovu')
      end
    end

    context 'when PWA is disabled' do
      before do
        allow(FeatureFlags).to receive(:pwa_enabled?).and_return(false)
      end

      scenario 'PWA meta tags are not present' do
        visit '/cs/dashboard'
        
        expect(page).not_to have_css('link[rel="manifest"]', visible: false)
        expect(page).not_to have_css('meta[name="application-name"]', visible: false)
        expect(page).not_to have_css('link[rel="apple-touch-icon"]', visible: false)
      end
    end
  end

  describe 'Phase 2: Service Worker Core' do
    context 'with JavaScript enabled' do
      before do
        allow(FeatureFlags).to receive(:pwa_enabled?).and_return(true)
        allow(FeatureFlags).to receive(:service_worker_enabled?).and_return(true)
      end

      scenario 'service worker registration is attempted in production-like environment' do
        # Mock HTTPS environment
        allow_any_instance_of(ActionDispatch::Request).to receive(:ssl?).and_return(true)
        
        visit '/cs/dashboard'
        
        # Check that service worker registration would be attempted
        # In a real test environment with HTTPS, we would check:
        # expect(page).to have_content('[PWA] Registering service worker')
        
        # For now, verify the registration code is present
        expect(page.html).to include('registerServiceWorker')
      end

      scenario 'feature flags control PWA functionality' do
        visit '/cs/dashboard'
        
        # Verify feature flags are checked
        expect(FeatureFlags.pwa_enabled?).to be_truthy
        expect(FeatureFlags.service_worker_enabled?).to be_truthy
        expect(FeatureFlags.offline_mode_enabled?).to be_truthy
      end
    end
  end

  describe 'Feature Flag Integration' do
    scenario 'PWA features respect feature flag settings' do
      # Test with PWA enabled
      allow(FeatureFlags).to receive(:pwa_enabled?).and_return(true)
      visit '/cs/dashboard'
      expect(page).to have_css('link[rel="manifest"]', visible: false)

      # Test with PWA disabled  
      allow(FeatureFlags).to receive(:pwa_enabled?).and_return(false)
      visit '/cs/dashboard'
      expect(page).not_to have_css('link[rel="manifest"]', visible: false)
    end
  end

  describe 'Error Handling' do
    scenario 'graceful degradation when service worker fails' do
      visit '/cs/dashboard'
      
      # App should still function normally even if SW registration fails
      expect(page).to have_content('Dashboard') # Or whatever indicates successful load
      
      # Check that error handling is present in the code
      expect(page.html).to include('Service worker registration failed')
    end
  end
end