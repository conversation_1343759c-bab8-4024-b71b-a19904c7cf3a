// ABOUTME: Playwright E2E tests for TYM-115 JWT tenant decoupling
// Tests frontend X-Company-ID header injection and company switching

const { test, expect } = require('@playwright/test');

test.describe('JWT Tenant Decoupling E2E', () => {
  let page;
  let context;
  
  test.beforeAll(async ({ browser }) => {
    context = await browser.newContext();
    page = await context.newPage();
  });
  
  test.afterAll(async () => {
    await context.close();
  });
  
  test.beforeEach(async () => {
    // Login as test user
    await page.goto('/cs/users/sign_in');
    await page.fill('input[name="user[email]"]', '<EMAIL>');
    await page.fill('input[name="user[password]"]', '123456');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForURL('**/dashboard');
  });
  
  test.describe('X-Company-ID Header Injection', () => {
    test('should include X-Company-ID header in API requests', async () => {
      // Monitor network requests
      const apiRequests = [];
      page.on('request', request => {
        if (request.url().includes('/api/v1/')) {
          apiRequests.push({
            url: request.url(),
            headers: request.headers()
          });
        }
      });
      
      // Navigate to a page that makes API calls
      await page.goto('/cs/works');
      await page.waitForLoadState('networkidle');
      
      // Check that API requests include X-Company-ID header
      const requestsWithTenant = apiRequests.filter(req => 
        !req.url.includes('/companies') && 
        !req.url.includes('/auth')
      );
      
      for (const request of requestsWithTenant) {
        expect(request.headers).toHaveProperty('x-company-id');
        console.log(`[TYM-115] Request ${request.url} has X-Company-ID: ${request.headers['x-company-id']}`);
      }
    });
    
    test('should NOT include X-Company-ID for tenant-less endpoints', async () => {
      const apiRequests = [];
      page.on('request', request => {
        if (request.url().includes('/api/v1/companies')) {
          apiRequests.push({
            url: request.url(),
            headers: request.headers()
          });
        }
      });
      
      // Navigate to companies page (tenant-less endpoint)
      await page.goto('/cs/companies');
      await page.waitForLoadState('networkidle');
      
      // Check that companies endpoint doesn't have X-Company-ID
      for (const request of apiRequests) {
        expect(request.headers).not.toHaveProperty('x-company-id');
        console.log(`[TYM-115] Tenant-less request ${request.url} has no X-Company-ID header`);
      }
    });
  });
  
  test.describe('Company Switching', () => {
    test('should switch company without page reload', async () => {
      // Go to companies page
      await page.goto('/cs/companies');
      await page.waitForSelector('.card');
      
      // Find a company that's not currently selected
      const inactiveCompany = await page.locator('.card:not(:has(.badge-success))').first();
      const companyName = await inactiveCompany.locator('h3').textContent();
      
      // Click switch button
      await inactiveCompany.locator('button:has-text("Vybrat tento")').click();
      
      // Should NOT reload the page (no navigation)
      expect(page.url()).toContain('/cs/companies');
      
      // The company should now be marked as current
      await expect(inactiveCompany.locator('.badge-success')).toBeVisible();
      await expect(inactiveCompany.locator('.badge-success')).toHaveText('Aktuální');
      
      console.log(`[TYM-115] Successfully switched to company: ${companyName}`);
    });
    
    test('should use new company context in subsequent API calls', async () => {
      // Switch company first
      await page.goto('/cs/companies');
      const inactiveCompany = await page.locator('.card:not(:has(.badge-success))').first();
      
      // Get the company ID from the element
      const companyElement = await inactiveCompany.evaluate(el => {
        // Extract company ID from ref attribute or data attribute
        const match = el.className.match(/company-(\d+)/);
        return match ? match[1] : null;
      });
      
      // Switch to this company
      await inactiveCompany.locator('button:has-text("Vybrat tento")').click();
      
      // Monitor next API request
      const nextApiRequest = page.waitForRequest(request => 
        request.url().includes('/api/v1/works')
      );
      
      // Navigate to works page
      await page.goto('/cs/works');
      
      // Check the API request has the new company ID
      const request = await nextApiRequest;
      const headers = request.headers();
      
      expect(headers).toHaveProperty('x-company-id');
      console.log(`[TYM-115] After switch, X-Company-ID is: ${headers['x-company-id']}`);
    });
    
    test('should persist company selection across page refreshes', async () => {
      // Switch company
      await page.goto('/cs/companies');
      const targetCompany = await page.locator('.card:not(:has(.badge-success))').first();
      const companyName = await targetCompany.locator('h3').textContent();
      
      await targetCompany.locator('button:has-text("Vybrat tento")').click();
      await expect(targetCompany.locator('.badge-success')).toBeVisible();
      
      // Refresh the page
      await page.reload();
      await page.waitForSelector('.card');
      
      // The same company should still be selected
      const selectedCompany = await page.locator('.card:has(.badge-success)');
      const selectedName = await selectedCompany.locator('h3').textContent();
      
      expect(selectedName).toBe(companyName);
      console.log(`[TYM-115] Company selection persisted after refresh: ${companyName}`);
    });
  });
  
  test.describe('Error Handling', () => {
    test('should show error when accessing tenant-required endpoint without context', async () => {
      // Clear localStorage to remove selectedCompanyId
      await page.evaluate(() => {
        localStorage.removeItem('selectedCompanyId');
      });
      
      // Try to access a tenant-required endpoint
      await page.goto('/cs/works');
      
      // Should show an error message
      await expect(page.locator('.flash-message.error')).toBeVisible();
      const errorText = await page.locator('.flash-message.error').textContent();
      
      expect(errorText).toContain('Company context required');
      console.log('[TYM-115] Error shown for missing company context');
    });
    
    test('should handle unauthorized company access gracefully', async () => {
      // Try to set an invalid company ID in localStorage
      await page.evaluate(() => {
        localStorage.setItem('selectedCompanyId', '999999');
      });
      
      // Try to access a page
      await page.goto('/cs/works');
      
      // Should show authorization error
      await expect(page.locator('.flash-message.error')).toBeVisible();
      console.log('[TYM-115] Unauthorized company access handled correctly');
    });
  });
  
  test.describe('Performance', () => {
    test('company switch should be faster without JWT regeneration', async () => {
      await page.goto('/cs/companies');
      await page.waitForSelector('.card');
      
      const startTime = Date.now();
      
      // Switch company
      const inactiveCompany = await page.locator('.card:not(:has(.badge-success))').first();
      await inactiveCompany.locator('button:has-text("Vybrat tento")').click();
      
      // Wait for the badge to appear (indicating switch complete)
      await expect(inactiveCompany.locator('.badge-success')).toBeVisible();
      
      const switchTime = Date.now() - startTime;
      
      // Should be much faster than 500ms (old JWT regeneration time)
      expect(switchTime).toBeLessThan(200);
      console.log(`[TYM-115] Company switch completed in ${switchTime}ms`);
    });
  });
});