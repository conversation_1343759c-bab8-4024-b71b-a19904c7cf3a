# ABOUTME: Tests for contract disconnection issues - UI display, validation, and unauthorized reconnection
# ABOUTME: Ensures terminated contracts are handled correctly regarding email validation, UI display, and role updates

require 'rails_helper'

RSpec.describe 'Contract Disconnection Issues', type: :feature do
  let(:company) { create(:company) }
  let(:owner) { create(:user) }
  let(:employee_user) { create(:user, email: '<EMAIL>') }
  let!(:owner_role) { Role.find_or_create_by(name: 'owner') }
  let!(:employee_role) { Role.find_or_create_by(name: 'employee') }
  
  before do
    ActsAsTenant.current_tenant = company
    CompanyUserRole.create!(user: owner, company: company, role: owner_role)
  end

  describe 'Issue 1: Email Validation Prevents Re-invitation' do
    context 'when a contract is terminated' do
      let!(:terminated_contract) do
        create(:contract, 
          company: company, 
          email: '<EMAIL>',
          status: 'terminated'
        )
      end

      it 'allows creating a new contract with the same email' do
        # This should not raise a validation error
        new_contract = Contract.new(
          company: company,
          email: '<EMAIL>',
          first_name: '<PERSON>',
          last_name: 'Do<PERSON>',
          status: 'active'
        )
        
        expect(new_contract).to be_valid
        expect(new_contract.save).to be true
      end

      it 'prevents duplicate active contracts with same email' do
        # Create an active contract
        active_contract = create(:contract, 
          company: company, 
          email: '<EMAIL>',
          status: 'active'
        )

        # Try to create another active contract with same email
        duplicate_contract = Contract.new(
          company: company,
          email: '<EMAIL>',
          first_name: 'Jane',
          last_name: 'Smith',
          status: 'active'
        )
        
        expect(duplicate_contract).not_to be_valid
        expect(duplicate_contract.errors[:email]).to include('již databáze obsahuje')
      end
    end
  end

  describe 'Issue 2: UI Shows Incorrect Connection Status', js: true do
    let!(:contract) { create(:contract, company: company, user: employee_user, status: 'active') }
    
    before do
      # Set up authentication for the owner
      sign_in_as(owner)
    end

    it 'shows disconnected status for terminated contracts' do
      # Terminate the contract
      contract.update!(status: 'terminated')
      
      visit "/companies/#{company.id}/contracts/#{contract.id}"
      
      # Should show "Odpojen" (Disconnected) in red
      expect(page).to have_css('.text-red-600', text: 'Odpojen')
      expect(page).not_to have_css('.text-green-600', text: 'Připojen')
    end

    it 'hides role dropdown for terminated contracts' do
      # Terminate the contract
      contract.update!(status: 'terminated')
      
      visit "/companies/#{company.id}/contracts/#{contract.id}"
      
      # Role dropdown should not be visible
      expect(page).not_to have_css('select.form-select')
      expect(page).not_to have_text('Role')
    end

    it 'shows connected status for active contracts with user' do
      visit "/companies/#{company.id}/contracts/#{contract.id}"
      
      # Should show "Připojen" (Connected) in green
      expect(page).to have_css('.text-green-600', text: 'Připojen')
      
      # Role dropdown should be visible for owner
      expect(page).to have_css('select.form-select')
    end
  end

  describe 'Issue 3: Unauthorized Reconnection via Role Selection' do
    let!(:contract) { create(:contract, company: company, user: employee_user) }
    
    before do
      sign_in_as(owner)
    end

    it 'prevents role updates on terminated contracts' do
      # Terminate the contract
      contract.update!(status: 'terminated')
      
      # Try to update role via API
      patch "/companies/#{company.id}/contracts/#{contract.id}/update_role",
        params: { role_name: 'employee' },
        headers: { 'Accept' => 'application/json' }
      
      expect(response).to have_http_status(:unprocessable_entity)
      
      json = JSON.parse(response.body)
      expect(json['success']).to be false
      expect(json['message']).to eq('Nelze upravit ukončený kontrakt.')
      
      # Verify no CompanyUserRole was created
      expect(CompanyUserRole.where(user: employee_user, company: company).count).to eq(0)
    end

    it 'allows role updates on active contracts' do
      # Ensure contract is active
      contract.update!(status: 'active')
      
      # Update role via API
      patch "/companies/#{company.id}/contracts/#{contract.id}/update_role",
        params: { role_name: 'employee' },
        headers: { 'Accept' => 'application/json' }
      
      expect(response).to have_http_status(:ok)
      
      json = JSON.parse(response.body)
      expect(json['success']).to be true
      expect(json['message']).to eq('Role byla aktualizována.')
      
      # Verify CompanyUserRole was created
      company_user_role = CompanyUserRole.find_by(user: employee_user, company: company)
      expect(company_user_role).to be_present
      expect(company_user_role.role.name).to eq('employee')
    end
  end
end