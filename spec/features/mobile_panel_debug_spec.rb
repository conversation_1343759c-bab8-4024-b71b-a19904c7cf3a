# ABOUTME: Debug test for MobileUnscheduledPanel visibility in Plus subscription
# ABOUTME: Simplified test to verify the mobile panel appears correctly

require 'rails_helper'

RSpec.describe 'Mobile Panel Debug', type: :feature, js: true do
  include ClaudeTestHelpers

  let(:user) { User.find_or_create_by(email: '<EMAIL>') { |u| u.password = '123456'; u.confirmed_at = Time.current } }
  let(:company) { create(:company) }
  let(:owner_role) { create(:role, name: 'owner') }
  
  before do
    ActsAsTenant.current_tenant = company
    
    # Create company user role for test user (find or create to avoid duplicates)
    CompanyUserRole.find_or_create_by(user: user, company: company) do |cur|
      cur.role = owner_role
      cur.is_primary = true
      cur.active = true
    end
    
    # Create Plus subscription for company
    plan = create(:plan, :plus)
    create(:subscription, company: company, plan: plan, status: 'active')
    
    sign_in_test_user
    
    # Create some unscheduled works for testing
    @work1 = create(:work, company: company, title: 'Test Unscheduled Work 1', scheduled_start_date: nil)
    @work2 = create(:work, company: company, title: 'Test Unscheduled Work 2', scheduled_start_date: nil)
  end

  it 'shows mobile panel for Plus users on mobile viewport', :mobile do
    # Navigate to calendar
    navigate_to('calendar')
    wait_for_vite_assets
    
    # Debug: Print page source to see what's rendered
    puts "=== PAGE DEBUG ==="
    puts "Current URL: #{current_url}"
    puts "Page title: #{page.title}"
    
    # Check for NewMonthlyCalendar component (should be present for Plus users)
    if page.has_css?('.new-calendar-layout')
      puts "✅ NewMonthlyCalendar is present"
    else
      puts "❌ NewMonthlyCalendar is NOT present"
    end
    
    # Check for MobileUnscheduledPanel component
    if page.has_css('[data-vue-component="mobile-unscheduled-panel"]')
      puts "✅ MobileUnscheduledPanel is present"
    else
      puts "❌ MobileUnscheduledPanel is NOT present"
    end
    
    # Check mobile view detection (simulate mobile viewport)
    page.execute_script("window.resizeTo(360, 640)")
    sleep(1)
    
    # Check if isMobileView is true
    is_mobile = page.evaluate_script("window.innerWidth <= 768")
    puts "Mobile viewport detected: #{is_mobile}"
    
    # Force mobile view for testing
    page.execute_script(<<~JS)
      // Find Vue instance and force mobile view
      const calendarEl = document.querySelector('.new-calendar-layout');
      if (calendarEl && calendarEl.__vue__) {
        calendarEl.__vue__.isMobileView = true;
        console.log('Forced mobile view to true');
      }
    JS
    
    sleep(1)
    
    # Now check again for mobile panel
    if page.has_css('[data-vue-component="mobile-unscheduled-panel"]')
      puts "✅ MobileUnscheduledPanel is NOW visible after forcing mobile view"
    else
      puts "❌ MobileUnscheduledPanel is STILL NOT visible"
    end
    
    # Take a screenshot for debugging
    screenshot_for_claude('mobile_panel_debug')
    
    puts "==================="
    
    # Basic assertion that should pass
    expect(page).to have_css('.new-calendar-layout')
  end
end