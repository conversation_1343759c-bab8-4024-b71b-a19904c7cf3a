# ABOUTME: Integration test to verify that when an employee leaves a company, the owner doesn't get logged out
# ABOUTME: This tests the critical bug fix where leaving employees were removing is_primary flag from all company users

require 'rails_helper'

RSpec.describe "Employee leaving company doesn't affect owner", type: :feature do
  let(:company) { create(:company, name: "Test Company") }
  let(:owner_user) { create(:user, email: '<EMAIL>', password: '123456') }
  let(:employee_user) { create(:user, email: '<EMAIL>', password: '123456') }
  
  before do
    # Setup roles
    owner_role = Role.find_or_create_by(name: 'owner')
    employee_role = Role.find_or_create_by(name: 'employee')
    
    # Create owner's company relationship
    CompanyUserRole.create!(
      user: owner_user,
      company: company,
      role: owner_role,
      active: true,
      is_primary: true
    )
    
    # Create employee's company relationship
    CompanyUserRole.create!(
      user: employee_user,
      company: company,
      role: employee_role,
      active: true,
      is_primary: true
    )
    
    # Create contracts for both
    create(:contract, user: owner_user, company: company, status: 'active')
    create(:contract, user: employee_user, company: company, status: 'active')
  end
  
  it "owner's primary company relationship remains intact when employee leaves" do
    # Verify initial state
    expect(CompanyUserRole.where(company: company, active: true).count).to eq(2)
    owner_role = CompanyUserRole.find_by(user: owner_user, company: company)
    expect(owner_role.is_primary).to be true
    expect(owner_role.active).to be true
    
    # Employee leaves the company
    result = employee_user.leave_company(company)
    expect(result).to be true
    
    # Check owner's role is still intact
    owner_role.reload
    expect(owner_role.is_primary).to be true
    expect(owner_role.active).to be true
    
    # Verify only one active role remains (the owner's)
    expect(CompanyUserRole.where(company: company, active: true).count).to eq(1)
    
    # Employee's role should be deactivated
    employee_role = CompanyUserRole.unscoped.find_by(user: employee_user, company: company)
    expect(employee_role.active).to be false
    expect(employee_role.is_primary).to be false
    
    # Most importantly: owner can still access their primary company
    owner_user.reload
    expect(owner_user.primary_company).to eq(company)
    expect(owner_user.company_user_roles.where(company: company, active: true).exists?).to be true
  end
  
  it "multiple employees leaving don't affect each other or the owner" do
    # Add another employee
    employee2 = create(:user, email: '<EMAIL>')
    employee_role = Role.find_by(name: 'employee')
    CompanyUserRole.create!(
      user: employee2,
      company: company,
      role: employee_role,
      active: true,
      is_primary: true
    )
    create(:contract, user: employee2, company: company, status: 'active')
    
    # First employee leaves
    employee_user.leave_company(company)
    
    # Check everyone else is unaffected
    expect(CompanyUserRole.find_by(user: owner_user, company: company).is_primary).to be true
    expect(CompanyUserRole.find_by(user: employee2, company: company).is_primary).to be true
    
    # Second employee leaves
    employee2.leave_company(company)
    
    # Owner should still be unaffected
    owner_role = CompanyUserRole.find_by(user: owner_user, company: company)
    expect(owner_role.is_primary).to be true
    expect(owner_role.active).to be true
  end
end