# ABOUTME: Tests for owner_work_summary API endpoint in DailyLogsController
# ABOUTME: Validates authorization and response format for company-wide work summaries

require 'rails_helper'

RSpec.describe 'Owner Work Summary API', type: :request do
  let(:company) { create(:company) }
  let(:owner_user) { create(:user) }
  let(:regular_user) { create(:user) }
  
  let(:owner_jwt_token) do
    post '/api/v1/auth/jwt_login', params: {
      email: owner_user.email,
      password: owner_user.password
    }
    JSON.parse(response.body)['access_token']
  end
  
  let(:regular_jwt_token) do
    post '/api/v1/auth/jwt_login', params: {
      email: regular_user.email,
      password: regular_user.password
    }
    JSON.parse(response.body)['access_token']
  end
  
  before do
    # Create owner role
    owner_role = create(:role, name: 'owner')
    regular_role = create(:role, name: 'employee')
    
    # Assign roles
    create(:company_user_role, company: company, user: owner_user, role: owner_role)
    create(:company_user_role, company: company, user: regular_user, role: regular_role)
    
    # Create contracts
    create(:contract, company: company, user: owner_user)
    create(:contract, company: company, user: regular_user)
    
    ActsAsTenant.current_tenant = company
  end

  describe 'GET /api/v1/daily_logs/owner_work_summary' do
    context 'when user is owner' do
      let(:headers) { { 'Authorization' => "Bearer #{owner_jwt_token}" } }

      it 'returns successful response' do
        get '/api/v1/daily_logs/owner_work_summary', params: { year: 2025, month: 1 }, headers: headers
        expect(response).to have_http_status(:success)
      end

      it 'returns proper JSON structure' do
        get '/api/v1/daily_logs/owner_work_summary', params: { year: 2025, month: 1 }, headers: headers
        json = JSON.parse(response.body)
        
        expect(json).to have_key('summary')
        expect(json).to have_key('selected_date')
        expect(json).to have_key('company_name')
        expect(json['selected_date']).to eq({ 'year' => 2025, 'month' => 1 })
      end

      it 'defaults to current month when no params given' do
        get '/api/v1/daily_logs/owner_work_summary', headers: headers
        json = JSON.parse(response.body)
        
        expect(json['selected_date']['year']).to eq(Date.current.year)
        expect(json['selected_date']['month']).to eq(Date.current.month)
      end
    end

    context 'when user is not owner/admin/supervisor' do
      let(:headers) { { 'Authorization' => "Bearer #{regular_jwt_token}" } }

      it 'returns forbidden status' do
        get '/api/v1/daily_logs/owner_work_summary', params: { year: 2025, month: 1 }, headers: headers
        expect(response).to have_http_status(:forbidden)
      end
    end

    context 'with invalid date parameters' do
      let(:headers) { { 'Authorization' => "Bearer #{owner_jwt_token}" } }

      it 'returns error for invalid year' do
        get '/api/v1/daily_logs/owner_work_summary', params: { year: 1999, month: 1 }, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns error for invalid month' do
        get '/api/v1/daily_logs/owner_work_summary', params: { year: 2025, month: 13 }, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context 'without authentication' do
      it 'returns unauthorized' do
        get '/api/v1/daily_logs/owner_work_summary', params: { year: 2025, month: 1 }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end