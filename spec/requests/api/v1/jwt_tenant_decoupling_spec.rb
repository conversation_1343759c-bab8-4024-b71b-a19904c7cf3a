# ABOUTME: RSpec tests for TYM-115 JWT tenant decoupling with dual-mode support
# Tests both X-Company-ID header (new) and JWT payload company_id (legacy) flows

require 'rails_helper'

RSpec.describe "JWT Tenant Decoupling", type: :request do
  let(:user) { create(:user) }
  let(:company1) { create(:company) }
  let(:company2) { create(:company) }
  let(:unauthorized_company) { create(:company) }
  let(:owner_role) { Role.find_or_create_by!(name: 'owner') }
  let(:employee_role) { Role.find_or_create_by!(name: 'employee') }
  
  before do
    # Set up user with roles in two companies
    create(:company_user_role, user: user, company: company1, role: owner_role, is_primary: true)
    create(:company_user_role, user: user, company: company2, role: employee_role, is_primary: false)
    user.set_primary_company(company1)
  end
  
  describe "Dual-mode tenant context support" do
    context "with new X-Company-ID header (preferred flow)" do
      let(:jwt_payload) { user.jwt_payload }
      let(:token) { JwtService.encode_access_token(jwt_payload) }
      let(:headers) do
        {
          'Authorization' => "Bearer #{token}",
          'X-Company-ID' => company1.id.to_s,
          'Accept' => 'application/json'
        }
      end
      
      it "sets tenant context from X-Company-ID header" do
        get '/api/v1/user', headers: headers
        
        expect(response).to have_http_status(:success)
        data = JSON.parse(response.body)
        expect(data['company']['id']).to eq(company1.id)
      end
      
      it "allows switching company via header without new JWT" do
        # First request with company1
        get '/api/v1/user', headers: headers
        expect(response).to have_http_status(:success)
        data1 = JSON.parse(response.body)
        expect(data1['company']['id']).to eq(company1.id)
        
        # Switch to company2 using same JWT but different header
        headers['X-Company-ID'] = company2.id.to_s
        get '/api/v1/user', headers: headers
        
        expect(response).to have_http_status(:success)
        data2 = JSON.parse(response.body)
        expect(data2['company']['id']).to eq(company2.id)
      end
      
      it "validates user has access to company in header" do
        headers['X-Company-ID'] = unauthorized_company.id.to_s
        
        get '/api/v1/user', headers: headers
        
        expect(response).to have_http_status(:forbidden)
        expect(JSON.parse(response.body)['error']).to include('Company context required')
      end
      
      it "handles missing X-Company-ID for tenant-required endpoints" do
        headers.delete('X-Company-ID')
        
        # Assuming /api/v1/works requires tenant context
        get '/api/v1/works', headers: headers
        
        expect(response).to have_http_status(:forbidden)
        expect(JSON.parse(response.body)['error']).to include('Company context required')
      end
    end
    
    context "with legacy JWT payload company_id (backward compatibility)" do
      before do
        # Enable legacy mode for testing
        ENV['JWT_INCLUDE_COMPANY_ID'] = 'true'
      end
      
      after do
        ENV.delete('JWT_INCLUDE_COMPANY_ID')
      end
      
      let(:jwt_payload) { user.jwt_payload.merge(company_id: company1.id) }
      let(:token) { JwtService.encode_access_token(jwt_payload) }
      let(:headers) do
        {
          'Authorization' => "Bearer #{token}",
          'Accept' => 'application/json'
        }
      end
      
      it "falls back to company_id from JWT when no header present" do
        get '/api/v1/user', headers: headers
        
        expect(response).to have_http_status(:success)
        data = JSON.parse(response.body)
        expect(data['company']['id']).to eq(company1.id)
      end
      
      it "prioritizes X-Company-ID header over JWT payload" do
        headers['X-Company-ID'] = company2.id.to_s
        
        get '/api/v1/user', headers: headers
        
        expect(response).to have_http_status(:success)
        # Should use header value, not JWT payload
        data = JSON.parse(response.body)
        expect(data['company']['id']).to eq(company2.id)
      end
    end
    
    context "with spoofed X-Company-ID header (security test)" do
      let(:jwt_payload) { user.jwt_payload }
      let(:token) { JwtService.encode_access_token(jwt_payload) }
      let(:malicious_user) { create(:user) }
      let(:malicious_token) { JwtService.encode_access_token(malicious_user.jwt_payload) }
      
      it "prevents access to unauthorized companies" do
        headers = {
          'Authorization' => "Bearer #{malicious_token}",
          'X-Company-ID' => company1.id.to_s,  # Company the malicious user doesn't have access to
          'Accept' => 'application/json'
        }
        
        get '/api/v1/user', headers: headers
        
        expect(response).to have_http_status(:forbidden)
        expect(JSON.parse(response.body)['error']).to include('Company context required')
      end
    end
    
    context "without company context (tenant-less operations)" do
      let(:jwt_payload) { user.jwt_payload }
      let(:token) { JwtService.encode_access_token(jwt_payload) }
      let(:headers) do
        {
          'Authorization' => "Bearer #{token}",
          'Accept' => 'application/json'
        }
      end
      
      it "allows tenant-less endpoints without X-Company-ID" do
        # Companies index doesn't require tenant context
        get '/api/v1/companies', headers: headers
        
        expect(response).to have_http_status(:success)
        companies_data = JSON.parse(response.body)
        expect(companies_data['company_user_roles'].length).to eq(2)
      end
      
      it "blocks tenant-required endpoints without context" do
        # Works endpoint requires tenant context
        get '/api/v1/works', headers: headers
        
        expect(response).to have_http_status(:forbidden)
        expect(JSON.parse(response.body)['message']).to include('X-Company-ID header')
      end
    end
  end
  
  describe "Company switching endpoint" do
    let(:jwt_payload) { user.jwt_payload }
    let(:token) { JwtService.encode_access_token(jwt_payload) }
    let(:headers) do
      {
        'Authorization' => "Bearer #{token}",
        'Accept' => 'application/json',
        'Content-Type' => 'application/json'
      }
    end
    
    it "does not generate new JWT token on company switch" do
      post '/api/v1/companies/switch_company', 
           params: { company_id: company2.id }.to_json,
           headers: headers
      
      expect(response).to have_http_status(:success)
      response_data = JSON.parse(response.body)
      
      # Should NOT return a new access_token
      expect(response_data).not_to have_key('access_token')
      
      # Should return company info and instructions
      expect(response_data['success']).to be true
      expect(response_data['company']['id']).to eq(company2.id)
      expect(response_data['header_required']).to be true
      expect(response_data['header_name']).to eq('X-Company-ID')
    end
    
    it "validates user has access to target company" do
      post '/api/v1/companies/switch_company',
           params: { company_id: unauthorized_company.id }.to_json,
           headers: headers
      
      expect(response).to have_http_status(:forbidden)
      expect(JSON.parse(response.body)['error']).to include('not found or access denied')
    end
  end
  
  describe "Performance and security" do
    let(:jwt_payload) { user.jwt_payload }
    let(:token) { JwtService.encode_access_token(jwt_payload) }
    
    it "handles rapid company switching efficiently" do
      headers = {
        'Authorization' => "Bearer #{token}",
        'Accept' => 'application/json'
      }
      
      # Rapid switching between companies
      10.times do |i|
        company_id = i.even? ? company1.id : company2.id
        headers['X-Company-ID'] = company_id.to_s
        
        get '/api/v1/user', headers: headers
        expect(response).to have_http_status(:success)
      end
    end
    
    it "logs security events for invalid company access attempts" do
      expect(Rails.logger).to receive(:warn).with(/User.*attempted to access company.*without authorization/)
      
      headers = {
        'Authorization' => "Bearer #{token}",
        'X-Company-ID' => unauthorized_company.id.to_s,
        'Accept' => 'application/json'
      }
      
      get '/api/v1/user', headers: headers
      
      expect(response).to have_http_status(:forbidden)
    end
  end
end