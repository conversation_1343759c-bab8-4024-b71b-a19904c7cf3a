# ABOUTME: RSpec tests for the API v1 meetings controller following TDD principles
# ABOUTME: Tests JWT authentication, authorization, and all CRUD + custom actions for meetings
require 'rails_helper'

RSpec.describe 'Api::V1::Meetings', type: :request do
  let(:company) { create(:company) }
  let(:user) { create(:user) }
  let(:role) { create(:role, name: 'owner') }
  let(:plus_plan) { create(:plan, :plus) }
  let!(:subscription) { create(:subscription, company: company, plan: plus_plan, status: 'active') }
  let!(:company_user_role) { create(:company_user_role, user: user, company: company, role: role) }
  let(:contract) { create(:contract, :with_user, company: company, user: user) }
  let!(:meeting) { create(:meeting, company: company, created_by: user) }
  
  before do
    ActsAsTenant.current_tenant = company
  end

  def jwt_auth_headers(test_user = user)
    payload = {
      user_id: test_user.id,
      email: test_user.email,
      company_id: company.id
    }
    
    token = JwtService.encode(payload, 1.hour.from_now)
    auth_headers(token)
  end

  describe 'GET /api/v1/meetings' do
    it 'returns all meetings for the company' do
      get '/api/v1/meetings', headers: jwt_auth_headers
      
      expect(response).to have_http_status(:ok)
      expect(json_response).to be_an(Array)
      expect(json_response.length).to eq(1)
      expect(json_response.first['id']).to eq(meeting.id)
    end
    
    it 'requires authentication' do
      get '/api/v1/meetings', headers: { 'Accept' => 'application/json' }
      
      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'GET /api/v1/meetings/:id' do
    it 'returns a specific meeting' do
      get "/api/v1/meetings/#{meeting.id}", headers: jwt_auth_headers
      
      expect(response).to have_http_status(:ok)
      expect(json_response['id']).to eq(meeting.id)
      expect(json_response['title']).to eq(meeting.title)
    end
    
    it 'returns 404 for non-existent meeting' do
      get '/api/v1/meetings/999999', headers: jwt_auth_headers
      
      expect(response).to have_http_status(:not_found)
    end
  end

  describe 'POST /api/v1/meetings' do
    let(:valid_attributes) do
      {
        meeting: {
          title: 'Test Meeting',
          description: 'Test Description',
          place: 'Test Location',
          day_options: { '2025-07-21' => true, '2025-07-22' => true },
          contract_ids: [contract.id],
          additional_emails: ['<EMAIL>']
        }
      }
    end

    it 'creates a new meeting' do
      expect {
        post '/api/v1/meetings', params: valid_attributes, headers: jwt_auth_headers
      }.to change(Meeting, :count).by(1)
      
      expect(response).to have_http_status(:created)
      expect(json_response['success']).to be(true)
      expect(json_response['meeting']['title']).to eq('Test Meeting')
    end
    
    it 'returns errors for invalid attributes' do
      invalid_attributes = { meeting: { title: '' } }
      
      post '/api/v1/meetings', params: invalid_attributes, headers: jwt_auth_headers
      
      expect(response).to have_http_status(:unprocessable_entity)
      expect(json_response['success']).to be(false)
      expect(json_response['errors']).to be_present
    end
  end

  describe 'PATCH /api/v1/meetings/:id' do
    it 'updates the meeting' do
      patch "/api/v1/meetings/#{meeting.id}", 
            params: { meeting: { title: 'Updated Title' } }, 
            headers: jwt_auth_headers
      
      expect(response).to have_http_status(:ok)
      expect(json_response['success']).to be(true)
      expect(json_response['meeting']['title']).to eq('Updated Title')
    end
  end

  describe 'DELETE /api/v1/meetings/:id' do
    it 'deletes the meeting' do
      expect {
        delete "/api/v1/meetings/#{meeting.id}", headers: jwt_auth_headers
      }.to change(Meeting, :count).by(-1)
      
      expect(response).to have_http_status(:ok)
      expect(json_response['success']).to be(true)
    end
  end

  # Custom actions tests
  describe 'GET /api/v1/meetings/conflicts' do
    it 'returns conflicts for given parameters' do
      get '/api/v1/meetings/conflicts', 
          params: { 
            contract_ids: [contract.id], 
            start_date: '2025-07-21', 
            end_date: '2025-07-22' 
          }, 
          headers: jwt_auth_headers
      
      expect(response).to have_http_status(:ok)
      expect(json_response).to have_key('events')
      expect(json_response).to have_key('works')
      expect(json_response).to have_key('meetings')
      expect(json_response).to have_key('holidays')
    end

    context 'when non-vacation events exist' do
      let!(:illness_event) { create(:event, :illness, contract: contract, company: company, start_time: '2025-07-21 09:00:00', end_time: '2025-07-21 17:00:00', status: nil) }
      let!(:day_care_event) { create(:event, :day_care, contract: contract, company: company, start_time: '2025-07-22 08:00:00', end_time: '2025-07-22 16:00:00', status: nil) }
      let!(:family_sick_event) { create(:event, :family_sick, contract: contract, company: company, start_time: '2025-07-21 10:00:00', end_time: '2025-07-21 14:00:00', status: nil) }
      let!(:rejected_event) { create(:event, :other, contract: contract, company: company, start_time: '2025-07-21 11:00:00', end_time: '2025-07-21 15:00:00', status: 'rejected') }

      it 'includes illness events in conflict detection' do
        get '/api/v1/meetings/conflicts', 
            params: { 
              contract_ids: [contract.id], 
              start_date: '2025-07-21', 
              end_date: '2025-07-22' 
            }, 
            headers: jwt_auth_headers
        
        expect(response).to have_http_status(:ok)
        events = json_response['events']
        illness_event_found = events.any? { |event| event['id'] == illness_event.id }
        expect(illness_event_found).to be(true), "Illness event should be included in conflict detection"
      end

      it 'includes day_care events in conflict detection' do
        get '/api/v1/meetings/conflicts', 
            params: { 
              contract_ids: [contract.id], 
              start_date: '2025-07-21', 
              end_date: '2025-07-22' 
            }, 
            headers: jwt_auth_headers
        
        expect(response).to have_http_status(:ok)
        events = json_response['events']
        day_care_event_found = events.any? { |event| event['id'] == day_care_event.id }
        expect(day_care_event_found).to be(true), "Day care event should be included in conflict detection"
      end

      it 'includes family_sick events in conflict detection' do
        get '/api/v1/meetings/conflicts', 
            params: { 
              contract_ids: [contract.id], 
              start_date: '2025-07-21', 
              end_date: '2025-07-22' 
            }, 
            headers: jwt_auth_headers
        
        expect(response).to have_http_status(:ok)
        events = json_response['events']
        family_sick_event_found = events.any? { |event| event['id'] == family_sick_event.id }
        expect(family_sick_event_found).to be(true), "Family sick event should be included in conflict detection"
      end

      it 'excludes rejected events from conflict detection' do
        get '/api/v1/meetings/conflicts', 
            params: { 
              contract_ids: [contract.id], 
              start_date: '2025-07-21', 
              end_date: '2025-07-22' 
            }, 
            headers: jwt_auth_headers
        
        expect(response).to have_http_status(:ok)
        events = json_response['events']
        rejected_event_found = events.any? { |event| event['id'] == rejected_event.id }
        expect(rejected_event_found).to be(false), "Rejected events should not be included in conflict detection"
      end

      it 'includes all non-rejected event types in conflict detection' do
        get '/api/v1/meetings/conflicts', 
            params: { 
              contract_ids: [contract.id], 
              start_date: '2025-07-21', 
              end_date: '2025-07-22' 
            }, 
            headers: jwt_auth_headers
        
        expect(response).to have_http_status(:ok)
        events = json_response['events']
        
        # Should include illness, day_care, family_sick events
        expected_event_ids = [illness_event.id, day_care_event.id, family_sick_event.id]
        actual_event_ids = events.map { |event| event['id'] }
        
        expected_event_ids.each do |event_id|
          expect(actual_event_ids).to include(event_id), "Event with ID #{event_id} should be included in conflict detection"
        end
        
        # Should not include rejected event
        expect(actual_event_ids).not_to include(rejected_event.id), "Rejected event should not be included in conflict detection"
      end
    end

    context 'when vacation events exist' do
      let!(:vacation_event) { create(:event, :vacation, contract: contract, company: company, start_time: '2025-07-21 09:00:00', end_time: '2025-07-21 17:00:00', status: nil) }

      it 'includes vacation events in conflict detection' do
        get '/api/v1/meetings/conflicts', 
            params: { 
              contract_ids: [contract.id], 
              start_date: '2025-07-21', 
              end_date: '2025-07-22' 
            }, 
            headers: jwt_auth_headers
        
        expect(response).to have_http_status(:ok)
        events = json_response['events']
        vacation_event_found = events.any? { |event| event['id'] == vacation_event.id }
        expect(vacation_event_found).to be(true), "Vacation event should be included in conflict detection"
      end
    end
  end

  describe 'POST /api/v1/meetings/:id/resend_invitation' do
    let!(:meeting_user) { create(:meeting_user, meeting: meeting, user: user) }
    
    it 'resends invitation to meeting user' do
      post "/api/v1/meetings/#{meeting.id}/resend_invitation",
           params: { meeting_user_id: meeting_user.id },
           headers: jwt_auth_headers
      
      expect(response).to have_http_status(:ok)
      expect(json_response['success']).to be(true)
    end
  end

  describe 'POST /api/v1/meetings/:id/extend_meeting_dates' do
    it 'extends meeting dates' do
      post "/api/v1/meetings/#{meeting.id}/extend_meeting_dates",
           params: { days_forward: 7, days_backward: 3 },
           headers: jwt_auth_headers
      
      expect(response).to have_http_status(:ok)
      expect(json_response['success']).to be(true)
    end
  end

  describe 'POST /api/v1/meetings/:id/confirm' do
    it 'confirms meeting with specific date' do
      post "/api/v1/meetings/#{meeting.id}/confirm",
           params: { confirmed_date: '2025-07-21' },
           headers: jwt_auth_headers
      
      expect(response).to have_http_status(:ok)
      expect(json_response['success']).to be(true)
    end
  end

end