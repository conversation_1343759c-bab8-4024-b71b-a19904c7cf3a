require 'rails_helper'

RSpec.describe "Api::V1::Contracts", type: :request do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let(:role) { create(:role, name: 'employee') }
  let(:user_role) { create(:company_user_role, user: user, company: company, role: role) }
  let(:contract) { create(:contract, user: user, company: company, status: :active) }
  
  # Create some colleagues
  let(:colleague1) { create(:user, email: '<EMAIL>') }
  let(:colleague2) { create(:user, email: '<EMAIL>') }
  let(:colleague3) { create(:user, email: '<EMAIL>') }
  
  let!(:contract1) do
    create(:contract, 
      user: colleague1, 
      company: company, 
      status: :active,
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      email: colleague1.email,
      job_title: 'Developer'
    )
  end
  
  let!(:contract2) do
    create(:contract, 
      user: colleague2, 
      company: company, 
      status: :active,
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      email: colleague2.email,
      job_title: 'Designer'
    )
  end
  
  let!(:contract3) do
    create(:contract, 
      user: colleague3, 
      company: company, 
      status: :terminated,
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      email: colleague3.email,
      job_title: 'Manager'
    )
  end
  
  # Setup JWT authentication
  before do
    user_role # Ensure user has role in company
    contract # Ensure user has contract
  end
  
  describe "GET /api/v1/contracts/colleagues" do
    context "with valid authentication and authorization" do
      context "without include_self parameter" do
        it "returns list of active colleagues excluding current user" do
          token = jwt_token_for(user)
          get "/api/v1/contracts/colleagues",
              headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
          
          expect(response).to have_http_status(:ok)
          expect(response.content_type).to match(/application\/json/)
          
          json = JSON.parse(response.body)
          
          # Should have the expected structure
          expect(json).to have_key('colleagues')
          expect(json).to have_key('current_user_contract_id')
          
          # Should return only active colleagues (not the current user)
          colleagues = json['colleagues']
          expect(colleagues.length).to eq(2)
          
          # Verify colleague data structure and ordering (by last_name)
          expect(colleagues[0]).to include(
            'id' => contract1.id,
            'first_name' => 'Alice',
            'last_name' => 'Anderson',
            'email' => colleague1.email,
            'job_title' => 'Developer',
            'is_current_user' => false
          )
          
          expect(colleagues[1]).to include(
            'id' => contract2.id,
            'first_name' => 'Bob',
            'last_name' => 'Brown',
            'email' => colleague2.email,
            'job_title' => 'Designer',
            'is_current_user' => false
          )
          
          # Should not include terminated contract
          colleague_ids = colleagues.map { |c| c['id'] }
          expect(colleague_ids).not_to include(contract3.id)
          
          # Should not include current user's contract
          expect(colleague_ids).not_to include(contract.id)
          
          # Should include current user contract ID
          expect(json['current_user_contract_id']).to eq(contract.id)
        end
      end
      
      context "with include_self=true" do
        it "returns list including current user marked appropriately" do
          token = jwt_token_for(user)
          get "/api/v1/contracts/colleagues",
              params: { include_self: 'true' },
              headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
          
          expect(response).to have_http_status(:ok)
          
          json = JSON.parse(response.body)
          colleagues = json['colleagues']
          
          # Should include current user
          expect(colleagues.length).to eq(3)
          
          # Find current user in the list
          current_user_data = colleagues.find { |c| c['id'] == contract.id }
          expect(current_user_data).to be_present
          expect(current_user_data['is_current_user']).to be true
          
          # Others should not be marked as current user
          other_colleagues = colleagues.reject { |c| c['id'] == contract.id }
          expect(other_colleagues.all? { |c| c['is_current_user'] == false }).to be true
        end
      end
    end
    
    context "when user has no contract with company" do
      before do
        contract.destroy
      end
      
      it "returns forbidden error" do
        token = jwt_token_for(user)
        get "/api/v1/contracts/colleagues",
            headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:forbidden)
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end
    
    context "when user is not associated with company" do
      let(:other_company) { create(:company) }
      let(:other_user) { create(:user) }
      let(:other_role) { create(:role, name: 'employee') }
      let!(:other_user_role) { create(:company_user_role, user: other_user, company: other_company, role: other_role) }
      
      it "returns forbidden error" do
        # Create a token for a user in a different company
        token = jwt_token_for(other_user)
        get "/api/v1/contracts/colleagues",
            headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:forbidden)
      end
    end
    
    context "without authentication" do
      it "returns unauthorized" do
        get "/api/v1/contracts/colleagues",
            headers: { 'Accept' => 'application/json' }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end