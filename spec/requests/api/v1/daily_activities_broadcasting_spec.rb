# ABOUTME: Tests for real-time broadcasting in DailyActivitiesController
# ABOUTME: Validates that team status updates are broadcast when work activities start/end

require 'rails_helper'

RSpec.describe 'Daily Activities Broadcasting', type: :request do
  let(:company) { create(:company) }
  let(:user) { create(:user) }
  let(:contract) { create(:contract, user: user, company: company) }
  let(:work) { create(:work, company: company) }
  let(:work_assignment) { create(:work_assignment, work: work, contract: contract) }
  let(:daily_log) { create(:daily_log, contract: contract, user: user, start_time: Time.current, end_time: nil) }
  
  let(:valid_token) do
    JWT.encode(
      {
        user_id: user.id,
        email: user.email,
        company_id: company.id,
        exp: 24.hours.from_now.to_i
      },
      Rails.application.credentials.devise_jwt_secret_key
    )
  end

  let(:headers) do
    {
      'Authorization' => "Bearer #{valid_token}",
      'Accept' => 'application/json',
      'Content-Type' => 'application/json'
    }
  end

  before do
    ActsAsTenant.current_tenant = company
    daily_log # Ensure daily log exists
  end

  describe 'POST /api/v1/daily_activities/start_work_activity' do
    it 'broadcasts team status update when work activity starts' do
      expect {
        post '/api/v1/daily_activities/start_work_activity',
             params: { work_id: work.id, activity_type: 'work_at_location' }.to_json,
             headers: headers
      }.to have_broadcasted_to(company).from_channel(TeamStatusChannel).with { |data|
        expect(data[:type]).to eq('team_status_update')
        expect(data[:event_type]).to eq('work_activity_started')
        expect(data[:employee_id]).to eq(user.id)
        expect(data[:employee_name]).to eq("#{contract.first_name} #{contract.last_name}")
        expect(data[:working]).to be true
        expect(data[:current_work][:id]).to eq(work.id)
      }

      expect(response).to have_http_status(:created)
    end
  end

  describe 'PATCH /api/v1/daily_activities/:id/end_work_activity' do
    let(:daily_activity) do
      create(:daily_activity,
             user: user,
             company: company,
             contract: contract,
             work: work,
             work_assignment: work_assignment,
             activity_type: 'work_at_location',
             start_time: 1.hour.ago,
             end_time: nil)
    end

    it 'broadcasts team status update when work activity ends' do
      expect {
        patch "/api/v1/daily_activities/#{daily_activity.id}/end_work_activity",
              headers: headers
      }.to have_broadcasted_to(company).from_channel(TeamStatusChannel).with { |data|
        expect(data[:type]).to eq('team_status_update')
        expect(data[:event_type]).to eq('work_activity_ended')
        expect(data[:employee_id]).to eq(user.id)
        expect(data[:employee_name]).to eq("#{contract.first_name} #{contract.last_name}")
        expect(data[:working]).to be false
      }

      expect(response).to have_http_status(:ok)
    end
  end
end