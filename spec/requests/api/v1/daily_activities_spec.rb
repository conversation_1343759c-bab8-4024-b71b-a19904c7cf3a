require 'rails_helper'

RSpec.describe "Api::V1::DailyActivities", type: :request do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let(:contract) { create(:contract, user: user, company: company, status: :active) }
  let(:work) { create(:work, company: company) }
  let!(:work_assignment) { create(:work_assignment, work: work, contract: contract) }
  
  # Setup role for the user
  let(:role) { create(:role, name: 'employee') }
  let!(:user_role) { create(:company_user_role, user: user, company: company, role: role) }
  
  describe "GET /api/v1/daily_activities/current_work_activity" do
    before do
      # Ensure contract exists before each test
      contract
    end
    
    context "when user has no active work activity" do
      it "returns nil activity" do
        token = jwt_token_for(user)
        get "/api/v1/daily_activities/current_work_activity",
            headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:ok)
        expect(response.content_type).to match(/application\/json/)
        
        json = JSON.parse(response.body)
        expect(json['activity']).to be_nil
        expect(json['work_session']).to be_nil
      end
    end
    
    context "when user has an active work activity" do
      let!(:active_activity) do
        create(:daily_activity,
          user: user,
          company: company,
          contract: contract,
          work: work,
          work_assignment: work_assignment,
          activity_type: 'work_at_location',
          start_time: 1.hour.ago,
          end_time: nil
        )
      end
      
      let!(:work_session) do
        create(:work_session,
          user: user,
          company: company,
          work: work,
          work_assignment: work_assignment,
          daily_activity: active_activity,
          start_time: 1.hour.ago,
          status: 'in_progress'
        )
      end
      
      it "returns the active activity with work details" do
        token = jwt_token_for(user)
        get "/api/v1/daily_activities/current_work_activity",
            headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:ok)
        
        json = JSON.parse(response.body)
        expect(json['activity']).to be_present
        expect(json['activity']['id']).to eq(active_activity.id)
        expect(json['activity']['work']).to be_present
        # Work assignment might be nested differently
        expect(json['activity']['work_assignment_id']).to eq(work_assignment.id)
        expect(json['work_session']).to be_present
      end
    end
    
    context "when user has no contract with company" do
      before do
        contract.destroy
      end
      
      it "returns forbidden error" do
        token = jwt_token_for(user)
        get "/api/v1/daily_activities/current_work_activity",
            headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:forbidden)
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end
  end
  
  describe "POST /api/v1/daily_activities/start_work_activity" do
    before do
      contract # Ensure contract exists
      work_assignment # Ensure work assignment exists
    end
    
    context "with valid parameters" do
      it "creates a new work activity and work session" do
        token = jwt_token_for(user)
        
        expect {
          post "/api/v1/daily_activities/start_work_activity",
               params: {
                 work_id: work.id,
                 activity_type: 'work_at_location',
                 daily_log_id: nil
               },
               headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        }.to change(DailyActivity, :count).by(1)
          .and change(WorkSession, :count).by(1)
        
        expect(response).to have_http_status(:created)
        
        json = JSON.parse(response.body)
        expect(json['activity']).to be_present
        expect(json['activity']['work']).to be_present
        expect(json['activity']['work_assignment']).to be_present
        expect(json['work_session']).to be_present
        expect(json['message']).to be_present
      end
      
      it "ends any existing work activities before creating new one" do
        # Create an existing active activity
        existing_activity = create(:daily_activity,
          user: user,
          company: company,
          contract: contract,
          activity_type: 'work_at_location',
          end_time: nil
        )
        
        token = jwt_token_for(user)
        post "/api/v1/daily_activities/start_work_activity",
             params: {
               work_id: work.id,
               activity_type: 'work_at_location'
             },
             headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:created)
        existing_activity.reload
        expect(existing_activity.end_time).to be_present
      end
    end
    
    context "when user is not assigned to the work" do
      let(:other_work) { create(:work, company: company) }
      
      it "returns forbidden error" do
        token = jwt_token_for(user)
        post "/api/v1/daily_activities/start_work_activity",
             params: {
               work_id: other_work.id,
               activity_type: 'work_at_location'
             },
             headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:forbidden)
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end
    
    context "when work doesn't exist" do
      it "returns not found error" do
        token = jwt_token_for(user)
        post "/api/v1/daily_activities/start_work_activity",
             params: {
               work_id: 999999,
               activity_type: 'work_at_location'
             },
             headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:not_found)
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end
  end
  
  describe "PATCH /api/v1/daily_activities/:id/end_work_activity" do
    before { contract } # Ensure contract exists
    let!(:active_activity) do
      create(:daily_activity,
        user: user,
        company: company,
        contract: contract,
        work: work,
        activity_type: 'work_at_location',
        start_time: 1.hour.ago,
        end_time: nil
      )
    end
    
    let!(:work_session) do
      create(:work_session,
        user: user,
        company: company,
        work: work,
        daily_activity: active_activity,
        start_time: 1.hour.ago,
        status: 'in_progress'
      )
    end
    
    context "with valid activity" do
      it "ends the activity and work session" do
        token = jwt_token_for(user)
        patch "/api/v1/daily_activities/#{active_activity.id}/end_work_activity",
              params: { notes: "Task completed" },
              headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:ok)
        
        json = JSON.parse(response.body)
        expect(json['activity']).to be_present
        expect(json['work_session']).to be_present
        expect(json['message']).to be_present
        
        active_activity.reload
        work_session.reload
        
        expect(active_activity.end_time).to be_present
        expect(work_session.status).to eq('completed')
        expect(work_session.end_time).to be_present
      end
      
      it "adds notes to work session if provided" do
        token = jwt_token_for(user)
        patch "/api/v1/daily_activities/#{active_activity.id}/end_work_activity",
              params: { notes: "Task completed successfully" },
              headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:ok)
        
        # Need to check how notes are stored in work_session
        # This depends on the actual implementation
      end
    end
    
    context "when activity doesn't exist" do
      it "returns not found error" do
        token = jwt_token_for(user)
        patch "/api/v1/daily_activities/999999/end_work_activity",
              headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:not_found)
        json = JSON.parse(response.body)
        expect(json['error']).to be_present
      end
    end
    
    context "when activity belongs to another user" do
      let(:other_user) { create(:user) }
      let(:other_contract) { create(:contract, user: other_user, company: company) }
      let!(:other_activity) do
        create(:daily_activity,
          user: other_user,
          company: company,
          contract: other_contract,
          end_time: nil
        )
      end
      
      it "returns not found error" do
        token = jwt_token_for(user)
        patch "/api/v1/daily_activities/#{other_activity.id}/end_work_activity",
              headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:not_found)
      end
    end
  end
  
  describe "POST /api/v1/daily_activities" do
    before { contract } # Ensure contract exists
    context "with valid parameters for regular activity" do
      it "creates a new daily activity" do
        token = jwt_token_for(user)
        expect {
          post "/api/v1/daily_activities",
               params: {
                 daily_activity: {
                   description: "Other work activity",
                   start_time: Time.current.iso8601,
                   activity_type: 'regular',
                   daily_log_id: nil
                 }
               },
               headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        }.to change(DailyActivity, :count).by(1)
        
        expect(response).to have_http_status(:created)
        
        json = JSON.parse(response.body)
        expect(json['id']).to be_present
        expect(json['description']).to eq("Other work activity")
        expect(json['activity_type']).to eq('regular')
      end
    end
    
    context "with invalid parameters" do
      it "returns validation errors" do
        token = jwt_token_for(user)
        post "/api/v1/daily_activities",
             params: {
               daily_activity: {
                 description: "",
                 activity_type: 'invalid_type'
               }
             },
             headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:unprocessable_entity)
        json = JSON.parse(response.body)
        expect(json['errors']).to be_present
      end
    end
  end
  
  describe "PUT /api/v1/daily_activities/:id" do
    before { contract } # Ensure contract exists
    let!(:activity) do
      create(:daily_activity,
        user: user,
        company: company,
        contract: contract,
        description: "Original description",
        activity_type: 'regular',
        start_time: 1.hour.ago
      )
    end
    
    context "with valid parameters" do
      it "updates the activity" do
        token = jwt_token_for(user)
        put "/api/v1/daily_activities/#{activity.id}",
            params: {
              daily_activity: {
                end_time: Time.current.iso8601
              }
            },
            headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:ok)
        
        activity.reload
        expect(activity.end_time).to be_present
      end
    end
    
    context "when activity belongs to another user" do
      let(:other_user) { create(:user) }
      let(:other_contract) { create(:contract, user: other_user, company: company) }
      let!(:other_activity) do
        create(:daily_activity,
          user: other_user,
          company: company,
          contract: other_contract
        )
      end
      
      it "returns not found" do
        token = jwt_token_for(user)
        put "/api/v1/daily_activities/#{other_activity.id}",
            params: {
              daily_activity: { description: "Hacked!" }
            },
            headers: auth_headers(token).merge({ 'Accept' => 'application/json' })
        
        expect(response).to have_http_status(:not_found)
      end
    end
  end
end