# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'JWT-Only Mode', type: :request do
  let(:user) { create(:user, password: 'password', password_confirmation: 'password') }
  let(:company) { create(:company) }
  let(:contract) { create(:contract, user: user, company: company) }
  let(:user_company_role) { create(:company_user_role, user: user, company: company, is_primary: true) }
  
  before do
    # Ensure user has a primary company
    user_company_role
    user.reload
    
    # Clear Redis before each test
    begin
      Redis.current.flushdb if Rails.env.test?
    rescue => e
      Rails.logger.warn "Could not clear Redis in test: #{e.message}"
    end
  end

  describe 'when JWT-only mode is enabled' do
    before do
      allow(FeatureFlags).to receive(:jwt_only_mode_enabled?).and_return(true)
    end

    describe 'API authentication' do
      context 'with valid JWT token' do
        let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
        let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

        it 'allows access to protected API endpoints' do
          # Use a simpler endpoint that definitely exists
          get '/api/v1/auth/jwt_logout', headers: auth_headers
          expect(response).not_to have_http_status(:unauthorized)
        end

        it 'logs JWT authentication method' do
          expect(Rails.logger).to receive(:info).with(/API authenticated via JWT for user: #{user.id}/)
          get '/api/v1/auth/jwt_logout', headers: auth_headers
        end
      end

      context 'with session authentication (no JWT)' do
        before do
          # Mock session-based authentication without actual login
          allow_any_instance_of(Api::V1::ApiController).to receive(:user_signed_in?).and_return(true)
          allow_any_instance_of(Api::V1::ApiController).to receive(:current_user).and_return(user)
        end

        it 'denies access to protected API endpoints' do
          get '/api/v1/auth/jwt_logout'
          expect(response).to have_http_status(:unauthorized)
        end

        it 'returns JWT-specific error message' do
          get '/api/v1/auth/jwt_logout'
          expect(JSON.parse(response.body)['error']).to eq('JWT authentication required')
        end

        it 'logs JWT-only mode authentication failure' do
          expect(Rails.logger).to receive(:info).with(/API authentication failed in JWT-only mode: No valid JWT provided/)
          get '/api/v1/auth/jwt_logout'
        end
      end

      context 'with no authentication' do
        it 'denies access and returns JWT-specific error' do
          get '/api/v1/auth/jwt_logout'
          expect(response).to have_http_status(:unauthorized)
          expect(JSON.parse(response.body)['error']).to eq('JWT authentication required')
        end
      end

      context 'with invalid JWT token' do
        let(:invalid_token) { 'invalid.jwt.token' }
        let(:auth_headers) { { 'Authorization' => "Bearer #{invalid_token}" } }

        it 'denies access to protected API endpoints' do
          get '/api/v1/auth/jwt_logout', headers: auth_headers
          expect(response).to have_http_status(:unauthorized)
        end
      end

      context 'with expired JWT token' do
        let(:expired_payload) { user.jwt_payload.merge(exp: 1.hour.ago.to_i) }
        let(:expired_token) { JWT.encode(expired_payload, Rails.application.credentials.jwt_secret, 'HS256') }
        let(:auth_headers) { { 'Authorization' => "Bearer #{expired_token}" } }

        it 'denies access to protected API endpoints' do
          get '/api/v1/auth/jwt_logout', headers: auth_headers
          expect(response).to have_http_status(:unauthorized)
        end
      end
    end

    describe 'refresh token endpoint security' do
      let(:refresh_token) { JwtService.encode_refresh_token(user.jwt_payload) }

      context 'with refresh token in HttpOnly cookie' do
        before do
          cookies[:refresh_token] = refresh_token
        end

        it 'successfully refreshes the token' do
          post '/api/v1/auth/refresh_token'
          expect(response).to have_http_status(:ok)
          expect(JSON.parse(response.body)['access_token']).to be_present
        end
      end

      context 'with refresh token in request body (fallback disabled)' do
        it 'rejects the refresh request' do
          post '/api/v1/auth/refresh_token', params: { refresh_token: refresh_token }
          expect(response).to have_http_status(:unauthorized)
          expect(JSON.parse(response.body)['error']).to eq('Refresh token required in secure cookie')
        end

        it 'logs security warning' do
          expect(Rails.logger).to receive(:warn).with(/JWT-only mode: Refresh token request without HttpOnly cookie/)
          post '/api/v1/auth/refresh_token', params: { refresh_token: refresh_token }
        end
      end

      context 'with refresh token in Authorization header (fallback disabled)' do
        let(:auth_headers) { { 'Authorization' => "Bearer #{refresh_token}" } }

        it 'rejects the refresh request' do
          post '/api/v1/auth/refresh_token', headers: auth_headers
          expect(response).to have_http_status(:unauthorized)
          expect(JSON.parse(response.body)['error']).to eq('Refresh token required in secure cookie')
        end
      end

      context 'with no refresh token provided' do
        it 'rejects the refresh request with specific error' do
          post '/api/v1/auth/refresh_token'
          expect(response).to have_http_status(:unauthorized)
          expect(JSON.parse(response.body)['error']).to eq('Refresh token required in secure cookie')
        end
      end
    end

    describe 'production cookie configuration validation' do
      context 'in production environment' do
        before do
          allow(Rails.env).to receive(:production?).and_return(true)
        end

        it 'validates secure cookie configuration' do
          expect(SecureCookieHelper.jwt_only_mode_config_valid?).to be true
        end

        context 'with insecure cookie configuration' do
          before do
            allow(Rails.application.config).to receive(:refresh_token_cookie).and_return({
              secure: false,
              httponly: true,
              same_site: :strict
            })
          end

          it 'reports invalid configuration' do
            expect(SecureCookieHelper.jwt_only_mode_config_valid?).to be false
          end

          it 'logs security error' do
            expect(Rails.logger).to receive(:error).with(/JWT-only mode requires secure cookie configuration in production/)
            SecureCookieHelper.jwt_only_mode_config_valid?
          end
        end
      end
    end

    describe 'feature flag integration' do
      it 'correctly reports JWT-only mode status' do
        expect(FeatureFlags.jwt_only_mode_enabled?).to be true
        expect(FeatureFlags.status[:jwt][:only_mode]).to be true
      end

      it 'logs feature flag status' do
        expect(Rails.logger).to receive(:info).with(/\[FeatureFlags\] Current status:.*only_mode.*true/)
        FeatureFlags.log_status
      end
    end
  end

  describe 'when JWT-only mode is disabled' do
    before do
      allow(FeatureFlags).to receive(:jwt_only_mode_enabled?).and_return(false)
    end

    describe 'dual authentication behavior' do
      context 'with session authentication' do
        before do
          # Simulate session-based login (this would need actual session setup)
          allow_any_instance_of(Api::V1::ApiController).to receive(:user_signed_in?).and_return(true)
          allow_any_instance_of(Api::V1::ApiController).to receive(:current_user).and_return(user)
        end

        it 'allows access via session fallback' do
          get '/api/v1/auth/jwt_logout'
          expect(response).to have_http_status(:ok)
        end

        it 'logs session authentication method' do
          expect(Rails.logger).to receive(:info).with(/API authenticated via session for user: #{user.id}/)
          get '/api/v1/auth/jwt_logout'
        end
      end

      context 'refresh token fallback mechanisms' do
        let(:refresh_token) { JwtService.encode_refresh_token(user.jwt_payload) }

        it 'accepts refresh token from request body' do
          post '/api/v1/auth/refresh_token', params: { refresh_token: refresh_token }
          expect(response).to have_http_status(:ok)
        end

        it 'accepts refresh token from Authorization header' do
          post '/api/v1/auth/refresh_token', headers: { 'Authorization' => "Bearer #{refresh_token}" }
          expect(response).to have_http_status(:ok)
        end
      end
    end

    it 'reports JWT-only mode as disabled' do
      expect(FeatureFlags.jwt_only_mode_enabled?).to be false
      expect(FeatureFlags.status[:jwt][:only_mode]).to be false
    end
  end

  describe 'comprehensive JWT workflow in JWT-only mode' do
    before do
      allow(FeatureFlags).to receive(:jwt_only_mode_enabled?).and_return(true)
    end

    it 'supports complete JWT authentication flow' do
      # 1. Login via JWT
      post '/api/v1/auth/jwt_login', params: {
        email: user.email,
        password: 'password'
      }
      expect(response).to have_http_status(:ok)
      
      login_response = JSON.parse(response.body)
      access_token = login_response['access_token']
      expect(access_token).to be_present

      # 2. Access protected resource with JWT
      get '/api/v1/auth/jwt_logout', headers: { 'Authorization' => "Bearer #{access_token}" }
      expect(response).to have_http_status(:ok)

      # Login again to test refresh
      post '/api/v1/auth/jwt_login', params: {
        email: user.email,
        password: 'password'
      }
      expect(response).to have_http_status(:ok)
      
      login_response2 = JSON.parse(response.body)
      access_token2 = login_response2['access_token']

      # 3. Refresh token using HttpOnly cookie
      post '/api/v1/auth/refresh_token'
      expect(response).to have_http_status(:ok)
      
      refresh_response = JSON.parse(response.body)
      new_access_token = refresh_response['access_token']
      expect(new_access_token).to be_present
      expect(new_access_token).not_to eq(access_token2)

      # 4. Access resource with new token
      get '/api/v1/auth/jwt_logout', headers: { 'Authorization' => "Bearer #{new_access_token}" }
      expect(response).to have_http_status(:ok)

      # 5. Logout
      post '/api/v1/auth/jwt_logout', headers: { 'Authorization' => "Bearer #{new_access_token}" }
      expect(response).to have_http_status(:ok)

      # 6. Verify token is revoked
      get '/api/v1/auth/jwt_logout', headers: { 'Authorization' => "Bearer #{new_access_token}" }
      expect(response).to have_http_status(:unauthorized)
    end
  end
end