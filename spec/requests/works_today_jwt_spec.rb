# ABOUTME: Test suite for works/today endpoint with JWT authentication
# ABOUTME: Reproduces and validates the fix for employee user work assignment display issue

require 'rails_helper'

RSpec.describe 'Works Today JWT Authentication', type: :request do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let(:employee_role) { Role.find_by(name: 'employee') || create(:role, name: 'employee') }
  let(:contract) { create(:contract, user: user, company: company, status: 'active') }
  let(:work) { create(:work, company: company, status: 'scheduled', scheduled_start_date: Date.current) }
  let(:work_assignment) { create(:work_assignment, work: work, contract: contract) }
  
  let(:jwt_token) do
    post '/api/v1/auth/jwt_login', params: {
      email: user.email,
      password: user.password
    }
    JSON.parse(response.body)['access_token']
  end
  
  before do
    # Setup user with employee role in company
    create(:company_user_role, user: user, company: company, role: employee_role, is_primary: true)
    # Ensure work assignment exists
    work_assignment
  end

  describe 'GET /works/today' do
    context 'with valid JWT token' do
      it 'successfully returns today\'s work assignments for employee user' do
        get '/works/today', headers: {
          'Authorization' => "Bearer #{jwt_token}",
          'Accept' => 'application/json'
        }
        
        expect(response).to have_http_status(:ok)
        
        works = JSON.parse(response.body)
        expect(works).to be_an(Array)
        expect(works.length).to eq(1)
        expect(works.first['id']).to eq(work.id)
        expect(works.first['title']).to eq(work.title)
      end
      
      it 'returns work assignments with correct associations' do
        get '/works/today', headers: {
          'Authorization' => "Bearer #{jwt_token}",
          'Accept' => 'application/json'
        }
        
        expect(response).to have_http_status(:ok)
        
        works = JSON.parse(response.body)
        work_data = works.first
        
        # Check work_assignments are included
        expect(work_data).to have_key('work_assignments')
        expect(work_data['work_assignments']).to be_an(Array)
        
        assignment = work_data['work_assignments'].first
        expect(assignment['contract']['id']).to eq(contract.id)
      end
      
      it 'filters works by scheduled date (today)' do
        # Create work for tomorrow (should not be included)
        tomorrow_work = create(:work, company: company, status: 'scheduled', scheduled_start_date: Date.tomorrow)
        create(:work_assignment, work: tomorrow_work, contract: contract)
        
        get '/works/today', headers: {
          'Authorization' => "Bearer #{jwt_token}",
          'Accept' => 'application/json'
        }
        
        expect(response).to have_http_status(:ok)
        
        works = JSON.parse(response.body)
        work_ids = works.map { |w| w['id'] }
        
        # Should only include today's work
        expect(work_ids).to include(work.id)
        expect(work_ids).not_to include(tomorrow_work.id)
      end
      
      it 'filters works by status (scheduled, in_progress, unprocessed)' do
        # Create works with different statuses
        completed_work = create(:work, company: company, status: 'completed', scheduled_start_date: Date.current)
        create(:work_assignment, work: completed_work, contract: contract)
        
        cancelled_work = create(:work, company: company, status: 'cancelled', scheduled_start_date: Date.current)
        create(:work_assignment, work: cancelled_work, contract: contract)
        
        get '/works/today', headers: {
          'Authorization' => "Bearer #{jwt_token}",
          'Accept' => 'application/json'
        }
        
        expect(response).to have_http_status(:ok)
        
        works = JSON.parse(response.body)
        work_ids = works.map { |w| w['id'] }
        
        # Should only include scheduled work
        expect(work_ids).to include(work.id)
        expect(work_ids).not_to include(completed_work.id)
        expect(work_ids).not_to include(cancelled_work.id)
      end
      
      it 'only returns works assigned to the current user' do
        # Create another user and their work
        other_user = create(:user)
        other_contract = create(:contract, user: other_user, company: company, status: 'active')
        other_work = create(:work, company: company, status: 'scheduled', scheduled_start_date: Date.current)
        create(:work_assignment, work: other_work, contract: other_contract)
        
        get '/works/today', headers: {
          'Authorization' => "Bearer #{jwt_token}",
          'Accept' => 'application/json'
        }
        
        expect(response).to have_http_status(:ok)
        
        works = JSON.parse(response.body)
        work_ids = works.map { |w| w['id'] }
        
        # Should only include current user's work
        expect(work_ids).to include(work.id)
        expect(work_ids).not_to include(other_work.id)
      end
    end
    
    context 'without JWT token' do
      it 'redirects to login or returns unauthorized' do
        get '/works/today', headers: { 'Accept' => 'application/json' }
        
        # In JWT-only mode, should return 401
        # In dual-auth mode, might redirect (302)
        expect(response.status).to be_in([302, 401])
      end
    end
    
    context 'with invalid JWT token' do
      it 'returns unauthorized' do
        get '/works/today', headers: {
          'Authorization' => 'Bearer invalid.jwt.token',
          'Accept' => 'application/json'
        }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end
    
    context 'with user having no contract in company' do
      it 'returns forbidden error' do
        # Create user without contract
        user_without_contract = create(:user)
        create(:company_user_role, user: user_without_contract, company: company, role: employee_role, is_primary: true)
        
        post '/api/v1/auth/jwt_login', params: {
          email: user_without_contract.email,
          password: user_without_contract.password
        }
        token_without_contract = JSON.parse(response.body)['access_token']
        
        get '/works/today', headers: {
          'Authorization' => "Bearer #{token_without_contract}",
          'Accept' => 'application/json'
        }
        
        expect(response).to have_http_status(:forbidden)
        error_response = JSON.parse(response.body)
        expect(error_response).to have_key('error')
      end
    end
    
    context 'with multi-tenant scenario' do
      it 'only returns works from the current tenant company' do
        # Create second company and user role
        other_company = create(:company)
        create(:company_user_role, user: user, company: other_company, role: employee_role)
        other_contract = create(:contract, user: user, company: other_company, status: 'active')
        other_work = create(:work, company: other_company, status: 'scheduled', scheduled_start_date: Date.current)
        create(:work_assignment, work: other_work, contract: other_contract)
        
        get '/works/today', headers: {
          'Authorization' => "Bearer #{jwt_token}",
          'Accept' => 'application/json'
        }
        
        expect(response).to have_http_status(:ok)
        
        works = JSON.parse(response.body)
        work_ids = works.map { |w| w['id'] }
        
        # Should only include works from primary company (first company)
        expect(work_ids).to include(work.id)
        expect(work_ids).not_to include(other_work.id)
      end
    end
  end
  
  # Test to reproduce the specific bug reported
  describe 'Regression test for Elena user issue' do
    let(:elena_email) { '<EMAIL>' }
    
    context 'when Elena user exists with real data' do
      before do
        @elena_user = User.find_by(email: elena_email)
        skip "Elena user not found in test database" unless @elena_user
      end
      
      it 'returns Elena\'s work assignments successfully' do
        # Login as Elena
        post '/api/v1/auth/jwt_login', params: {
          email: elena_email,
          password: '123456'
        }
        
        expect(response).to have_http_status(:ok)
        elena_token = JSON.parse(response.body)['access_token']
        
        # Test the problematic endpoint
        get '/works/today', headers: {
          'Authorization' => "Bearer #{elena_token}",
          'Accept' => 'application/json'
        }
        
        expect(response).to have_http_status(:ok)
        
        works = JSON.parse(response.body)
        expect(works).to be_an(Array)
        
        # Elena should have work assignments based on previous investigation
        # If this test fails, it indicates the bug is still present
        puts "Elena's works count: #{works.length}"
        puts "Elena's works: #{works.map { |w| { id: w['id'], title: w['title'] } }}"
      end
    end
  end
end