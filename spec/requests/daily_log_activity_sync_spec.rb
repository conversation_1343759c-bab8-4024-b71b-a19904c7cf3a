# ABOUTME: RSpec test for DailyLog/DailyActivity synchronization 
# ABOUTME: Tests requirement that ending DailyLog automatically ends all its DailyActivities
require 'rails_helper'

RSpec.describe "DailyLog Activity Synchronization", type: :request do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let(:contract) { create(:contract, user: user, company: company, status: :active) }
  let(:work) { create(:work, company: company) }
  let!(:work_assignment) { create(:work_assignment, work: work, contract: contract) }
  let(:role) { create(:role, name: 'employee') }
  let!(:user_role) { create(:company_user_role, user: user, company: company, role: role) }
  
  # Ensure consistent data setup for all tests
  before do
    user
    company
    contract
    work_assignment
    user_role
  end

  describe "when daily log ends, work activities should automatically end" do
    let(:daily_log) { create(:daily_log, user: user, contract: contract, company: company, start_time: 2.hours.ago, end_time: nil) }
    
    context "via main controller (TimeTracking.vue endWork)" do
      it "ends work activities when daily log is ended" do
        # Step 1: Create an active work activity linked to daily log
        activity = create(:daily_activity,
          user: user,
          company: company,
          contract: contract,
          work: work,
          work_assignment: work_assignment,
          daily_log: daily_log,
          activity_type: 'work_at_location',
          start_time: 1.hour.ago,
          end_time: nil
        )
        
        work_session = create(:work_session,
          user: user,
          company: company,
          work: work,
          work_assignment: work_assignment,
          daily_activity: activity,
          start_time: 1.hour.ago,
          status: 0  # Use integer value for enum: in_progress = 0
        )
        
        # Verify activity is active
        expect(activity.end_time).to be_nil
        expect(work_session.status).to eq('in_progress')
        
        # Step 2: End daily log via main controller (what TimeTracking.vue uses)
        token = jwt_token_for(user)
        put "/daily_logs/#{daily_log.id}",
          params: {
            daily_log: { end_time: Time.current.iso8601 }
          },
          headers: auth_headers(token).merge({ 'X-Company-Id' => company.id.to_s })
        
        expect(response).to have_http_status(:ok)
        
        # Step 3: Verify work activity was automatically ended
        activity.reload
        work_session.reload
        
        expect(activity.end_time).to be_present, "DailyActivity should be ended when DailyLog ends"
        expect(work_session.status).to eq('completed'), "WorkSession should be completed when DailyLog ends"
        expect(work_session.end_time).to be_present, "WorkSession should have end_time"
      end
    end
    
    context "via API controller" do
      it "ends work activities when daily log is ended via API" do
        # Step 1: Create an active work activity via API
        token = jwt_token_for(user)
        
        post "/api/v1/daily_activities/start_work_activity",
          params: {
            work_id: work.id,
            activity_type: 'work_at_location',
            daily_log_id: daily_log.id
          },
          headers: auth_headers(token).merge({ 'Accept' => 'application/json', 'X-Company-Id' => company.id.to_s })
        
        expect(response).to have_http_status(:created)
        
        json = JSON.parse(response.body)
        activity_id = json['activity']['id']
        work_session_id = json['work_session']['id'] if json['work_session']
        
        # Step 2: End daily log via API controller
        put "/api/v1/daily_logs/#{daily_log.id}",
          params: {
            daily_log: { end_time: Time.current.iso8601 }
          },
          headers: auth_headers(token).merge({ 'Accept' => 'application/json', 'X-Company-Id' => company.id.to_s })
        
        expect(response).to have_http_status(:ok)
        
        # Step 3: Verify work activity was automatically ended
        activity = DailyActivity.find(activity_id)
        expect(activity.end_time).to be_present, "DailyActivity should be ended when DailyLog ends via API"
        
        if work_session_id
          work_session = WorkSession.find(work_session_id)
          expect(work_session.status).to eq('completed'), "WorkSession should be completed when DailyLog ends via API"
        end
      end
    end
    
    context "edge case: activities with null daily_log_id" do
      it "ends activities with null daily_log_id when user's daily log ends" do
        # This tests the bug condition where daily_log_id might be null
        
        # Step 1: Create work activity with null daily_log_id (simulating the bug)
        activity = create(:daily_activity,
          user: user,
          company: company,
          contract: contract,
          work: work,
          daily_log: nil,  # This is the bug condition
          activity_type: 'work_at_location',
          start_time: 1.hour.ago,
          end_time: nil
        )
        
        # Verify activity is active and has null daily_log_id
        expect(activity.daily_log_id).to be_nil
        expect(activity.end_time).to be_nil
        
        # Step 2: End daily log
        token = jwt_token_for(user)
        put "/daily_logs/#{daily_log.id}",
          params: {
            daily_log: { end_time: Time.current.iso8601 }
          },
          headers: auth_headers(token)
        
        expect(response).to have_http_status(:ok)
        
        # Step 3: Activity with null daily_log_id should still be ended
        # (this is the behavior we want to implement)
        activity.reload
        
        # This will initially fail, confirming the bug
        expect(activity.end_time).to be_present, "Activities with null daily_log_id should still be ended when user's daily log ends"
      end
    end
    
    context "frontend workflow simulation" do
      it "simulates exact frontend workflow: start work, then end daily log" do
        token = jwt_token_for(user)
        
        # Step 1: Check current status (simulate TimeTracking.vue checkCurrentStatus)
        get '/daily_logs/current_status',
          headers: auth_headers(token)
        
        expect(response).to have_http_status(:ok)
        
        # Step 2: Start work activity (simulate TodayWorkSection.vue quickStartWork)
        post "/api/v1/daily_activities/start_work_activity",
          params: {
            work_id: work.id,
            activity_type: 'work_at_location',
            daily_log_id: daily_log.id
          },
          headers: auth_headers(token).merge({ 'Accept' => 'application/json', 'X-Company-Id' => company.id.to_s })
        
        expect(response).to have_http_status(:created)
        activity_data = JSON.parse(response.body)
        
        # Step 3: Check current work activity (simulate TodayWorkSection.vue fetchCurrentWorkActivity)
        get '/api/v1/daily_activities/current_work_activity',
          headers: auth_headers(token).merge({ 'Accept' => 'application/json', 'X-Company-Id' => company.id.to_s })
        
        expect(response).to have_http_status(:ok)
        current_activity = JSON.parse(response.body)
        expect(current_activity['activity']).to be_present
        
        # Step 4: End daily log (simulate TimeTracking.vue endWork)
        put "/daily_logs/#{daily_log.id}",
          params: {
            daily_log: { end_time: Time.current.iso8601 }
          },
          headers: auth_headers(token)
        
        expect(response).to have_http_status(:ok)
        
        # Step 5: Verify no current work activity after daily log ends
        get '/api/v1/daily_activities/current_work_activity',
          headers: auth_headers(token).merge({ 'Accept' => 'application/json', 'X-Company-Id' => company.id.to_s })
        
        expect(response).to have_http_status(:ok)
        current_activity_after = JSON.parse(response.body)
        expect(current_activity_after['activity']).to be_nil, "Should have no current work activity after daily log ends"
      end
    end
  end
end