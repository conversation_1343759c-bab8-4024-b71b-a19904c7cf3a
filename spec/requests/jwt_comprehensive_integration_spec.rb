# spec/requests/jwt_comprehensive_integration_spec.rb
# Chunk 49: Comprehensive JWT Integration Tests for current implementation

require 'rails_helper'

RSpec.describe 'JWT Comprehensive Integration', type: :request do
  let(:user) { create(:user, email: '<EMAIL>', password: 'password123') }
  let(:company) { create(:company, name: 'Test Company') }
  let(:secondary_company) { create(:company, name: 'Secondary Company') }
  let(:admin_role) { create(:role, name: 'owner') }
  let(:employee_role) { create(:role, name: 'employee') }
  
  before do
    # Set up user with multiple companies
    create(:company_user_role, user: user, company: company, role: admin_role, is_primary: true)
    create(:company_user_role, user: user, company: secondary_company, role: employee_role, is_primary: false)
    
    # Set tenant context
    ActsAsTenant.current_tenant = company
  end

  describe 'Complete JWT Authentication Lifecycle' do
    it 'successfully completes full authentication flow' do
      # Step 1: JWT Login
      post '/api/v1/auth/jwt_login', params: {
        email: user.email,
        password: 'password123'
      }
      
      expect(response).to have_http_status(:ok)
      login_response = JSON.parse(response.body)
      
      expect(login_response).to include(
        'success' => true,
        'access_token' => be_present,
        'expires_in' => JwtService::ACCESS_TOKEN_EXPIRATION_DURATION.to_i
      )
      expect(login_response['user']).to include(
        'id' => user.id,
        'email' => user.email,
        'company_id' => company.id
      )
      
      access_token = login_response['access_token']
      
      # Verify cookies are set
      expect(response.cookies['refresh_token']).to be_present
      expect(response.cookies['jwt_session_id']).to be_present
      
      # Step 2: Verify JWT payload
      payload = JwtService.decode(access_token)
      expect(payload).to include(
        'user_id' => user.id,
        'email' => user.email,
        'company_id' => company.id,
        'jti' => be_present
      )
      
      # Step 3: Use JWT for authenticated request
      get '/api/v1/user', headers: {
        'Authorization' => "Bearer #{access_token}"
      }
      
      expect(response).to have_http_status(:ok)
      current_user_response = JSON.parse(response.body)
      expect(current_user_response['email']).to eq(user.email)
      expect(current_user_response['company']['id']).to eq(company.id)
      
      # Step 4: Refresh token
      post '/api/v1/auth/refresh_token'
      
      expect(response).to have_http_status(:ok)
      refresh_response = JSON.parse(response.body)
      expect(refresh_response).to include(
        'success' => true,
        'access_token' => be_present
      )
      
      new_access_token = refresh_response['access_token']
      expect(new_access_token).not_to eq(access_token) # Should be a new token
      
      # Step 5: Verify new token works
      get '/api/v1/user', headers: {
        'Authorization' => "Bearer #{new_access_token}"
      }
      
      expect(response).to have_http_status(:ok)
      
      # Step 6: Logout
      post '/api/v1/auth/jwt_logout', headers: {
        'Authorization' => "Bearer #{new_access_token}"
      }
      
      expect(response).to have_http_status(:ok)
      logout_response = JSON.parse(response.body)
      expect(logout_response['success']).to be true
      
      # Step 7: Verify token is revoked
      get '/api/v1/user', headers: {
        'Authorization' => "Bearer #{new_access_token}"
      }
      
      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'JWT Security Features' do
    context 'Token Validation' do
      it 'rejects invalid JWT signatures' do
        # Create token with wrong secret
        payload = user.jwt_payload
        invalid_token = JWT.encode(payload, 'wrong_secret', 'HS256')
        
        get '/api/v1/user', headers: {
          'Authorization' => "Bearer #{invalid_token}"
        }
        
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)['error']).to be_present
      end
      
      it 'rejects expired tokens' do
        # Create expired token
        payload = user.jwt_payload
        expired_token = JwtService.encode(payload, 1.second.ago)
        
        get '/api/v1/user', headers: {
          'Authorization' => "Bearer #{expired_token}"
        }
        
        expect(response).to have_http_status(:unauthorized)
        error = JSON.parse(response.body)['error']
        expect(error).to match(/(expired|JWT authentication required)/i)
      end
      
      it 'rejects malformed tokens' do
        get '/api/v1/user', headers: {
          'Authorization' => 'Bearer invalid.jwt.token'
        }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'Token Revocation' do
      let(:jwt_token) do
        post '/api/v1/auth/jwt_login', params: {
          email: user.email,
          password: 'password123'
        }
        JSON.parse(response.body)['access_token']
      end
      
      it 'prevents reuse of revoked tokens' do
        # Use token successfully
        get '/api/v1/user', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        expect(response).to have_http_status(:ok)
        
        # Logout (revoke token)
        post '/api/v1/auth/jwt_logout', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        expect(response).to have_http_status(:ok)
        
        # Try to use revoked token
        get '/api/v1/user', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        expect(response).to have_http_status(:unauthorized)
      end
      
      it 'handles double logout gracefully' do
        # First logout
        post '/api/v1/auth/jwt_logout', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        expect(response).to have_http_status(:ok)
        
        # Second logout with same token
        post '/api/v1/auth/jwt_logout', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        expect(response).to have_http_status(:ok) # Should still succeed
      end
    end
  end

  describe 'Multi-Tenancy with JWT' do
    let(:jwt_token) do
      post '/api/v1/auth/jwt_login', params: {
        email: user.email,
        password: 'password123'
      }
      JSON.parse(response.body)['access_token']
    end
    
    it 'switches companies and updates JWT context' do
      # Verify initial company
      payload = JwtService.decode(jwt_token)
      expect(payload['company_id']).to eq(company.id)
      
      # Switch to secondary company
      post '/api/v1/companies/switch_company',
        params: { company_id: secondary_company.id },
        headers: { 'Authorization' => "Bearer #{jwt_token}" }
      
      expect(response).to have_http_status(:ok)
      switch_response = JSON.parse(response.body)
      expect(switch_response).to include(
        'success' => true,
        'access_token' => be_present,
        'company' => hash_including('id' => secondary_company.id)
      )
      
      # Verify new token has updated company
      new_token = switch_response['access_token']
      new_payload = JwtService.decode(new_token)
      expect(new_payload['company_id']).to eq(secondary_company.id)
      expect(new_payload['user_id']).to eq(user.id) # User ID unchanged
    end
    
    it 'prevents switching to unauthorized company' do
      unauthorized_company = create(:company, name: 'Unauthorized Company')
      
      post '/api/v1/companies/switch_company',
        params: { company_id: unauthorized_company.id },
        headers: { 'Authorization' => "Bearer #{jwt_token}" }
      
      expect(response).to have_http_status(:forbidden)
      error_response = JSON.parse(response.body)
      expect(error_response['error']).to match(/Company not found or access denied/i)
    end
  end

  describe 'Refresh Token Security' do
    it 'rotates refresh tokens on use' do
      # Initial login
      post '/api/v1/auth/jwt_login', params: {
        email: user.email,
        password: 'password123'
      }
      
      initial_access_token = JSON.parse(response.body)['access_token']
      
      # First refresh
      post '/api/v1/auth/refresh_token'
      
      expect(response).to have_http_status(:ok)
      first_refresh_response = JSON.parse(response.body)
      new_access_token = first_refresh_response['access_token']
      
      expect(new_access_token).not_to eq(initial_access_token)
      
      # Verify refresh token cookie is updated (rotated)
      expect(response.cookies['refresh_token']).to be_present
    end
    
    it 'handles invalid refresh tokens' do
      post '/api/v1/auth/refresh_token', params: {
        refresh_token: 'invalid.refresh.token'
      }
      
      expect(response).to have_http_status(:unauthorized)
    end
  end

  describe 'JWT Session Persistence' do
    it 'maintains authentication across requests using session cookie' do
      # Login
      post '/api/v1/auth/jwt_login', params: {
        email: user.email,
        password: 'password123'
      }
      
      expect(response).to have_http_status(:ok)
      
      # Verify session cookie is set
      session_cookie = response.cookies['jwt_session_id']
      expect(session_cookie).to be_present
      
      # In a real JWT-only implementation, subsequent requests 
      # could retrieve the JWT from Redis using the session cookie
      # For now, we'll verify the session was created
      expect(response.cookies['jwt_session_id']).to be_present
    end
  end

  describe 'API Error Handling' do
    it 'returns consistent error format for authentication failures' do
      # Test various authentication failure scenarios
      scenarios = [
        {
          desc: 'missing Authorization header',
          headers: {},
          expected_status: [401, 302] # 302 redirect or 401
        },
        {
          desc: 'invalid Bearer format',
          headers: { 'Authorization' => 'NotBearer token' },
          expected_status: [401, 302]
        },
        {
          desc: 'Bearer without token',
          headers: { 'Authorization' => 'Bearer ' },
          expected_status: [401, 302]
        }
      ]
      
      scenarios.each do |scenario|
        get '/api/v1/user', headers: scenario[:headers]
        
        expect(scenario[:expected_status]).to include(response.status)
        
        if response.status == 401
          error_response = JSON.parse(response.body)
          expect(error_response).to have_key('error')
        end
      end
    end
  end

  describe 'Performance and Reliability' do
    let(:jwt_token) do
      post '/api/v1/auth/jwt_login', params: {
        email: user.email,
        password: 'password123'
      }
      JSON.parse(response.body)['access_token']
    end
    
    it 'handles rapid sequential requests' do
      start_time = Time.current
      
      5.times do
        get '/api/v1/user', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        expect(response).to have_http_status(:ok)
      end
      
      elapsed = Time.current - start_time
      expect(elapsed).to be < 1.second # Should be fast
    end
    
    it 'maintains Redis connection stability' do
      # Verify Redis is accessible
      expect(Redis.current.with { |c| c.ping }).to eq('PONG')
      
      # Perform JWT operations that use Redis
      post '/api/v1/auth/jwt_logout', headers: {
        'Authorization' => "Bearer #{jwt_token}"
      }
      
      expect(response).to have_http_status(:ok)
      
      # Redis should still be accessible
      expect(Redis.current.with { |c| c.ping }).to eq('PONG')
    end
  end
end