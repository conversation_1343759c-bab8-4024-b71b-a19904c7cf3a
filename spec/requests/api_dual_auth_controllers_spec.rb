# spec/requests/api_dual_auth_controllers_spec.rb
require 'rails_helper'

RSpec.describe "API Controllers Dual Authentication", type: :request do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let!(:company_user_role) { create(:company_user_role, user: user, company: company, is_primary: true) }
  let!(:contract) { create(:contract, user: user, company: company) }
  
  # JWT setup
  let(:valid_payload) { { user_id: user.id, email: user.email, company_id: company.id } }
  let(:valid_jwt) { JwtService.encode(valid_payload) }
  
  before do
    # Mock User#jwt_payload to include company_id
    allow_any_instance_of(User).to receive(:jwt_payload).and_return(valid_payload)
  end
  
  shared_examples "dual auth controller" do |controller_path|
    context "with JWT authentication" do
      it "authenticates successfully and sets tenant context" do
        get controller_path, headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        # Response should be successful or redirect based on controller logic
        expect([200, 302]).to include(response.status)
      end
      
      it "returns 401 with invalid JWT" do
        get controller_path, headers: { 'Authorization' => "Bearer invalid.jwt.token" }
        
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)).to include('error' => 'Authentication required')
      end
      
      it "returns 401 with expired JWT" do
        expired_jwt = JwtService.encode(valid_payload, 1.second.ago)
        get controller_path, headers: { 'Authorization' => "Bearer #{expired_jwt}" }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end
    
    context "with session authentication" do
      before do
        sign_in user
      end
      
      it "authenticates successfully" do
        get controller_path
        
        # Response should be successful or redirect based on controller logic
        expect([200, 302]).to include(response.status)
      end
    end
    
    context "without authentication" do
      it "returns 401 unauthorized" do
        get controller_path
        
        expect(response).to have_http_status(:unauthorized)
        expect(JSON.parse(response.body)).to include('error' => 'Authentication required')
      end
    end
  end
  
  describe "DailyLogsController" do
    include_examples "dual auth controller", "/api/v1/daily_logs"
    
    context "with JWT authentication - specific actions" do
      let!(:daily_log) { create(:daily_log, contract: contract, user: user, start_time: Time.current) }
      
      it "can fetch daily logs" do
        get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response).to be_an(Array)
      end
      
      it "can get current status" do
        get "/api/v1/daily_logs/current_status", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('is_working')
      end
      
      it "can create a daily log" do
        post "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('daily_log')
      end
    end
  end
  
  describe "EventsController" do
    include_examples "dual auth controller", "/api/v1/events"
    
    context "with JWT authentication - specific actions" do
      it "can fetch events list" do
        get "/api/v1/events", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response).to be_an(Array)
      end
      
      it "can fetch events for a month" do
        get "/api/v1/events/fetch", 
            params: { date: Date.current.to_s },
            headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('events')
        expect(json_response).to have_key('works')
        expect(json_response).to have_key('meetings')
        expect(json_response).to have_key('holidays')
      end
      
      it "can create an event" do
        event_params = {
          event: {
            event_type: 'vacation',
            title: 'Test Event',
            start_time: Time.current + 1.day,
            end_time: Time.current + 2.days
          }
        }
        
        post "/api/v1/events", 
             params: event_params,
             headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:created)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response).to have_key('event')
      end
    end
  end
  
  describe "EmployeesController" do
    include_examples "dual auth controller", "/api/v1/employees"
    
    context "with JWT authentication - specific actions" do
      it "can fetch employees list" do
        get "/api/v1/employees", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response).to be_an(Array)
      end
      
      it "includes employee work status" do
        # Create a daily log for testing
        daily_log = create(:daily_log, contract: contract, user: user, start_time: Time.current)
        
        get "/api/v1/employees", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        # Should include contract information
        employee = json_response.first
        expect(employee).to have_key('id')
        expect(employee).to have_key('name')
        expect(employee).to have_key('working')
      end
    end
  end
  
  describe "Authentication precedence" do
    context "when both JWT and session are present" do
      before do
        sign_in user
      end
      
      it "prefers JWT authentication for DailyLogsController" do
        allow(Rails.logger).to receive(:info).and_call_original
        expect(Rails.logger).to receive(:info).with("API authenticated via JWT for user: #{user.id}").at_least(:once)
        
        get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        expect(response).to have_http_status(:success)
      end
      
      it "prefers JWT authentication for EventsController" do
        allow(Rails.logger).to receive(:info).and_call_original
        expect(Rails.logger).to receive(:info).with("API authenticated via JWT for user: #{user.id}").at_least(:once)
        
        get "/api/v1/events", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        expect(response).to have_http_status(:success)
      end
      
      it "prefers JWT authentication for EmployeesController" do
        allow(Rails.logger).to receive(:info).and_call_original
        expect(Rails.logger).to receive(:info).with("API authenticated via JWT for user: #{user.id}").at_least(:once)
        
        get "/api/v1/employees", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        expect(response).to have_http_status(:success)
      end
    end
  end
  
  describe "Tenant context with JWT" do
    it "sets correct tenant for DailyLogsController" do
      # Stub InvitationHandler to avoid email sending issues
      allow(InvitationHandler).to receive(:process).and_return(true)
      
      # Create daily logs for a different company
      other_company = create(:company)
      other_user = create(:user)
      create(:company_user_role, user: other_user, company: other_company)
      other_contract = create(:contract, company: other_company, user: other_user)
      other_daily_log = nil
      ActsAsTenant.with_tenant(other_company) do
        other_daily_log = create(:daily_log, contract: other_contract, user: other_user)
      end
      
      # Create daily logs for the user's company
      # Set tenant context to ensure proper creation
      user_daily_log = nil
      ActsAsTenant.with_tenant(company) do
        user_daily_log = create(:daily_log, contract: contract, user: user)
      end
      
      get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
      
      expect(response).to have_http_status(:success)
      json_response = JSON.parse(response.body)
      
      # Should only include daily logs from the JWT's company
      daily_log_ids = json_response.map { |log| log['id'] }
      expect(daily_log_ids).to include(user_daily_log.id)
      expect(daily_log_ids).not_to include(other_daily_log.id)
    end
    
    it "sets correct tenant for EventsController" do
      # Stub InvitationHandler to avoid email sending issues
      allow(InvitationHandler).to receive(:process).and_return(true)
      
      # Create an event for a different company
      other_company = create(:company)
      other_user = create(:user)
      create(:company_user_role, user: other_user, company: other_company)
      other_contract = create(:contract, company: other_company, user: other_user)
      other_event = create(:event, contract: other_contract, user: other_user)
      
      # Create an event for the user's company
      # Ensure the event is created with the correct associations
      user_event = create(:event, contract: contract, company: company, user: user, status: 'pending')
      
      # EventsController defaults to showing 'pending' events
      get "/api/v1/events", params: { status: 'pending' }, headers: { 'Authorization' => "Bearer #{valid_jwt}" }
      
      expect(response).to have_http_status(:success)
      json_response = JSON.parse(response.body)
      
      # Debug output if test fails
      if json_response.empty?
        puts "Debug: user_event status: #{user_event.status}, company_id: #{user_event.company_id}"
        puts "Debug: JWT company_id: #{company.id}"
        puts "Debug: Contract company_id: #{contract.company_id}"
      end
      
      # Should only include events from the JWT's company
      event_ids = json_response.map { |e| e['id'] }
      expect(event_ids).to include(user_event.id)
      expect(event_ids).not_to include(other_event.id)
    end
  end
  
  describe "Invalid tenant in JWT" do
    it "rejects JWT with inaccessible company_id" do
      # Create a company the user doesn't belong to
      other_company = create(:company, name: 'Unauthorized Company')
      
      # Create JWT with unauthorized company_id
      invalid_payload = { user_id: user.id, email: user.email, company_id: other_company.id }
      invalid_jwt = JwtService.encode(invalid_payload)
      
      # Attempt to access with invalid company context
      get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{invalid_jwt}" }
      
      # Should be rejected due to invalid tenant context
      expect(response).to have_http_status(:unauthorized)
      json_response = JSON.parse(response.body)
      expect(json_response['error']).to eq('Authentication required')
    end
    
    it "accepts JWT with valid company_id" do
      get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
      expect(response).to have_http_status(:success)
    end
  end
  
  describe "Error handling" do
    it "handles missing contract gracefully in DailyLogsController" do
      # Remove the contract
      contract.destroy
      
      get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
      
      expect(response).to have_http_status(:forbidden)
      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('error')
    end
    
    it "handles missing contract gracefully in EventsController" do
      contract.destroy
      
      get "/api/v1/events", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
      
      expect(response).to have_http_status(:forbidden)
      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('error')
    end
  end
end