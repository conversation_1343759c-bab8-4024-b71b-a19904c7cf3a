# spec/requests/jwt_multitenancy_integration_spec.rb
require 'rails_helper'

RSpec.describe 'JWT Multitenancy Integration Tests', type: :request do
  include AuthTestHelpers

  # Setup: Create users, companies, and roles
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  
  let(:company_a) { create(:company, name: 'Alpha Corp') }
  let(:company_b) { create(:company, name: 'Beta Inc') }
  let(:company_c) { create(:company, name: 'Gamma Ltd') }
  
  let(:owner_role) { create(:role, name: 'owner') }
  let(:employee_role) { create(:role, name: 'employee') }

  # Company A resources (user is owner)
  let!(:user_company_a_role) do
    create(:company_user_role, user: user, company: company_a, role: owner_role, is_primary: true, active: true)
  end
  let!(:company_a_contract) { create(:contract, :skip_invitation, company: company_a, user: user, status: 'active') }
  let!(:company_a_daily_log) { create(:daily_log, contract: company_a_contract, user: user, company: company_a) }
  let!(:company_a_event) do 
    create(:event, 
           contract: company_a_contract, 
           company: company_a, 
           user: user, 
           title: 'Company A Meeting',
           start_time: 1.week.from_now.beginning_of_day + 10.hours,
           end_time: 1.week.from_now.beginning_of_day + 11.hours)
  end

  # Company B resources (user is employee)
  let!(:user_company_b_role) do
    create(:company_user_role, user: user, company: company_b, role: employee_role, is_primary: false, active: true)
  end
  let!(:company_b_contract) { create(:contract, :skip_invitation, company: company_b, user: user, status: 'active') }
  let!(:company_b_daily_log) { create(:daily_log, contract: company_b_contract, user: user, company: company_b) }
  let!(:company_b_event) do
    create(:event, 
           contract: company_b_contract, 
           company: company_b, 
           user: user, 
           title: 'Company B Training',
           start_time: 2.weeks.from_now.beginning_of_day + 14.hours,
           end_time: 2.weeks.from_now.beginning_of_day + 16.hours)
  end

  # Company C resources (other_user is owner, user has no access)
  let!(:other_user_company_c_role) do
    create(:company_user_role, user: other_user, company: company_c, role: owner_role, is_primary: true, active: true)
  end
  let!(:company_c_contract) { create(:contract, :skip_invitation, company: company_c, user: other_user, status: 'active') }
  let!(:company_c_daily_log) { create(:daily_log, contract: company_c_contract, user: other_user, company: company_c) }
  let!(:company_c_event) do
    create(:event, 
           contract: company_c_contract, 
           company: company_c, 
           user: other_user, 
           title: 'Company C Strategy',
           start_time: 3.weeks.from_now.beginning_of_day + 9.hours,
           end_time: 3.weeks.from_now.beginning_of_day + 10.hours)
  end

  before do
    Redis.current.flushdb # Clean Redis for JWT revocation tests
  end

  describe 'JWT-based Company Switching End-to-End' do
    it 'allows complete company switch workflow with proper tenant isolation' do
      # Step 1: Get initial JWT for company A (primary)
      jwt_token_a = JwtService.encode_access_token(user.jwt_payload)
      auth_headers_a = { 'Authorization' => "Bearer #{jwt_token_a}" }
      
      # Verify initial JWT has company A context
      payload_a = JwtService.decode(jwt_token_a)
      expect(payload_a[:company_id]).to eq(company_a.id)
      
      # Step 2: Verify tenant isolation with company A context
      get '/api/v1/daily_logs', headers: auth_headers_a
      expect(response).to have_http_status(:ok)
      
      daily_logs_a = JSON.parse(response.body)
      expect(daily_logs_a.map { |dl| dl['id'] }).to contain_exactly(company_a_daily_log.id)
      expect(daily_logs_a.map { |dl| dl['id'] }).not_to include(company_b_daily_log.id, company_c_daily_log.id)
      
      # Step 3: Verify events isolation with company A context
      get '/api/v1/events', headers: auth_headers_a
      expect(response).to have_http_status(:ok)
      
      events_a = JSON.parse(response.body)
      event_titles_a = events_a.map { |e| e['title'] }
      expect(event_titles_a).to include('Company A Meeting')
      expect(event_titles_a).not_to include('Company B Training', 'Company C Strategy')
      
      # Step 4: Switch to company B
      post '/api/v1/companies/switch_company', 
           params: { company_id: company_b.id },
           headers: auth_headers_a
      
      expect(response).to have_http_status(:ok)
      switch_response = JSON.parse(response.body)
      expect(switch_response['success']).to be true
      expect(switch_response['company']['id']).to eq(company_b.id)
      
      jwt_token_b = switch_response['token']
      auth_headers_b = { 'Authorization' => "Bearer #{jwt_token_b}" }
      
      # Verify new JWT has company B context
      payload_b = JwtService.decode(jwt_token_b)
      expect(payload_b[:company_id]).to eq(company_b.id)
      expect(payload_b[:user_id]).to eq(user.id)
      
      # Step 5: Verify tenant isolation with company B context
      get '/api/v1/daily_logs', headers: auth_headers_b
      expect(response).to have_http_status(:ok)
      
      daily_logs_b = JSON.parse(response.body)
      expect(daily_logs_b.map { |dl| dl['id'] }).to contain_exactly(company_b_daily_log.id)
      expect(daily_logs_b.map { |dl| dl['id'] }).not_to include(company_a_daily_log.id, company_c_daily_log.id)
      
      # Step 6: Verify events isolation with company B context
      get '/api/v1/events', headers: auth_headers_b
      expect(response).to have_http_status(:ok)
      
      events_b = JSON.parse(response.body)
      event_titles_b = events_b.map { |e| e['title'] }
      expect(event_titles_b).to include('Company B Training')
      expect(event_titles_b).not_to include('Company A Meeting', 'Company C Strategy')
      
      # Step 7: Verify user's primary company was updated
      expect(user.reload.primary_company).to eq(company_b)
      
      # Step 8: Test cross-tenant access prevention through update/delete
      patch "/api/v1/daily_logs/#{company_a_daily_log.id}", 
            params: { daily_log: { description: 'Unauthorized change' } },
            headers: auth_headers_b
      expect(response).to have_http_status(:not_found)
      
      delete "/api/v1/events/#{company_a_event.id}", headers: auth_headers_b
      expect(response).to have_http_status(:not_found)
    end

    it 'prevents unauthorized company switches and maintains security' do
      jwt_token = JwtService.encode_access_token(user.jwt_payload)
      auth_headers = { 'Authorization' => "Bearer #{jwt_token}" }
      
      # Attempt to switch to company C (no access)
      post '/api/v1/companies/switch_company', 
           params: { company_id: company_c.id },
           headers: auth_headers
      
      expect(response).to have_http_status(:forbidden)
      error_response = JSON.parse(response.body)
      expect(error_response['success']).to be false
      expect(error_response['error']).to eq('Company not found or access denied')
      
      # Verify primary company wasn't changed
      expect(user.reload.primary_company).to eq(company_a)
      
      # Verify original tenant context still works
      get '/api/v1/daily_logs', headers: auth_headers
      expect(response).to have_http_status(:ok)
      
      daily_logs = JSON.parse(response.body)
      expect(daily_logs.map { |dl| dl['id'] }).to contain_exactly(company_a_daily_log.id)
    end

    it 'enforces strict tenant isolation across different resource types' do
      # Create JWT for company A
      jwt_token = JwtService.encode_access_token(user.jwt_payload.merge(company_id: company_a.id))
      auth_headers = { 'Authorization' => "Bearer #{jwt_token}" }
      
      # Test daily logs isolation
      get '/api/v1/daily_logs', headers: auth_headers
      daily_logs = JSON.parse(response.body)
      expect(daily_logs.map { |dl| dl['id'] }).to contain_exactly(company_a_daily_log.id)
      
      # Test events isolation
      get '/api/v1/events', headers: auth_headers
      events = JSON.parse(response.body)
      expect(events.map { |e| e['title'] }).to include('Company A Meeting')
      expect(events.map { |e| e['title'] }).not_to include('Company B Training', 'Company C Strategy')
      
      # Test cross-tenant access prevention through modify/delete actions
      patch "/api/v1/daily_logs/#{company_b_daily_log.id}", 
            params: { daily_log: { description: 'Unauthorized change' } },
            headers: auth_headers
      expect(response).to have_http_status(:not_found)
      
      # Verify no change was made
      expect(company_b_daily_log.reload.description).not_to eq('Unauthorized change')
      
      # Test deletion prevention
      delete "/api/v1/events/#{company_c_event.id}", headers: auth_headers
      expect(response).to have_http_status(:not_found)
      
      # Verify event still exists
      expect(Event.exists?(company_c_event.id)).to be true
    end

    it 'handles JWT token chains correctly' do
      # Initial token for company A
      token_a1 = JwtService.encode_access_token(user.jwt_payload.merge(company_id: company_a.id))
      headers_a1 = { 'Authorization' => "Bearer #{token_a1}" }
      
      # Switch A → B
      post '/api/v1/companies/switch_company', 
           params: { company_id: company_b.id },
           headers: headers_a1
      
      token_b = JSON.parse(response.body)['token']
      headers_b = { 'Authorization' => "Bearer #{token_b}" }
      
      # Verify B context
      get '/api/v1/events', headers: headers_b
      events_b = JSON.parse(response.body)
      expect(events_b.map { |e| e['title'] }).to include('Company B Training')
      expect(events_b.map { |e| e['title'] }).not_to include('Company A Meeting')
      
      # Switch B → A using new token
      post '/api/v1/companies/switch_company', 
           params: { company_id: company_a.id },
           headers: headers_b
      
      token_a2 = JSON.parse(response.body)['token']
      headers_a2 = { 'Authorization' => "Bearer #{token_a2}" }
      
      # Verify back to A context
      get '/api/v1/events', headers: headers_a2
      events_a = JSON.parse(response.body)
      expect(events_a.map { |e| e['title'] }).to include('Company A Meeting')
      expect(events_a.map { |e| e['title'] }).not_to include('Company B Training')
      
      # All tokens should be different
      expect([token_a1, token_b, token_a2].uniq.length).to eq(3)
      
      # Old tokens retain their original tenant context
      get '/api/v1/events', headers: headers_a1
      old_events = JSON.parse(response.body)
      expect(old_events.map { |e| e['title'] }).to include('Company A Meeting')
    end

    it 'validates company access with inactive roles' do
      # Deactivate user's role in company B
      user_company_b_role.update!(active: false)
      
      jwt_token = JwtService.encode_access_token(user.jwt_payload)
      auth_headers = { 'Authorization' => "Bearer #{jwt_token}" }
      
      # Attempt to switch to company B (inactive role)
      post '/api/v1/companies/switch_company', 
           params: { company_id: company_b.id },
           headers: auth_headers
      
      expect(response).to have_http_status(:forbidden)
      error_response = JSON.parse(response.body)
      expect(error_response['error']).to eq('Company not found or access denied')
      
      # Verify company list excludes inactive role companies
      get '/api/v1/companies', headers: auth_headers
      company_roles = JSON.parse(response.body)['company_user_roles']
      company_ids = company_roles.map { |cur| cur['company']['id'] }
      expect(company_ids).to contain_exactly(company_a.id) # Only active role
    end

    it 'handles edge cases and error scenarios' do
      # Test with non-existent company ID
      jwt_token = JwtService.encode_access_token(user.jwt_payload)
      auth_headers = { 'Authorization' => "Bearer #{jwt_token}" }
      
      post '/api/v1/companies/switch_company', 
           params: { company_id: 999999 },
           headers: auth_headers
      
      expect(response).to have_http_status(:forbidden)
      error_response = JSON.parse(response.body)
      expect(error_response['error']).to eq('Company not found or access denied')
      
      # Test with missing company_id parameter
      post '/api/v1/companies/switch_company', 
           params: {},
           headers: auth_headers
      
      expect(response).to have_http_status(:bad_request)
      error_response = JSON.parse(response.body)
      expect(error_response['error']).to eq('Company ID is required')
    end
  end

  describe 'Security Event Monitoring and Logging' do
    it 'logs security events for unauthorized access attempts' do
      jwt_token = JwtService.encode_access_token(user.jwt_payload.merge(company_id: company_a.id))
      auth_headers = { 'Authorization' => "Bearer #{jwt_token}" }
      
      # Use spy to verify security event logging
      allow(AuthHealthCheck).to receive(:log_security_event)
      
      # Attempt cross-tenant access
      patch "/api/v1/daily_logs/#{company_c_daily_log.id}", 
            params: { daily_log: { description: 'Hacked!' } },
            headers: auth_headers
      expect(response).to have_http_status(:not_found)
      
      # Verify security events were logged (tenant context setting and potential authorization failures)
      expect(AuthHealthCheck).to have_received(:log_security_event).at_least(:once)
      
      # More specifically, verify tenant context was set (which is the actual security event logged)
      expect(AuthHealthCheck).to have_received(:log_security_event).with(
        'tenant_context_set',
        hash_including(:severity, :user_id, :company_id)
      )
    end

    it 'tracks successful company switch attempts in logs' do
      jwt_token = JwtService.encode_access_token(user.jwt_payload)
      auth_headers = { 'Authorization' => "Bearer #{jwt_token}" }
      
      # Successful switch should be logged
      allow(Rails.logger).to receive(:info)
      post '/api/v1/companies/switch_company', 
           params: { company_id: company_b.id },
           headers: auth_headers
      expect(response).to have_http_status(:ok)
      expect(Rails.logger).to have_received(:info).with(/Company switch/)
      
      # Failed switch returns 403 but may not specifically log the attempt
      post '/api/v1/companies/switch_company', 
           params: { company_id: company_c.id },
           headers: auth_headers
      expect(response).to have_http_status(:forbidden)
    end
  end

  describe 'Dual Authentication Compatibility' do
    it 'maintains compatibility with session authentication fallback' do
      # Test with session authentication when JWT fails
      sign_in user
      
      post '/api/v1/companies/switch_company', 
           params: { company_id: company_b.id }
      
      expect(response).to have_http_status(:ok)
      switch_response = JSON.parse(response.body)
      expect(switch_response['success']).to be true
      expect(switch_response['company']['id']).to eq(company_b.id)
    end

    it 'works with expired JWT tokens by falling back to session' do
      # Create expired JWT
      expired_payload = user.jwt_payload.merge(exp: 1.hour.ago.to_i)
      expired_token = JWT.encode(expired_payload, Rails.application.credentials.jwt_secret, 'HS256')
      expired_headers = { 'Authorization' => "Bearer #{expired_token}" }
      
      # Set up session fallback
      sign_in user
      
      post '/api/v1/companies/switch_company', 
           params: { company_id: company_b.id },
           headers: expired_headers
      
      expect(response).to have_http_status(:ok)
      switch_response = JSON.parse(response.body)
      expect(switch_response['success']).to be true
    end
  end

  describe 'Performance and Concurrent Access' do
    it 'maintains proper tenant context isolation between different JWT tokens' do
      token_a = JwtService.encode_access_token(user.jwt_payload.merge(company_id: company_a.id))
      token_b = JwtService.encode_access_token(user.jwt_payload.merge(company_id: company_b.id))
      
      # Test sequential requests with different tokens to verify tenant context isolation
      headers_a = { 'Authorization' => "Bearer #{token_a}" }
      get '/api/v1/events', headers: headers_a
      events_a = JSON.parse(response.body)
      expect(events_a.map { |e| e['title'] }).to include('Company A Meeting')
      expect(events_a.map { |e| e['title'] }).not_to include('Company B Training')
      
      headers_b = { 'Authorization' => "Bearer #{token_b}" }
      get '/api/v1/events', headers: headers_b
      events_b = JSON.parse(response.body)
      expect(events_b.map { |e| e['title'] }).to include('Company B Training')
      expect(events_b.map { |e| e['title'] }).not_to include('Company A Meeting')
    end
  end

  describe 'Resource Creation with Tenant Context' do
    it 'creates resources with correct tenant context from JWT' do
      # Use company B context
      jwt_token = JwtService.encode_access_token(user.jwt_payload.merge(company_id: company_b.id))
      auth_headers = { 'Authorization' => "Bearer #{jwt_token}" }
      
      # Create a new daily log (controller uses current time automatically)
      post '/api/v1/daily_logs', 
           params: {},
           headers: auth_headers
      
      expect(response).to have_http_status(:ok)
      new_log_response = JSON.parse(response.body)
      
      # Verify it was created for company B
      if new_log_response['daily_log']
        created_log = DailyLog.find(new_log_response['daily_log']['id'])
        expect(created_log.company_id).to eq(company_b.id)
        expect(created_log.user_id).to eq(user.id)
        expect(created_log.contract_id).to eq(company_b_contract.id)
      end
    end
  end
end