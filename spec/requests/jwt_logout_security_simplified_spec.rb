# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'JWT Logout Security Improvements - Functional Tests', type: :request do
  let(:user) { create(:user, password: 'password', password_confirmation: 'password') }
  let(:company) { create(:company) }
  let(:user_company_role) { create(:company_user_role, user: user, company: company, is_primary: true) }
  
  before do
    user_company_role
    user.reload
    
    # Clear Redis before each test
    begin
      Redis.current.flushdb if Rails.env.test?
    rescue => e
      Rails.logger.warn "Could not clear Redis in test: #{e.message}"
    end
  end

  describe 'POST /api/v1/auth/jwt_logout - Security Improvements' do
    context 'with valid access and refresh tokens' do
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:refresh_token) { JwtService.encode_refresh_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      before do
        cookies[:refresh_token] = refresh_token
      end

      it 'successfully revokes both tokens and returns success' do
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['success']).to be true
      end

      it 'revokes access token (token should be invalid after logout)' do
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        # Verify token is revoked by checking revocation strategy
        strategy = JwtRevocationStrategy.new
        payload = JwtService.decode(access_token)
        expect(strategy.jwt_revoked?(payload, user)).to be true
      end

      it 'revokes refresh token (token should be invalid after logout)' do
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        # Verify refresh token is revoked
        strategy = JwtRevocationStrategy.new
        payload = JwtService.decode(refresh_token)
        expect(strategy.jwt_revoked?(payload, user)).to be true
      end
    end

    context 'with mismatched user IDs between tokens' do
      let(:other_user) { create(:user) }
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:refresh_token) { JwtService.encode_refresh_token(other_user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      before do
        cookies[:refresh_token] = refresh_token
      end

      it 'still returns success (security best practice)' do
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['success']).to be true
      end

      it 'revokes the access token for the primary user' do
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        strategy = JwtRevocationStrategy.new
        access_payload = JwtService.decode(access_token)
        expect(strategy.jwt_revoked?(access_payload, user)).to be true
      end

      it 'does not revoke the refresh token due to user mismatch' do
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        strategy = JwtRevocationStrategy.new
        refresh_payload = JwtService.decode(refresh_token)
        expect(strategy.jwt_revoked?(refresh_payload, other_user)).to be false
      end
    end

    context 'with only access token (no refresh token)' do
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      it 'successfully revokes access token only' do
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        expect(response).to have_http_status(:ok)
        
        strategy = JwtRevocationStrategy.new
        payload = JwtService.decode(access_token)
        expect(strategy.jwt_revoked?(payload, user)).to be true
      end
    end

    context 'with only refresh token (no access token)' do
      let(:refresh_token) { JwtService.encode_refresh_token(user.jwt_payload) }

      before do
        cookies[:refresh_token] = refresh_token
      end

      it 'successfully revokes refresh token only' do
        post '/api/v1/auth/jwt_logout'
        
        expect(response).to have_http_status(:ok)
        
        strategy = JwtRevocationStrategy.new
        payload = JwtService.decode(refresh_token)
        expect(strategy.jwt_revoked?(payload, user)).to be true
      end
    end

    context 'with invalid tokens' do
      let(:invalid_token) { 'invalid.jwt.token' }
      let(:auth_headers) { { 'Authorization' => "Bearer #{invalid_token}" } }

      before do
        cookies[:refresh_token] = 'invalid_refresh_token'
      end

      it 'returns success without revealing invalid token information' do
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['success']).to be true
      end
    end

    context 'with no tokens provided' do
      it 'returns success (security best practice - no info disclosure)' do
        post '/api/v1/auth/jwt_logout'
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['success']).to be true
      end
    end

    context 'single user resolution verification' do
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:refresh_token) { JwtService.encode_refresh_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      before do
        cookies[:refresh_token] = refresh_token
      end

      it 'uses single User.find_by call for efficiency' do
        expect(User).to receive(:find_by).once.with(id: user.id).and_return(user)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end

      it 'uses single revocation strategy instance' do
        strategy_instance = instance_double(JwtRevocationStrategy)
        allow(JwtRevocationStrategy).to receive(:new).once.and_return(strategy_instance)
        
        expect(strategy_instance).to receive(:revoke_jwt).twice
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end
    end

    context 'cookie cleanup verification' do
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:refresh_token) { JwtService.encode_refresh_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      before do
        cookies[:refresh_token] = refresh_token
      end

      it 'clears refresh token cookie regardless of revocation outcome' do
        # Mock revocation to fail
        allow_any_instance_of(JwtRevocationStrategy).to receive(:revoke_jwt).and_raise(StandardError)
        
        expect(SecureCookieHelper).to receive(:delete_refresh_token_cookie)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end
    end

    context 'error resilience verification' do
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      it 'still returns success even when revocation fails' do
        allow_any_instance_of(JwtRevocationStrategy).to receive(:revoke_jwt).and_raise(StandardError, "Redis failure")
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['success']).to be true
      end

      it 'performs cookie cleanup even when token revocation fails' do
        allow_any_instance_of(JwtRevocationStrategy).to receive(:revoke_jwt).and_raise(StandardError)
        
        expect(SecureCookieHelper).to receive(:delete_refresh_token_cookie)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end
    end
  end
end