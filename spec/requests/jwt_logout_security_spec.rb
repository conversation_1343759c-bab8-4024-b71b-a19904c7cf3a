# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'JWT Logout Security Improvements', type: :request do
  let(:user) { create(:user, password: 'password', password_confirmation: 'password') }
  let(:company) { create(:company) }
  let(:user_company_role) { create(:company_user_role, user: user, company: company, is_primary: true) }
  
  before do
    user_company_role
    user.reload
    
    # Clear Redis before each test
    begin
      Redis.current.flushdb if Rails.env.test?
    rescue => e
      Rails.logger.warn "Could not clear Redis in test: #{e.message}"
    end
  end

  describe 'POST /api/v1/auth/jwt_logout' do
    context 'with valid access and refresh tokens' do
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:refresh_token) { JwtService.encode_refresh_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      before do
        cookies[:refresh_token] = refresh_token
      end

      it 'successfully revokes both tokens with single user resolution' do
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] JWT logout attempt - User: #{user.id}/)
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] Access token revoked for user #{user.id}/)
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] Refresh token revoked for user #{user.id}/)
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] JWT logout completed successfully/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['success']).to be true
      end

      it 'logs comprehensive security audit trail' do
        expect(Rails.logger).to receive(:info).with(/IP: #{request.remote_ip}/)
        expect(Rails.logger).to receive(:info).with(/User-Agent:/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end

      it 'clears all cookies and sessions' do
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] Refresh token cookie cleared/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end
    end

    context 'with mismatched user IDs between tokens' do
      let(:other_user) { create(:user) }
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:refresh_token) { JwtService.encode_refresh_token(other_user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      before do
        cookies[:refresh_token] = refresh_token
      end

      it 'detects and logs user ID mismatch security issue' do
        expect(Rails.logger).to receive(:error).with(/\[SECURITY\] User ID mismatch in refresh token during logout/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        expect(response).to have_http_status(:ok)
      end

      it 'still revokes the access token for the primary user' do
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] Access token revoked for user #{user.id}/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end

      it 'does not revoke the refresh token due to user mismatch' do
        expect(Rails.logger).not_to receive(:info).with(/\[SECURITY\] Refresh token revoked/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end
    end

    context 'with only access token (no refresh token)' do
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      it 'successfully revokes access token only' do
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] Access token revoked for user #{user.id}/)
        expect(Rails.logger).to receive(:info).with(/Tokens revoked: access=true, refresh=false/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with only refresh token (no access token)' do
      let(:refresh_token) { JwtService.encode_refresh_token(user.jwt_payload) }

      before do
        cookies[:refresh_token] = refresh_token
      end

      it 'successfully revokes refresh token only' do
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] Refresh token revoked for user #{user.id}/)
        expect(Rails.logger).to receive(:info).with(/Tokens revoked: access=false, refresh=true/)
        
        post '/api/v1/auth/jwt_logout'
        
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with invalid tokens' do
      let(:invalid_token) { 'invalid.jwt.token' }
      let(:auth_headers) { { 'Authorization' => "Bearer #{invalid_token}" } }

      before do
        cookies[:refresh_token] = 'invalid_refresh_token'
      end

      it 'logs security warning for logout without valid user context' do
        expect(Rails.logger).to receive(:warn).with(/\[SECURITY\] JWT logout attempt without valid user context/)
        expect(Rails.logger).to receive(:warn).with(/\[SECURITY\] JWT logout completed without token revocation/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        expect(response).to have_http_status(:ok)
      end

      it 'still performs cookie cleanup for security' do
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] Refresh token cookie cleared/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end
    end

    context 'with no tokens provided' do
      it 'logs security warning and performs cleanup' do
        expect(Rails.logger).to receive(:warn).with(/\[SECURITY\] JWT logout attempt without valid user context/)
        expect(Rails.logger).to receive(:warn).with(/\[SECURITY\] JWT logout completed without token revocation/)
        
        post '/api/v1/auth/jwt_logout'
        
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when token revocation raises an error' do
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      before do
        allow_any_instance_of(JwtRevocationStrategy).to receive(:revoke_jwt).and_raise(StandardError, "Redis connection failed")
      end

      it 'logs enhanced error with security context' do
        expect(Rails.logger).to receive(:error).with(/\[SECURITY\] Error during token revocation in logout: StandardError - Redis connection failed/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
        
        expect(response).to have_http_status(:ok)
      end

      it 'still performs cookie cleanup in ensure block' do
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] Refresh token cookie cleared/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end
    end

    context 'single strategy instance verification' do
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:refresh_token) { JwtService.encode_refresh_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      before do
        cookies[:refresh_token] = refresh_token
      end

      it 'uses single revocation strategy instance for both tokens' do
        strategy_instance = instance_double(JwtRevocationStrategy)
        allow(JwtRevocationStrategy).to receive(:new).once.and_return(strategy_instance)
        
        expect(strategy_instance).to receive(:revoke_jwt).twice
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end
    end

    context 'comprehensive security logging verification' do
      let(:access_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{access_token}" } }

      it 'includes IP address in all security logs' do
        expect(Rails.logger).to receive(:info).with(/IP: #{Regexp.escape(request.remote_ip)}/).at_least(:once)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end

      it 'includes User-Agent in security logs' do
        expect(Rails.logger).to receive(:info).with(/User-Agent:/).at_least(:once)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end

      it 'logs successful logout with token status summary' do
        expect(Rails.logger).to receive(:info).with(/\[SECURITY\] JWT logout completed successfully.*Tokens revoked: access=true, refresh=false/)
        
        post '/api/v1/auth/jwt_logout', headers: auth_headers
      end
    end
  end
end