# spec/requests/jwt_request_metrics_spec.rb
require 'rails_helper'

RSpec.describe "JWT Request Metrics Tracking", type: :request do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let!(:company_user_role) { create(:company_user_role, user: user, company: company, is_primary: true) }
  let!(:contract) { create(:contract, user: user, company: company) }
  
  # JWT setup
  let(:valid_payload) { { user_id: user.id, email: user.email, company_id: company.id } }
  let(:valid_jwt) { JwtService.encode(valid_payload) }
  
  before do
    # Mock User#jwt_payload to include company_id
    allow_any_instance_of(User).to receive(:jwt_payload).and_return(valid_payload)
    
    # Set up a clean metrics state for testing
    @test_metrics = { 'events' => [] }
    allow_any_instance_of(AuthHealthCheck).to receive(:load_metrics).and_return(@test_metrics)
    allow_any_instance_of(AuthHealthCheck).to receive(:save_metrics).and_return(true)
  end
  
  describe "JWT authentication metrics" do
    it "tracks JWT requests in AuthHealthCheck" do
      expect(AuthHealthCheck).to receive(:increment_jwt_request_count).once
      
      get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
      
      expect(response).to have_http_status(:success)
    end
    
    it "increments JWT request count for JWT authenticated requests" do
      # Mock the increment method and verify it's called
      expect(AuthHealthCheck).to receive(:increment_jwt_request_count).once
      
      get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
      expect(response).to have_http_status(:success)
    end
    
    it "calculates JWT adoption rate correctly with JWT requests" do
      # Simulate having JWT request events in metrics
      jwt_event = {
        'type' => 'jwt_request',
        'timestamp' => Time.current.iso8601,
        'success' => true
      }
      @test_metrics['events'] = [jwt_event]
      
      auth_health_check = AuthHealthCheck.new
      adoption_rate = auth_health_check.jwt_adoption_rate
      
      # Should be 100% since we only have JWT requests
      expect(adoption_rate).to eq(100.0)
    end
  end
  
  describe "Metrics calculation" do
    it "calculates JWT adoption rate correctly with mixed request events" do
      # Simulate having both JWT and session request events
      jwt_event = {
        'type' => 'jwt_request',
        'timestamp' => Time.current.iso8601,
        'success' => true
      }
      session_event = {
        'type' => 'session_request',
        'timestamp' => Time.current.iso8601,
        'success' => true
      }
      @test_metrics['events'] = [jwt_event, session_event]
      
      auth_health_check = AuthHealthCheck.new
      adoption_rate = auth_health_check.jwt_adoption_rate
      
      # Should be 50% (1 JWT request out of 2 total auth requests)
      expect(adoption_rate).to eq(50.0)
    end
    
    it "returns 0% adoption rate when no auth events exist" do
      @test_metrics['events'] = []
      
      auth_health_check = AuthHealthCheck.new
      adoption_rate = auth_health_check.jwt_adoption_rate
      
      expect(adoption_rate).to eq(0.0)
    end
    
    it "counts JWT requests correctly" do
      jwt_event = {
        'type' => 'jwt_request',
        'timestamp' => 2.hours.ago.iso8601,
        'success' => true
      }
      @test_metrics['events'] = [jwt_event]
      
      auth_health_check = AuthHealthCheck.new
      jwt_count = auth_health_check.jwt_request_count
      
      expect(jwt_count).to eq(1)
    end
    
    it "counts session requests correctly" do
      session_event = {
        'type' => 'session_request',
        'timestamp' => 2.hours.ago.iso8601,
        'success' => true
      }
      @test_metrics['events'] = [session_event]
      
      auth_health_check = AuthHealthCheck.new
      session_count = auth_health_check.session_request_count
      
      expect(session_count).to eq(1)
    end
  end
  
  describe "Unauthenticated requests" do
    it "does not track metrics for unauthenticated requests" do
      expect(AuthHealthCheck).not_to receive(:increment_jwt_request_count)
      expect(AuthHealthCheck).not_to receive(:increment_session_request_count)
      
      get "/api/v1/daily_logs"
      
      expect(response).to have_http_status(:unauthorized)
    end
  end
  
  describe "Error handling in metrics tracking" do
    it "continues processing request even if metrics tracking fails" do
      allow(AuthHealthCheck).to receive(:increment_jwt_request_count).and_raise(StandardError.new("Metrics error"))
      
      # Request should still succeed despite metrics error
      get "/api/v1/daily_logs", headers: { 'Authorization' => "Bearer #{valid_jwt}" }
      
      expect(response).to have_http_status(:success)
    end
  end
end