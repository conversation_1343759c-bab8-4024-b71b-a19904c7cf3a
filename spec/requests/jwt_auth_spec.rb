# spec/requests/jwt_auth_spec.rb
require 'rails_helper'

RSpec.describe 'JWT Authentication', type: :request do
  let(:user) { create(:user, password: 'password123') }
  let(:company) { create(:company) }
  
  before do
    create(:company_user_role, user: user, company: company, is_primary: true)
  end
  
  describe 'POST /api/v1/auth/jwt_login' do
    let(:valid_credentials) do
      {
        email: user.email,
        password: 'password123'
      }
    end
    
    let(:invalid_credentials) do
      {
        email: user.email,
        password: 'wrong_password'
      }
    end
    
    context 'with valid credentials' do
      before do
        post '/api/v1/auth/jwt_login', params: valid_credentials
      end
      
      it 'returns success status' do
        expect(response).to have_http_status(:ok)
      end
      
      it 'returns access token' do
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['access_token']).to be_present
      end
      
      it 'sets refresh token in HttpOnly cookie' do
        # Refresh token is stored in <PERSON>ttpOnly cookie for security (Chunk 32)
        expect(response.cookies['refresh_token']).to be_present
      end
      
      it 'returns token expiration time' do
        json_response = JSON.parse(response.body)
        expect(json_response['expires_in']).to eq(JwtService::ACCESS_TOKEN_EXPIRATION_DURATION.to_i)
      end
      
      it 'returns user information' do
        json_response = JSON.parse(response.body)
        expect(json_response['user']).to include(
          'id' => user.id,
          'email' => user.email,
          'company_id' => company.id
        )
      end
      
      it 'does NOT create a session' do
        expect(session[:user_id]).to be_nil
      end
      
      it 'returns valid JWT that can be decoded' do
        json_response = JSON.parse(response.body)
        token = json_response['access_token']
        
        decoded = JwtService.decode(token)
        expect(decoded).to include(
          'user_id' => user.id,
          'email' => user.email,
          'company_id' => company.id
        )
        expect(decoded['jti']).to be_present
      end
      
      it 'logs successful authentication to AuthHealthCheck' do
        # Allow for the api_request log event from ApiController
        allow(AuthHealthCheck).to receive(:log_auth_event).with(
          'api_request',
          anything
        )
        
        expect(AuthHealthCheck).to receive(:log_auth_event).with(
          'jwt_login',
          hash_including(success: true, duration_ms: kind_of(Integer))
        )
        
        post '/api/v1/auth/jwt_login', params: valid_credentials
      end
    end
    
    context 'with invalid credentials' do
      before do
        post '/api/v1/auth/jwt_login', params: invalid_credentials
      end
      
      it 'returns unauthorized status' do
        expect(response).to have_http_status(:unauthorized)
      end
      
      it 'returns error message' do
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Invalid email or password')
      end
      
      it 'does not return tokens' do
        json_response = JSON.parse(response.body)
        expect(json_response['access_token']).to be_nil
        expect(json_response['refresh_token']).to be_nil
      end
      
      it 'logs failed authentication to AuthHealthCheck' do
        # Allow for the api_request log event from ApiController
        allow(AuthHealthCheck).to receive(:log_auth_event).with(
          'api_request',
          anything
        )
        
        expect(AuthHealthCheck).to receive(:log_auth_event).with(
          'jwt_login',
          hash_including(success: false, duration_ms: kind_of(Integer), error: 'Invalid credentials')
        )
        
        post '/api/v1/auth/jwt_login', params: invalid_credentials
      end
    end
    
    context 'with non-existent user' do
      it 'returns unauthorized status' do
        post '/api/v1/auth/jwt_login', params: { email: '<EMAIL>', password: 'password' }
        
        expect(response).to have_http_status(:unauthorized)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Invalid email or password')
      end
    end
    
    context 'with missing parameters' do
      it 'returns unauthorized when email is missing' do
        post '/api/v1/auth/jwt_login', params: { password: 'password123' }
        
        expect(response).to have_http_status(:unauthorized)
      end
      
      it 'returns unauthorized when password is missing' do
        post '/api/v1/auth/jwt_login', params: { email: user.email }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
  
  describe 'POST /api/v1/auth/jwt_logout' do
    let(:valid_jwt) do
      payload = user.jwt_payload
      JwtService.encode_access_token(payload)
    end
    
    let(:headers_with_token) do
      { 'Authorization' => "Bearer #{valid_jwt}" }
    end
    
    let(:headers_without_token) do
      {}
    end
    
    context 'with valid JWT token' do
      it 'returns success status' do
        post '/api/v1/auth/jwt_logout', headers: headers_with_token
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
      
      it 'revokes the token' do
        # First decode to get the payload
        payload = JwtService.decode(valid_jwt)
        
        # Make the logout request
        post '/api/v1/auth/jwt_logout', headers: headers_with_token
        
        # Check that the token is revoked
        strategy = JwtRevocationStrategy.new
        expect(strategy.jwt_revoked?(payload, user)).to be true
      end
      
      it 'logs successful logout to AuthHealthCheck' do
        # Allow for the api_request log event from ApiController
        allow(AuthHealthCheck).to receive(:log_auth_event).with(
          'api_request',
          anything
        )
        
        expect(AuthHealthCheck).to receive(:log_auth_event).with(
          'jwt_logout',
          hash_including(success: true)
        )
        
        post '/api/v1/auth/jwt_logout', headers: headers_with_token
      end
    end
    
    context 'with invalid JWT token' do
      it 'still returns success (security best practice)' do
        headers = { 'Authorization' => 'Bearer invalid.jwt.token' }
        post '/api/v1/auth/jwt_logout', headers: headers
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
    end
    
    context 'with expired JWT token' do
      it 'still returns success (security best practice)' do
        # Create an expired token
        payload = user.jwt_payload
        expired_jwt = JwtService.encode(payload, 1.second.ago)
        headers = { 'Authorization' => "Bearer #{expired_jwt}" }
        
        post '/api/v1/auth/jwt_logout', headers: headers
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
    end
    
    context 'without Authorization header' do
      it 'returns success (security best practice)' do
        post '/api/v1/auth/jwt_logout', headers: headers_without_token
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
    end
    
    context 'with malformed Authorization header' do
      it 'returns success when header does not start with Bearer' do
        headers = { 'Authorization' => 'Token 12345' }
        post '/api/v1/auth/jwt_logout', headers: headers
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
      
      it 'returns success when header is just Bearer without token' do
        headers = { 'Authorization' => 'Bearer' }
        post '/api/v1/auth/jwt_logout', headers: headers
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
    end
    
    context 'when token is already revoked' do
      it 'still returns success' do
        # First revoke the token
        payload = JwtService.decode(valid_jwt)
        strategy = JwtRevocationStrategy.new
        strategy.revoke_jwt(payload, user)
        
        # Try to logout again
        post '/api/v1/auth/jwt_logout', headers: headers_with_token
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
    end
  end
  
  describe 'POST /api/v1/auth/refresh_token' do
    let(:user_payload) { user.jwt_payload }
    let(:valid_refresh_token) { JwtService.encode_refresh_token(user_payload) }
    
    context 'with valid refresh token in request body' do
      before do
        post '/api/v1/auth/refresh_token', params: { refresh_token: valid_refresh_token }
      end
      
      it 'returns success status' do
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
      
      it 'returns new access token' do
        json_response = JSON.parse(response.body)
        expect(json_response['access_token']).to be_present
        expect(json_response['access_token']).not_to eq(valid_refresh_token)
      end
      
      it 'rotates refresh token in HttpOnly cookie' do
        # New refresh token is stored in HttpOnly cookie for security (Chunk 32)
        expect(response.cookies['refresh_token']).to be_present
        # Note: Can't compare cookie value directly as it's HttpOnly
      end
      
      it 'returns access token expiration time' do
        json_response = JSON.parse(response.body)
        expect(json_response['expires_in']).to eq(JwtService::ACCESS_TOKEN_EXPIRATION_DURATION.to_i)
      end
      
      it 'returns user information' do
        json_response = JSON.parse(response.body)
        expect(json_response['user']).to include(
          'id' => user.id,
          'email' => user.email,
          'company_id' => company.id
        )
      end
      
      it 'revokes the old refresh token' do
        old_payload = JwtService.decode(valid_refresh_token)
        strategy = JwtRevocationStrategy.new
        expect(strategy.jwt_revoked?(old_payload, user)).to be true
      end
      
    end
    
    context 'logging to AuthHealthCheck' do
      it 'logs successful token refresh' do
        # Allow for the api_request log event from ApiController
        allow(AuthHealthCheck).to receive(:log_auth_event).with(
          'api_request',
          anything
        )
        
        expect(AuthHealthCheck).to receive(:log_auth_event).with(
          'jwt_refresh',
          hash_including(success: true)
        )
        
        # Create a fresh token for this test
        fresh_token = JwtService.encode_refresh_token(user.jwt_payload)
        post '/api/v1/auth/refresh_token', params: { refresh_token: fresh_token }
      end
    end
    
    context 'with valid refresh token in Authorization header' do
      it 'returns new tokens when using Authorization header' do
        headers = { 'Authorization' => "Bearer #{valid_refresh_token}" }
        post '/api/v1/auth/refresh_token', headers: headers
        
        expect(response).to have_http_status(:ok)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['access_token']).to be_present
        # Refresh token is in HttpOnly cookie, not in JSON response
        expect(response.cookies['refresh_token']).to be_present
      end
    end
    
    context 'with invalid refresh token' do
      it 'returns unauthorized for invalid token' do
        post '/api/v1/auth/refresh_token', params: { refresh_token: 'invalid.token.here' }
        
        expect(response).to have_http_status(:unauthorized)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Invalid or expired refresh token')
      end
    end
    
    context 'with expired refresh token' do
      it 'returns unauthorized for expired token' do
        expired_token = JwtService.encode(user_payload.merge(token_type: 'refresh'), 1.second.ago)
        post '/api/v1/auth/refresh_token', params: { refresh_token: expired_token }
        
        expect(response).to have_http_status(:unauthorized)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Invalid or expired refresh token')
      end
    end
    
    context 'with access token instead of refresh token' do
      it 'returns unauthorized for wrong token type' do
        access_token = JwtService.encode_access_token(user_payload)
        post '/api/v1/auth/refresh_token', params: { refresh_token: access_token }
        
        expect(response).to have_http_status(:unauthorized)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Invalid token type')
      end
    end
    
    context 'with revoked refresh token' do
      it 'returns unauthorized for revoked token' do
        # First revoke the token
        payload = JwtService.decode(valid_refresh_token)
        strategy = JwtRevocationStrategy.new
        strategy.revoke_jwt(payload, user)
        
        post '/api/v1/auth/refresh_token', params: { refresh_token: valid_refresh_token }
        
        expect(response).to have_http_status(:unauthorized)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Refresh token has been revoked')
      end
    end
    
    context 'without refresh token' do
      it 'returns unauthorized when no token provided' do
        post '/api/v1/auth/refresh_token'
        
        expect(response).to have_http_status(:unauthorized)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Refresh token required')
      end
    end
    
    context 'with non-existent user' do
      it 'returns unauthorized when user not found' do
        invalid_payload = user_payload.merge(user_id: 99999, token_type: 'refresh')
        invalid_token = JwtService.encode_refresh_token(invalid_payload)
        
        post '/api/v1/auth/refresh_token', params: { refresh_token: invalid_token }
        
        expect(response).to have_http_status(:unauthorized)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('User not found')
      end
    end
  end
end