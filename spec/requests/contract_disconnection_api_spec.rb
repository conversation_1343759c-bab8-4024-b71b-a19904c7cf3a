# ABOUTME: API tests for contract disconnection issues - validation and unauthorized reconnection
# ABOUTME: Ensures terminated contracts are handled correctly in validation and role updates

require 'rails_helper'

RSpec.describe 'Contract Disconnection API', type: :request do
  let(:company) { create(:company) }
  let(:owner) { create(:user) }
  let(:employee_user) { create(:user, email: '<EMAIL>') }
  let!(:owner_role) { Role.find_or_create_by(name: 'owner') }
  let!(:employee_role) { Role.find_or_create_by(name: 'employee') }
  let(:jwt_token) { generate_jwt_token_for(owner) }
  let(:headers) { { 'Authorization' => "Bearer #{jwt_token}", 'Accept' => 'application/json' } }
  
  before do
    ActsAsTenant.current_tenant = company
    CompanyUserRole.create!(user: owner, company: company, role: owner_role)
  end

  describe 'Email Validation with Terminated Contracts' do
    context 'when a contract is terminated' do
      let!(:terminated_contract) do
        create(:contract, 
          company: company, 
          email: '<EMAIL>',
          status: 'terminated'
        )
      end

      it 'allows creating a new contract with the same email' do
        new_contract = Contract.new(
          company: company,
          email: '<EMAIL>',
          first_name: '<PERSON>',
          last_name: '<PERSON><PERSON>',
          status: 'active'
        )
        
        expect(new_contract).to be_valid
        expect(new_contract.save).to be true
      end

      it 'prevents duplicate active contracts with same email' do
        active_contract = create(:contract, 
          company: company, 
          email: '<EMAIL>',
          status: 'active'
        )

        duplicate_contract = Contract.new(
          company: company,
          email: '<EMAIL>',
          first_name: 'Jane',
          last_name: 'Smith',
          status: 'active'
        )
        
        expect(duplicate_contract).not_to be_valid
        expect(duplicate_contract.errors[:email]).to be_present
      end
    end
  end

  describe 'Role Updates on Terminated Contracts' do
    let!(:contract) { create(:contract, company: company, user: employee_user) }
    
    context 'when contract is terminated' do
      before { contract.terminate! }

      it 'prevents role updates via API' do
        post "/contracts/#{contract.id}/update_role",
          params: { role_name: 'employee' },
          headers: headers
        
        expect(response).to have_http_status(:unprocessable_entity)
        
        json = JSON.parse(response.body)
        expect(json['success']).to be false
        expect(json['message']).to eq('Nelze upravit ukončený kontrakt.')
        
        # Verify no CompanyUserRole was created
        expect(CompanyUserRole.where(user: employee_user, company: company).count).to eq(0)
      end
    end

    context 'when contract is active' do
      before { contract.update!(status: 'active') }

      it 'allows role updates via API' do
        post "/contracts/#{contract.id}/update_role",
          params: { role_name: 'employee' },
          headers: headers
        
        expect(response).to have_http_status(:ok)
        
        json = JSON.parse(response.body)
        expect(json['success']).to be true
        
        # Verify CompanyUserRole was created
        company_user_role = CompanyUserRole.find_by(user: employee_user, company: company)
        expect(company_user_role).to be_present
        expect(company_user_role.role.name).to eq('employee')
      end
    end
  end

  private

  def generate_jwt_token_for(user)
    payload = { user_id: user.id, exp: 1.hour.from_now.to_i }
    JWT.encode(payload, Rails.application.secrets.secret_key_base)
  end
end