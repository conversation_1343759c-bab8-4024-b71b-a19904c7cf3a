# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Session Authentication', type: :request do
  let(:user) { create(:user, email: '<EMAIL>', password: 'password123') }
  let(:company) { create(:company, name: 'Test Company') }
  let(:role) { create(:role, name: 'employee') }
  
  before do
    create(:company_user_role, user: user, company: company, role: role, is_primary: true)
  end

  describe 'POST /users/sign_in' do
    context 'with valid credentials' do
      it 'creates a session and redirects to root' do
        post user_session_path, params: {
          user: {
            email: user.email,
            password: 'password123'
          }
        }
        
        expect(response).to redirect_to(root_path)
        follow_redirect!
        expect(response.body).to include('Signed in successfully')
      end

      it 'sets session cookies' do
        post user_session_path, params: {
          user: {
            email: user.email,
            password: 'password123'
          }
        }
        
        expect(response.cookies['_attendifyapp_session']).not_to be_nil
      end

      it 'tracks login event in AuthHealthCheck' do
        expect(AuthHealthCheck).to receive(:log_auth_event).with(
          'login',
          success: true,
          duration_ms: anything
        )
        
        post user_session_path, params: {
          user: {
            email: user.email,
            password: 'password123'
          }
        }
      end
    end

    context 'with invalid credentials' do
      it 'does not create a session' do
        post user_session_path, params: {
          user: {
            email: user.email,
            password: 'wrongpassword'
          }
        }
        
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.body).to include('Invalid Email or password')
      end

      it 'tracks failed login in AuthHealthCheck' do
        expect(AuthHealthCheck).to receive(:log_auth_event).with(
          'login',
          success: false,
          duration_ms: anything,
          error: anything
        )
        
        post user_session_path, params: {
          user: {
            email: user.email,
            password: 'wrongpassword'
          }
        }
      end
    end

    context 'with remember me' do
      it 'sets remember token cookie' do
        post user_session_path, params: {
          user: {
            email: user.email,
            password: 'password123',
            remember_me: '1'
          }
        }
        
        expect(response.cookies['remember_user_token']).not_to be_nil
      end
    end
  end

  describe 'DELETE /users/sign_out' do
    before do
      sign_in user
    end

    it 'destroys the session' do
      delete destroy_user_session_path
      
      expect(response).to redirect_to(root_path)
      follow_redirect!
      expect(response.body).to include('Signed out successfully')
    end

    it 'clears session data' do
      # Set some session data
      get companies_path
      expect(session[:tenant_id]).to eq(company.id)
      
      delete destroy_user_session_path
      
      expect(session[:tenant_id]).to be_nil
      expect(session['warden.user.user.key']).to be_nil
    end
  end

  describe 'Session persistence' do
    before do
      sign_in user
    end

    it 'maintains session across requests' do
      get companies_path
      expect(response).to have_http_status(:success)
      
      get contracts_path
      expect(response).to have_http_status(:success)
      
      get daily_logs_path
      expect(response).to have_http_status(:success)
    end

    it 'maintains tenant context across requests' do
      # Set tenant
      session[:tenant_id] = company.id
      
      get daily_logs_path
      expect(ActsAsTenant.current_tenant).to eq(company)
      
      get events_path
      expect(ActsAsTenant.current_tenant).to eq(company)
    end
  end

  describe 'Protected routes' do
    context 'when not authenticated' do
      it 'redirects to login page' do
        get companies_path
        expect(response).to redirect_to(new_user_session_path)
        
        get contracts_path
        expect(response).to redirect_to(new_user_session_path)
        
        get daily_logs_path
        expect(response).to redirect_to(new_user_session_path)
      end

      it 'stores location for redirect after login' do
        get contracts_path
        expect(session['user_return_to']).to eq(contracts_path)
      end
    end

    context 'when authenticated' do
      before do
        sign_in user
      end

      it 'allows access to protected routes' do
        get companies_path
        expect(response).to have_http_status(:success)
        
        get contracts_path
        expect(response).to have_http_status(:success)
      end
    end
  end

  describe 'CSRF protection' do
    it 'includes CSRF token in session' do
      get new_user_session_path
      expect(session[:_csrf_token]).not_to be_nil
    end

    it 'rejects requests with invalid CSRF token' do
      # Disable CSRF protection for this test
      allow_any_instance_of(ActionController::Base).to receive(:protect_against_forgery?).and_return(true)
      allow_any_instance_of(ActionController::Base).to receive(:verified_request?).and_return(false)
      
      post user_session_path, params: {
        user: {
          email: user.email,
          password: 'password123'
        }
      }
      
      expect(response).to have_http_status(:unprocessable_entity)
    end
  end

  describe 'Session timeout' do
    before do
      sign_in user
    end

    it 'expires session after timeout period' do
      # Simulate session timeout by manipulating session timestamp
      # This is implementation-specific and may need adjustment
      get companies_path
      expect(response).to have_http_status(:success)
      
      # Fast-forward time to simulate timeout
      travel_to 25.hours.from_now do
        get companies_path
        expect(response).to redirect_to(new_user_session_path)
      end
    end
  end

  describe 'Concurrent sessions' do
    it 'allows multiple sessions for the same user' do
      # First session
      post user_session_path, params: {
        user: {
          email: user.email,
          password: 'password123'
        }
      }
      first_session_cookie = response.cookies['_attendifyapp_session']
      
      # Second session (simulating different browser)
      reset! # Reset the session
      post user_session_path, params: {
        user: {
          email: user.email,
          password: 'password123'
        }
      }
      second_session_cookie = response.cookies['_attendifyapp_session']
      
      # Both sessions should be different but valid
      expect(first_session_cookie).not_to eq(second_session_cookie)
    end
  end

  describe 'API endpoints with session auth' do
    before do
      sign_in user
    end

    it 'accepts session authentication for API endpoints' do
      get api_v1_users_path, headers: { 'Accept' => 'application/json' }
      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')
    end

    it 'returns 401 for unauthenticated API requests' do
      sign_out user
      
      get api_v1_users_path, headers: { 'Accept' => 'application/json' }
      expect(response).to have_http_status(:unauthorized)
    end
  end
end