# ABOUTME: Tests for PWA static assets accessibility and content
# ABOUTME: Validates manifest.j<PERSON>, service worker, icons, and offline page

require 'rails_helper'

RSpec.describe 'PWA Assets', type: :request do
  describe 'Manifest.json' do
    it 'serves manifest.json with correct content-type' do
      get '/manifest.json'
      
      expect(response).to have_http_status(:ok)
      expect(response.content_type).to include('application/json')
    end

    it 'contains required PWA manifest fields' do
      get '/manifest.json'
      manifest = JSON.parse(response.body)
      
      expect(manifest).to include(
        'name' => 'Týmbox - Docházkový systém',
        'short_name' => 'Týmbox',
        'start_url' => '/',
        'display' => 'standalone',
        'background_color' => '#ffffff',
        'theme_color' => '#3b82f6'
      )
      
      expect(manifest['icons']).to be_an(Array)
      expect(manifest['icons'].length).to be >= 2
      
      # Check icon structure
      icon_192 = manifest['icons'].find { |icon| icon['sizes'] == '192x192' }
      expect(icon_192).to include(
        'src' => '/icons/icon-192x192.png',
        'type' => 'image/png',
        'purpose' => 'any maskable'
      )
    end
  end

  describe 'Service Worker' do
    it 'serves service worker with correct content-type' do
      get '/sw.js'
      
      expect(response).to have_http_status(:ok)
      expect(response.content_type).to include('javascript')
    end

    it 'contains expected service worker functionality' do
      get '/sw.js'
      
      expect(response.body).to include('install')
      expect(response.body).to include('activate') 
      expect(response.body).to include('fetch')
      expect(response.body).to include('CACHE_NAME')
      expect(response.body).to include('handleApiRequest')
      expect(response.body).to include('isValidJWT')
    end
  end

  describe 'PWA Icons' do
    it 'serves 192x192 icon' do
      get '/icons/icon-192x192.png'
      
      expect(response).to have_http_status(:ok)
      expect(response.content_type).to include('image/png')
    end

    it 'serves 512x512 icon' do
      get '/icons/icon-512x512.png'
      
      expect(response).to have_http_status(:ok)
      expect(response.content_type).to include('image/png')
    end

    it 'serves screenshot placeholders' do
      get '/icons/screenshot-desktop.png'
      expect(response).to have_http_status(:ok)
      expect(response.content_type).to include('image/png')

      get '/icons/screenshot-mobile.png'
      expect(response).to have_http_status(:ok)
      expect(response.content_type).to include('image/png')
    end
  end

  describe 'Offline Page' do
    it 'serves offline.html' do
      get '/offline.html'
      
      expect(response).to have_http_status(:ok)
      expect(response.content_type).to include('text/html')
    end

    it 'contains offline functionality' do
      get '/offline.html'
      
      body = response.body.force_encoding('UTF-8')
      expect(body).to include('Jste offline')
      expect(body).to include('Týmbox funguje i offline')
      expect(body).to include('Zkusit znovu')
      expect(body).to include('checkOnline')
    end
  end

  describe 'Cache Headers' do
    it 'sets appropriate cache headers for static assets' do
      get '/icons/icon-192x192.png'
      
      # In production, these should have cache headers
      # For development, we just ensure they're accessible
      expect(response).to have_http_status(:ok)
    end

    it 'sets appropriate headers for manifest.json' do
      get '/manifest.json'
      
      expect(response).to have_http_status(:ok)
      # Manifest should not be cached too aggressively
    end
  end
end