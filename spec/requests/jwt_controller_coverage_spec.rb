# spec/requests/jwt_controller_coverage_spec.rb
# Chunk 49: Verify JWT authentication works across all controllers

require 'rails_helper'

RSpec.describe 'JWT Controller Coverage', type: :request do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let(:admin_role) { create(:role, :admin) }
  let(:jwt_token) do
    post '/api/v1/auth/jwt_login', params: {
      email: user.email,
      password: user.password
    }
    JSON.parse(response.body)['access_token']
  end
  
  before do
    create(:company_user_role, user: user, company: company, role: admin_role, is_primary: true)
    ActsAsTenant.current_tenant = company
  end

  describe 'API Controllers' do
    context 'UsersController' do
      it 'accepts JWT authentication' do
        get '/api/v1/users/current', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['id']).to eq(user.id)
      end
    end

    context 'CompaniesController' do
      it 'accepts JWT for company operations' do
        # List companies
        get '/api/v1/companies', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        
        expect(response).to have_http_status(:ok)
        companies = JSON.parse(response.body)
        expect(companies).to be_an(Array)
        expect(companies.map { |c| c['id'] }).to include(company.id)
      end

      it 'accepts JWT for company switching' do
        second_company = create(:company)
        create(:company_user_role, user: user, company: second_company, role: admin_role)
        
        post '/api/v1/companies/switch_company',
          params: { company_id: second_company.id },
          headers: { 'Authorization' => "Bearer #{jwt_token}" }
        
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['access_token']).to be_present
      end
    end

    context 'DailyLogsController' do
      it 'accepts JWT for CRUD operations' do
        # Create
        post '/api/v1/daily_logs',
          params: {
            daily_log: {
              log_date: Date.current,
              start_time: '09:00',
              end_time: '17:00',
              notes: 'Test log'
            }
          },
          headers: { 'Authorization' => "Bearer #{jwt_token}" }
        
        expect(response).to have_http_status(:created)
        log_id = JSON.parse(response.body)['id']
        
        # Read
        get "/api/v1/daily_logs/#{log_id}", headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        
        expect(response).to have_http_status(:ok)
        
        # Update
        patch "/api/v1/daily_logs/#{log_id}",
          params: { daily_log: { notes: 'Updated notes' } },
          headers: { 'Authorization' => "Bearer #{jwt_token}" }
        
        expect(response).to have_http_status(:ok)
        
        # Delete
        delete "/api/v1/daily_logs/#{log_id}", headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        
        expect(response).to have_http_status(:no_content)
      end

      it 'enforces tenant isolation with JWT' do
        # Create log in primary company
        post '/api/v1/daily_logs',
          params: {
            daily_log: {
              log_date: Date.current,
              start_time: '09:00',
              end_time: '17:00'
            }
          },
          headers: { 'Authorization' => "Bearer #{jwt_token}" }
        
        log_id = JSON.parse(response.body)['id']
        
        # Switch to different company
        other_company = create(:company)
        create(:company_user_role, user: user, company: other_company, role: admin_role)
        
        post '/api/v1/companies/switch_company',
          params: { company_id: other_company.id },
          headers: { 'Authorization' => "Bearer #{jwt_token}" }
        
        new_token = JSON.parse(response.body)['access_token']
        
        # Try to access original company's log (should fail)
        get "/api/v1/daily_logs/#{log_id}", headers: {
          'Authorization' => "Bearer #{new_token}"
        }
        
        expect(response).to have_http_status(:not_found)
      end
    end

    context 'EventsController' do
      it 'accepts JWT for event management' do
        # Create event
        post '/api/v1/events',
          params: {
            event: {
              title: 'Team Meeting',
              event_type: 'meeting',
              event_date: Date.tomorrow,
              start_time: '14:00',
              end_time: '15:00'
            }
          },
          headers: { 'Authorization' => "Bearer #{jwt_token}" }
        
        expect(response.status).to be_in([200, 201])
        
        # List events
        get '/api/v1/events', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        
        expect(response).to have_http_status(:ok)
      end
    end

    context 'WorksController' do
      let(:booking_link) { create(:booking_link, company: company, employee: user) }
      let(:booking) { create(:booking, booking_link: booking_link) }
      
      it 'accepts JWT for work operations' do
        # Create work
        post '/api/v1/works',
          params: {
            work: {
              name: 'Test Work',
              scheduled_start_date: Date.tomorrow,
              booking_id: booking.id
            }
          },
          headers: { 'Authorization' => "Bearer #{jwt_token}" }
        
        expect(response.status).to be_in([200, 201])
      end
    end

    context 'NotificationsController' do
      it 'accepts JWT for notification operations' do
        # Create a notification
        notification = create(:notification, user: user)
        
        # List notifications
        get '/api/v1/notifications', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        
        expect(response).to have_http_status(:ok)
        
        # Mark as read
        patch "/api/v1/notifications/#{notification.id}/mark_as_read",
          headers: { 'Authorization' => "Bearer #{jwt_token}" }
        
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'Rails Controllers (if still using JWT)' do
    # These tests verify that Rails controllers also work with JWT
    # In the current dual-auth setup, they might fall back to session
    
    context 'when JWT-only mode is enabled' do
      before do
        # This would need to check if JWT-only mode is enabled
        # skip unless jwt_only_mode_enabled?
      end
      
      it 'requires JWT for Rails controller access' do
        # Example: CompaniesController (Rails version)
        get '/companies', headers: {
          'Authorization' => "Bearer #{jwt_token}",
          'Accept' => 'application/json'
        }
        
        # In dual-auth mode, might get redirect (302)
        # In JWT-only mode, should get 401 without valid JWT
        expect(response.status).to be_in([200, 302, 401])
      end
    end
  end

  describe 'Error Responses' do
    it 'returns consistent error format across controllers' do
      # Test with invalid token
      invalid_token = 'invalid.jwt.token'
      
      # Test different controllers
      controllers_and_paths = [
        ['/api/v1/users/current', :get],
        ['/api/v1/daily_logs', :post],
        ['/api/v1/events', :get],
        ['/api/v1/companies', :get]
      ]
      
      controllers_and_paths.each do |path, method|
        send(method, path, headers: { 'Authorization' => "Bearer #{invalid_token}" })
        
        expect(response).to have_http_status(:unauthorized)
        error_response = JSON.parse(response.body)
        expect(error_response).to have_key('error')
      end
    end
  end

  describe 'Performance Considerations' do
    it 'handles high-frequency API calls efficiently' do
      start_time = Time.current
      
      # Make multiple rapid API calls
      10.times do
        get '/api/v1/users/current', headers: {
          'Authorization' => "Bearer #{jwt_token}"
        }
        expect(response).to have_http_status(:ok)
      end
      
      elapsed_time = Time.current - start_time
      
      # All 10 requests should complete in reasonable time
      expect(elapsed_time).to be < 2.seconds
    end
  end
end