require 'rails_helper'

RSpec.describe "API Dual Authentication", type: :request do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let!(:company_user_role) { create(:company_user_role, user: user, company: company, is_primary: true) }
  
  # JWT setup
  let(:valid_payload) { { user_id: user.id, email: user.email, company_id: company.id } }
  let(:valid_jwt) { JwtService.encode(valid_payload) }
  let(:expired_jwt) { JwtService.encode(valid_payload, 1.second.ago) }
  let(:invalid_jwt) { "invalid.jwt.token" }
  
  # Test endpoint - using a real API endpoint
  let(:test_endpoint) { "/api/v1/user" }
  
  before do
    # Mock User#jwt_payload to include company_id
    allow_any_instance_of(User).to receive(:jwt_payload).and_return(valid_payload)
  end
  
  describe "Authentication precedence" do
    context "with both JWT and session authentication present" do
      before do
        # Sign in user to create a session
        sign_in user
      end
      
      it "prefers JWT authentication over session and logs JWT usage" do
        # Expect ApiController to log JWT authentication and not session authentication
        expect(Rails.logger).to receive(:info).with("API authenticated via JWT for user: #{user.id}").once
        expect(Rails.logger).not_to receive(:info).with(/API authenticated via session for user/)
        
        # Provide JWT in headers
        get test_endpoint, headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:success)
      end
    end
  end
  
  describe "JWT authentication" do
    context "with valid JWT token" do
      before do
        # Ensure ActsAsTenant.current_tenant will be set correctly if ApplicationController relies on user.primary_company
        # This setup is implicitly tested by checking the response from /api/v1/user
        get test_endpoint, headers: { 'Authorization' => "Bearer #{valid_jwt}" }
      end
      
      it "authenticates successfully" do
        expect(response).to have_http_status(:success)
      end

      it "returns user data" do
        json_response = JSON.parse(response.body)
        expect(json_response['email']).to eq(user.email)
        # Company info is now available via JWT auth (added in Chunk 16)
      end
    end
    
    context "with expired JWT token" do
      before do
        get test_endpoint, headers: { 'Authorization' => "Bearer #{expired_jwt}" }
      end
      
      it "returns unauthorized" do
        expect(response).to have_http_status(:unauthorized)
      end
      
      it "returns authentication error message" do
        expect(JSON.parse(response.body)).to include('error' => 'Authentication required')
      end
    end
    
    context "with invalid JWT token" do
      before do
        get test_endpoint, headers: { 'Authorization' => "Bearer #{invalid_jwt}" }
      end
      
      it "returns unauthorized" do
        expect(response).to have_http_status(:unauthorized)
      end
    end
    
    context "with revoked JWT token" do
      let(:token_with_jti) { JwtService.encode(valid_payload.merge(jti: 'test-revoked-jti')) }
      
      before do
        # Revoke the token
        decoded = JwtService.decode(token_with_jti)
        JwtRevocationStrategy.new.revoke_jwt(decoded, user)
        
        get test_endpoint, headers: { 'Authorization' => "Bearer #{token_with_jti}" }
      end
      
      it "returns unauthorized" do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
  
  describe "Session authentication (fallback)" do
    context "with valid session" do
      before do
        sign_in user
        get test_endpoint
      end
      
      it "authenticates successfully" do
        expect(response).to have_http_status(:success)
      end
      
      it "returns user data" do
        json_response = JSON.parse(response.body)
        expect(json_response['email']).to eq(user.email)
        # Company info is now available via JWT auth (added in Chunk 16)
      end
    end
    
    context "without session" do
      before do
        get test_endpoint
      end
      
      it "returns unauthorized" do
        expect(response).to have_http_status(:unauthorized)
      end
      
      it "returns authentication error message" do
        expect(JSON.parse(response.body)).to include('error' => 'Authentication required')
      end
    end
  end
  
  describe "No authentication" do
    context "without JWT or session" do
      before do
        get test_endpoint
      end
      
      it "returns unauthorized" do
        expect(response).to have_http_status(:unauthorized)
      end
      
      it "returns authentication required error" do
        expect(JSON.parse(response.body)).to include('error' => 'Authentication required')
      end
    end
  end
  
  describe "CSRF protection" do
    it "is disabled for API endpoints" do
      post "/api/v1/auth/login", params: { email: '<EMAIL>', password: 'wrong' }
      # Should not get CSRF error (422), but might get auth error (401/403)
      expect(response).not_to have_http_status(422)
    end
  end
  
  describe "current_user method" do
    context "with JWT authentication" do
      it "returns the JWT authenticated user" do
        # This would need to be tested within a controller context
        # Here we're just verifying the endpoint works
        get test_endpoint, headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        expect(response).to have_http_status(:success)
      end
    end
    
    context "with session authentication" do
      it "returns the session authenticated user" do
        sign_in user
        get test_endpoint
        expect(response).to have_http_status(:success)
      end
    end
  end
  
  describe "Logging and metrics" do
    context "with JWT authentication" do
      it "logs JWT authentication event" do
        # Expect both JWT auth event and API request event
        expect(AuthHealthCheck).to receive(:log_auth_event)
          .with('jwt_authentication', success: true).once
        expect(AuthHealthCheck).to receive(:log_auth_event)
          .with('api_request', hash_including(success: true)).once
        
        get test_endpoint, headers: { 'Authorization' => "Bearer #{valid_jwt}" }
      end
    end
    
    context "with API request logging" do
      it "logs successful API requests" do
        sign_in user
        expect(AuthHealthCheck).to receive(:log_auth_event)
          .with('api_request', hash_including(success: true))
        
        get test_endpoint
      end
      
      it "logs failed API requests" do
        expect(AuthHealthCheck).to receive(:log_auth_event)
          .with('api_request', hash_including(success: false))
        
        get test_endpoint
      end
    end
  end
  
  describe "Error handling" do
    context "when JWT authentication raises an error" do
      before do
        allow_any_instance_of(Api::V1::ApiController).to receive(:decode_and_validate_token)
          .and_raise(StandardError.new("JWT error"))
      end
      
      it "falls back to session authentication" do
        sign_in user
        get test_endpoint, headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:success)
      end
    end
  end
  
  describe "Tenant context handling" do
    context "with JWT authentication" do
      it "sets tenant context from company_id in JWT payload" do
        # The UsersController depends on @company being set via set_tenant_company
        get test_endpoint, headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        # JWT auth now sets tenant context from company_id in payload
        expect(json_response['company']).not_to be_nil
        expect(json_response['company']['id']).to eq(company.id)
        expect(json_response['company']['name']).to eq(company.name)
      end
      
      it "handles JWT without company_id gracefully" do
        # Create a JWT without company_id
        payload_without_company = { user_id: user.id, email: user.email }
        jwt_without_company = JwtService.encode(payload_without_company)
        
        get test_endpoint, headers: { 'Authorization' => "Bearer #{jwt_without_company}" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        # No company context set when JWT doesn't include company_id
        expect(json_response['company']).to be_nil
      end
      
      it "validates user has access to company in JWT" do
        # Create another company that user doesn't belong to
        other_company = create(:company, name: 'Other Company')
        
        # Create JWT with unauthorized company_id
        unauthorized_payload = { user_id: user.id, email: user.email, company_id: other_company.id }
        unauthorized_jwt = JwtService.encode(unauthorized_payload)
        
        get test_endpoint, headers: { 'Authorization' => "Bearer #{unauthorized_jwt}" }
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        # User is authenticated but tenant context not set for unauthorized company
        expect(json_response['email']).to eq(user.email)
        expect(json_response['company']).to be_nil
      end
    end
    
    context "with session authentication" do
      before do
        sign_in user
      end
      
      it "maintains tenant context from session" do
        # First request sets tenant_id in session
        get test_endpoint
        
        json_response = JSON.parse(response.body)
        expect(json_response['company']['id']).to eq(company.id)
        
        # Note: Can't test cross-request tenant persistence with events endpoint
        # as it may have different authorization requirements
      end
    end
  end
  
  describe "API endpoint variations" do
    # Note: Not all controllers under api/v1 inherit from ApiController yet
    # This is a limitation of the current implementation
    context "controllers inheriting from ApiController" do
      it "support dual authentication" do
        # The /api/v1/user endpoint uses ApiController
        
        # Test without auth - should fail
        get test_endpoint
        expect(response).to have_http_status(:unauthorized)
        
        # Test with session - should work
        sign_in user
        get test_endpoint
        expect(response).to have_http_status(:success)
        
        # Test with JWT after signing out - should work
        sign_out user
        get test_endpoint, headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        expect(response).to have_http_status(:success)
      end
    end
    
    context "controllers NOT inheriting from ApiController" do
      it "still use session-based auth only (e.g., EmployeesController)" do
        # EmployeesController inherits from ApplicationController, not ApiController
        employees_endpoint = "/api/v1/employees"
        
        # Without auth - redirects to login (302) instead of returning 401
        get employees_endpoint
        expect(response).to have_http_status(:found) # 302 redirect
        
        # With session - works
        sign_in user
        get employees_endpoint
        expect(response).to have_http_status(:success)
        
        # JWT doesn't work for these controllers yet
        sign_out user
        get employees_endpoint, headers: { 'Authorization' => "Bearer #{valid_jwt}" }
        expect(response).to have_http_status(:found) # Still redirects, JWT ignored
      end
    end
  end
end