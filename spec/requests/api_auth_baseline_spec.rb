require 'rails_helper'

RSpec.describe 'API Authentication Baseline', type: :request do
  # These tests establish baseline behavior for API authentication
  # Used to ensure JWT implementation maintains compatibility
  
  let(:user) { create(:user, email: '<EMAIL>', password: 'password123') }
  let(:company) { create(:company) }
  let!(:role) { create(:role, name: 'employee', company: company) }
  let!(:company_user_role) { create(:company_user_role, user: user, company: company, role: role) }
  
  describe 'POST /api/v1/auth/login' do
    context 'with valid credentials' do
      it 'returns success and user data' do
        post api_v1_auth_login_path, params: {
          email: user.email,
          password: 'password123'
        }
        
        expect(response).to have_http_status(:success)
        
        json = JSON.parse(response.body)
        expect(json['success']).to be true
        expect(json['user']['email']).to eq(user.email)
        expect(json['user']['role']).to eq('employee')
      end
      
      it 'creates a session' do
        expect {
          post api_v1_auth_login_path, params: {
            email: user.email,
            password: 'password123'
          }
        }.to change { session['warden.user.user.key'] }.from(nil)
      end
      
      it 'logs the authentication event' do
        expect(AuthHealthCheck).to receive(:log_auth_event).with(
          'login',
          hash_including(success: true)
        )
        
        post api_v1_auth_login_path, params: {
          email: user.email,
          password: 'password123'
        }
      end
    end
    
    context 'with invalid credentials' do
      it 'returns unauthorized for wrong password' do
        post api_v1_auth_login_path, params: {
          email: user.email,
          password: 'wrongpassword'
        }
        
        expect(response).to have_http_status(:unauthorized)
        
        json = JSON.parse(response.body)
        expect(json['error']).to eq('Invalid email or password')
      end
      
      it 'returns unauthorized for non-existent user' do
        post api_v1_auth_login_path, params: {
          email: '<EMAIL>',
          password: 'password123'
        }
        
        expect(response).to have_http_status(:unauthorized)
      end
      
      it 'logs failed authentication attempts' do
        expect(AuthHealthCheck).to receive(:log_auth_event).with(
          'login',
          hash_including(success: false, error: 'Invalid credentials')
        )
        
        post api_v1_auth_login_path, params: {
          email: user.email,
          password: 'wrongpassword'
        }
      end
    end
    
    context 'with missing parameters' do
      it 'handles missing email' do
        post api_v1_auth_login_path, params: {
          password: 'password123'
        }
        
        expect(response).to have_http_status(:unauthorized)
      end
      
      it 'handles missing password' do
        post api_v1_auth_login_path, params: {
          email: user.email
        }
        
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
  
  describe 'DELETE /api/v1/auth/logout' do
    context 'when authenticated' do
      before { sign_in user }
      
      it 'returns success' do
        delete api_v1_auth_logout_path
        
        expect(response).to have_http_status(:success)
        json = JSON.parse(response.body)
        expect(json['success']).to be true
      end
      
      it 'destroys the session' do
        delete api_v1_auth_logout_path
        
        expect(session['warden.user.user.key']).to be_nil
      end
    end
    
    context 'when not authenticated' do
      it 'still returns success' do
        delete api_v1_auth_logout_path
        
        expect(response).to have_http_status(:success)
      end
    end
  end
  
  describe 'GET /api/v1/user' do
    context 'when authenticated' do
      before { sign_in user }
      
      it 'returns current user data' do
        get api_v1_user_path
        
        expect(response).to have_http_status(:success)
        
        json = JSON.parse(response.body)
        expect(json['email']).to eq(user.email)
        expect(json['id']).to eq(user.id)
      end
      
      it 'includes role information' do
        get api_v1_user_path
        
        json = JSON.parse(response.body)
        expect(json['roles']).to include(
          hash_including(
            'company_id' => company.id,
            'role' => 'employee'
          )
        )
      end
    end
    
    context 'when not authenticated' do
      it 'returns unauthorized' do
        get api_v1_user_path
        
        expect(response).to have_http_status(:unauthorized)
        
        json = JSON.parse(response.body)
        expect(json['error']).to eq('You need to sign in or sign up before continuing.')
      end
    end
  end
  
  describe 'Protected API endpoints' do
    let(:endpoints) do
      [
        { method: :get, path: api_v1_companies_path },
        { method: :get, path: api_v1_contracts_path },
        { method: :get, path: api_v1_daily_logs_path }
      ]
    end
    
    context 'when authenticated' do
      before { sign_in user }
      
      it 'allows access to protected endpoints' do
        endpoints.each do |endpoint|
          send(endpoint[:method], endpoint[:path])
          expect(response).not_to have_http_status(:unauthorized)
        end
      end
      
      it 'logs API requests' do
        expect(AuthHealthCheck).to receive(:log_auth_event).at_least(:once)
        
        get api_v1_companies_path
      end
    end
    
    context 'when not authenticated' do
      it 'denies access to protected endpoints' do
        endpoints.each do |endpoint|
          send(endpoint[:method], endpoint[:path])
          expect(response).to have_http_status(:unauthorized)
        end
      end
    end
  end
  
  describe 'CSRF protection' do
    it 'is disabled for API endpoints' do
      # API endpoints should work without CSRF token
      post api_v1_auth_login_path, params: {
        email: user.email,
        password: 'password123'
      }
      
      expect(response).not_to have_http_status(:unprocessable_entity)
    end
  end
  
  describe 'Multi-tenancy in API' do
    let(:company2) { create(:company) }
    let!(:role2) { create(:role, name: 'admin', company: company2) }
    let!(:company_user_role2) { create(:company_user_role, user: user, company: company2, role: role2) }
    
    before { sign_in user }
    
    it 'uses current tenant for API requests' do
      # Set tenant
      allow(Company).to receive(:current).and_return(company)
      
      get api_v1_contracts_path
      
      # Should only return contracts for current company
      expect(response).to have_http_status(:success)
    end
    
    it 'allows switching tenants via header' do
      # This tests if tenant switching is implemented
      # Adjust based on your implementation
    end
  end
  
  describe 'Response times' do
    it 'completes login request quickly' do
      start_time = Time.current
      
      post api_v1_auth_login_path, params: {
        email: user.email,
        password: 'password123'
      }
      
      duration = (Time.current - start_time) * 1000 # Convert to ms
      
      expect(duration).to be < 200 # Should complete within 200ms
    end
    
    it 'completes authenticated requests quickly' do
      sign_in user
      
      start_time = Time.current
      get api_v1_user_path
      duration = (Time.current - start_time) * 1000
      
      expect(duration).to be < 100 # Should complete within 100ms
    end
  end
end