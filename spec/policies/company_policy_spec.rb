# ABOUTME: Tests for CompanyPolicy focusing on free tier role assignment restrictions
require 'rails_helper'

RSpec.describe CompanyPolicy, type: :policy do
  let!(:owner_role) { Role.find_or_create_by!(name: 'owner') }
  let!(:employee_role) { Role.find_or_create_by!(name: 'employee') }
  let!(:admin_role) { Role.find_or_create_by!(name: 'admin') }
  let!(:supervisor_role) { Role.find_or_create_by!(name: 'supervisor') }
  
  let(:company) { create(:company) }
  let(:owner) { create(:user) }

  describe '#can_assign_role?' do
    subject { described_class.new(user: owner, record: company).can_assign_role?(role_name) }

    context 'employee role' do
      let(:role_name) { 'employee' }
      it { is_expected.to be true }
    end

    context 'free tier company' do
      context 'owner role' do
        let(:role_name) { 'owner' }

        context 'with no existing owners' do
          it { is_expected.to be true }
        end

        context 'with existing owner' do
          before do
            ActsAsTenant.with_tenant(company) do
              create(:company_user_role, user: owner, company: company, role: owner_role)
            end
          end

          it { is_expected.to be false }
        end
      end

      context 'admin role' do
        let(:role_name) { 'admin' }
        it { is_expected.to be false }
      end

      context 'supervisor role' do
        let(:role_name) { 'supervisor' }
        it { is_expected.to be false }
      end
    end

    context 'plus plan company' do
      let!(:plus_plan) { create(:plan, :plus) }
      let!(:subscription) { create(:subscription, company: company, plan: plus_plan) }

      context 'owner role' do
        let(:role_name) { 'owner' }
        it { is_expected.to be true }
      end

      context 'admin role' do
        let(:role_name) { 'admin' }
        it { is_expected.to be true }
      end

      context 'supervisor role' do
        let(:role_name) { 'supervisor' }
        it { is_expected.to be true }
      end
    end

    context 'premium plan company' do
      let!(:premium_plan) { create(:plan, :premium) }
      let!(:subscription) { create(:subscription, company: company, plan: premium_plan) }

      context 'owner role' do
        let(:role_name) { 'owner' }
        it { is_expected.to be true }
      end

      context 'employee role' do
        let(:role_name) { 'employee' }
        it { is_expected.to be true }
      end

      context 'admin role' do
        let(:role_name) { 'admin' }
        it { is_expected.to be true }
      end

      context 'supervisor role' do
        let(:role_name) { 'supervisor' }
        it { is_expected.to be true }
      end
    end
  end
end