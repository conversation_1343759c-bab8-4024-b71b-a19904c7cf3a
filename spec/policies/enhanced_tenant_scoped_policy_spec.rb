# spec/policies/enhanced_tenant_scoped_policy_spec.rb
# Test enhanced tenant scoped policy with JWT validation from Chunk 29

require 'rails_helper'

RSpec.describe TenantScopedPolicy, type: :policy do
  let(:user) { create(:user) }
  let(:company1) { create(:company) }
  let(:company2) { create(:company) }
  let(:daily_log) { create(:daily_log, company: company1) }

  describe 'Enhanced JWT tenant validation' do
    before do
      # Set up tenant context
      ActsAsTenant.current_tenant = company1
      
      # Mock AuthHealthCheck for security logging
      allow(AuthHealthCheck).to receive(:log_security_event)
    end

    context 'with JWT authentication context' do
      let(:policy_context) { 
        { 
          user: user, 
          current_jwt_user: user, 
          jwt_company_id: company1.id 
        } 
      }
      let(:policy) { described_class.new(daily_log, **policy_context) }

      describe '#jwt_tenant_authorized?' do
        it 'allows access when JWT company_id matches current tenant' do
          expect(policy.jwt_tenant_authorized?).to be true
        end

        it 'denies access when JWT company_id differs from current tenant' do
          policy_with_wrong_company = described_class.new(
            daily_log, 
            user: user, 
            current_jwt_user: user, 
            jwt_company_id: company2.id
          )
          
          expect(policy_with_wrong_company.jwt_tenant_authorized?).to be false
        end

        it 'logs security event for tenant mismatch' do
          ActsAsTenant.current_tenant = nil
          
          expect(AuthHealthCheck).to receive(:log_security_event).with(
            'jwt_tenant_mismatch',
            hash_including(
              severity: 'high',
              user_id: user.id,
              jwt_company_id: company1.id,
              current_tenant_id: nil,
              reason: 'no_current_tenant_with_jwt_company'
            )
          )
          
          policy.jwt_tenant_authorized?
        end
      end

      describe '#enhanced_tenant_authorized?' do
        it 'passes when both basic and JWT tenant checks pass' do
          expect(policy.enhanced_tenant_authorized?).to be true
        end

        it 'fails when basic tenant check fails' do
          # Create a record from different company
          other_daily_log = create(:daily_log, company: company2)
          policy_with_wrong_record = described_class.new(
            other_daily_log, 
            **policy_context
          )
          
          expect(policy_with_wrong_record.enhanced_tenant_authorized?).to be false
        end

        it 'fails when JWT tenant check fails' do
          policy_with_wrong_jwt = described_class.new(
            daily_log,
            user: user,
            current_jwt_user: user,
            jwt_company_id: company2.id
          )
          
          expect(policy_with_wrong_jwt.enhanced_tenant_authorized?).to be false
        end

        it 'logs security event when authorization fails' do
          # Force a failure by using wrong company in JWT
          policy_with_wrong_jwt = described_class.new(
            daily_log,
            user: user,
            current_jwt_user: user,
            jwt_company_id: company2.id
          )
          
          expect(AuthHealthCheck).to receive(:log_security_event).with(
            'tenant_authorization_failed',
            hash_including(
              severity: 'medium',
              user_id: user.id,
              record_class: 'DailyLog',
              basic_tenant_check: true,
              jwt_tenant_check: false,
              current_tenant_id: company1.id,
              jwt_company_id: company2.id
            )
          )
          
          policy_with_wrong_jwt.enhanced_tenant_authorized?
        end
      end
    end

    context 'without JWT authentication context (session auth)' do
      let(:policy_context) { { user: user } }
      let(:policy) { described_class.new(daily_log, **policy_context) }

      describe '#jwt_tenant_authorized?' do
        it 'allows access when no JWT context is present' do
          expect(policy.jwt_tenant_authorized?).to be true
        end
      end

      describe '#enhanced_tenant_authorized?' do
        it 'falls back to basic tenant validation for session auth' do
          expect(policy.enhanced_tenant_authorized?).to be true
        end

        it 'still enforces basic tenant validation' do
          other_daily_log = create(:daily_log, company: company2)
          policy_with_wrong_record = described_class.new(
            other_daily_log, 
            **policy_context
          )
          
          expect(policy_with_wrong_record.enhanced_tenant_authorized?).to be false
        end
      end
    end
  end

  describe 'Integration with pre_check' do
    let(:policy_context) { 
      { 
        user: user, 
        current_jwt_user: user, 
        jwt_company_id: company1.id 
      } 
    }
    let(:policy) { described_class.new(daily_log, **policy_context) }

    before do
      ActsAsTenant.current_tenant = company1
    end

    it 'uses enhanced tenant validation in pre_check' do
      # This should pass as it uses the enhanced validation
      expect { policy.apply(:show?) }.not_to raise_error
    end

    it 'raises ActionPolicy::Unauthorized for tenant violations' do
      # Use wrong company in JWT
      policy_with_violation = described_class.new(
        daily_log,
        user: user,
        current_jwt_user: user,
        jwt_company_id: company2.id
      )
      
      expect { policy_with_violation.apply(:show?) }.to raise_error(ActionPolicy::Unauthorized)
    end
  end

  describe 'JWT authentication detection' do
    let(:policy) { described_class.new(daily_log, user: user) }

    it 'detects JWT authentication correctly' do
      policy_with_jwt = described_class.new(
        daily_log,
        user: user,
        current_jwt_user: user
      )
      
      expect(policy_with_jwt.send(:jwt_authenticated?)).to be true
      expect(policy.send(:jwt_authenticated?)).to be false
    end
  end
end