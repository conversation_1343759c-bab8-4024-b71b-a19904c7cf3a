# spec/policies/application_policy_chunk_29_spec.rb
# Test for Chunk 29 enhancements to ApplicationPolicy

require 'rails_helper'

RSpec.describe ApplicationPolicy, type: :policy do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let(:record) { double('record') }

  describe '#tenant_matches_jwt?' do
    context 'when JWT authenticated' do
      context 'when JWT has no company_id but tenant is set' do
        let(:policy_context) { 
          { 
            user: user, 
            current_jwt_user: user,
            jwt_company_id: nil
          } 
        }
        let(:policy) { described_class.new(record, **policy_context) }

        before do
          ActsAsTenant.current_tenant = company
        end

        it 'returns false (mismatch)' do
          expect(policy.send(:tenant_matches_jwt?)).to be false
        end
      end

      context 'when JWT has company_id and it matches current tenant' do
        let(:policy_context) { 
          { 
            user: user, 
            current_jwt_user: user,
            jwt_company_id: company.id
          } 
        }
        let(:policy) { described_class.new(record, **policy_context) }

        before do
          ActsAsTenant.current_tenant = company
        end

        it 'returns true' do
          expect(policy.send(:tenant_matches_jwt?)).to be true
        end
      end

      context 'when JWT has company_id and it does not match current tenant' do
        let(:other_company) { create(:company) }
        let(:policy_context) { 
          { 
            user: user, 
            current_jwt_user: user,
            jwt_company_id: other_company.id
          } 
        }
        let(:policy) { described_class.new(record, **policy_context) }

        before do
          ActsAsTenant.current_tenant = company
        end

        it 'returns false' do
          expect(policy.send(:tenant_matches_jwt?)).to be false
        end
      end
    end

    context 'when not JWT authenticated (session auth)' do
      let(:policy_context) { { user: user } }
      let(:policy) { described_class.new(record, **policy_context) }

      before do
        ActsAsTenant.current_tenant = company
      end

      it 'returns true (no JWT to validate)' do
        expect(policy.send(:tenant_matches_jwt?)).to be true
      end
    end
  end
end