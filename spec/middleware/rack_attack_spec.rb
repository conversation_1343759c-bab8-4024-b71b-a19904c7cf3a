# spec/middleware/rack_attack_spec.rb
# Test rate limiting implementation from Chunk 29

require 'rails_helper'

RSpec.describe 'Rack::Attack Rate Limiting (Chunk 29)', type: :request do
  describe 'JWT authentication endpoint rate limiting' do
    let(:valid_credentials) { { email: '<EMAIL>', password: 'password123' } }
    let(:ip_address) { '***********' }

    before do
      # Clear any existing rate limit data
      Rack::Attack.cache.store.clear
      
      # Stub the request IP
      allow_any_instance_of(ActionDispatch::Request).to receive(:ip).and_return(ip_address)
    end

    describe 'JWT login rate limiting' do
      it 'allows requests under the limit' do
        # Should allow 5 requests per 20 seconds
        4.times do
          post '/api/v1/auth/jwt_login', params: valid_credentials
          expect(response).not_to have_http_status(429)
        end
      end

      it 'blocks requests over the limit' do
        # Make 5 requests (the limit)
        5.times do
          post '/api/v1/auth/jwt_login', params: valid_credentials
        end

        # The 6th request should be rate limited
        post '/api/v1/auth/jwt_login', params: valid_credentials
        expect(response).to have_http_status(429)
        
        response_body = JSON.parse(response.body)
        expect(response_body['error']).to eq('Too Many Requests')
        expect(response.headers['Retry-After']).to be_present
      end
    end

    describe 'JWT refresh token rate limiting' do
      it 'allows requests under the limit' do
        # Should allow 10 requests per 1 minute
        9.times do
          post '/api/v1/auth/refresh_token'
          expect(response).not_to have_http_status(429)
        end
      end

      it 'blocks requests over the limit' do
        # Make 10 requests (the limit)
        10.times do
          post '/api/v1/auth/refresh_token'
        end

        # The 11th request should be rate limited
        post '/api/v1/auth/refresh_token'
        expect(response).to have_http_status(429)
        
        response_body = JSON.parse(response.body)
        expect(response_body['error']).to eq('Too Many Requests')
      end
    end

    describe 'General API rate limiting' do
      let(:user) { create(:user) }
      let(:company) { create(:company) }
      let!(:company_user_role) { create(:company_user_role, user: user, company: company) }

      before do
        sign_in user
      end

      it 'allows normal API usage' do
        # Should allow 300 requests per 5 minutes for general API
        50.times do
          get '/api/v1/employees'
          expect(response).not_to have_http_status(429)
        end
      end
    end

    describe 'Authentication failure rate limiting' do
      it 'throttles repeated authentication failures' do
        # Should allow 3 failed attempts per 5 minutes
        invalid_credentials = { email: '<EMAIL>', password: 'wrongpassword' }
        
        # Simulate authentication failures by setting the rack.attack.auth_failure flag
        3.times do |i|
          post '/api/v1/auth/jwt_login', params: invalid_credentials, env: { 'rack.attack.auth_failure' => true }
          # First 3 attempts should not be rate limited
          expect(response).not_to have_http_status(429)
        end

        # The 4th failed attempt should be rate limited
        post '/api/v1/auth/jwt_login', params: invalid_credentials, env: { 'rack.attack.auth_failure' => true }
        expect(response).to have_http_status(429)
        
        response_body = JSON.parse(response.body)
        expect(response_body['error']).to eq('Too Many Requests')
      end

      it 'throttles failed sign_in attempts for session auth' do
        # Test that paths containing 'sign_in' are also throttled
        3.times do
          post '/users/sign_in', params: { user: { email: '<EMAIL>', password: 'wrong' } }, 
               env: { 'rack.attack.auth_failure' => true }
          expect(response).not_to have_http_status(429)
        end

        # 4th attempt should be throttled
        post '/users/sign_in', params: { user: { email: '<EMAIL>', password: 'wrong' } },
             env: { 'rack.attack.auth_failure' => true }
        expect(response).to have_http_status(429)
      end
    end
  end

  describe 'Security logging' do
    let(:ip_address) { '********' }

    before do
      Rack::Attack.cache.store.clear
      allow_any_instance_of(ActionDispatch::Request).to receive(:ip).and_return(ip_address)
      
      # Mock AuthHealthCheck to verify security events are logged
      allow(AuthHealthCheck).to receive(:log_security_event)
    end

    it 'logs rate limit violations to security system' do
      # Trigger rate limiting
      6.times { post '/api/v1/auth/jwt_login' }

      expect(AuthHealthCheck).to have_received(:log_security_event).with(
        'rate_limit_violation',
        hash_including(
          ip: ip_address,
          path: '/api/v1/auth/jwt_login',
          method: 'POST'
        )
      )
    end
  end

  describe 'Development environment safelist' do
    before do
      allow(Rails.env).to receive(:development?).and_return(true)
      Rack::Attack.cache.store.clear
    end

    it 'allows localhost traffic in development' do
      allow_any_instance_of(ActionDispatch::Request).to receive(:ip).and_return('127.0.0.1')
      
      # Should not be rate limited even with many requests
      10.times do
        post '/api/v1/auth/jwt_login'
        expect(response).not_to have_http_status(429)
      end
    end
  end
end